package com.bilibili.collage.api.dto;

import java.io.Serializable;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 三连首页：平台最新动态
 */
@Data
@Accessors(chain = true)
public class SanlianLatestUpdateDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer type; // 1-产品说明 , 2-投放指南, 3-商业咨询

    private String title;

    private String articleId;

    private String jumpUrl; // 跳转链接，type = 3 商业咨询时，该字段不为空

    private Integer sanlianShowOrderNum;

    private Long ctime;
}