package com.bilibili.collage.api.service;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.collage.api.dto.CollageOperationLogDto;
import com.bilibili.collage.api.dto.NewCollageOperationLogDto;
import com.bilibili.collage.api.dto.QueryCollageOperationLogDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/09/10
 **/
public interface ICollageLogService {
    void insertLog(NewCollageOperationLogDto newCollageOperationLogDto);

    PageResult<CollageOperationLogDto> queryOperationLogs(QueryCollageOperationLogDto queryCollageOperationLogDto);

    void batchInsert(List<NewCollageOperationLogDto> collageOperationLogDtos);
}
