package com.bilibili.collage.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @file: CollageEnterpriseDto
 * @author: gaoming
 * @date: 2020/12/10
 * @version: 1.0
 * @description:
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CollageEnterpriseDto implements Serializable {
    private static final long serialVersionUID = -600500967434812875L;

    /**
     * 名称
     */
    private String name;

    /**
     * mid
     */
    private Long mid;
}
