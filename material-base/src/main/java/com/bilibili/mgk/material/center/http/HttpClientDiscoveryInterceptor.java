package com.bilibili.mgk.material.center.http;

import com.google.common.base.Strings;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;
import okhttp3.HttpUrl;
import okhttp3.Interceptor;
import okhttp3.Response;
import pleiades.component.env.Environment;
import pleiades.component.env.EnvironmentKeys;
import pleiades.venus.naming.client.resolve.NamingResolver;

/**
 * plan2
 *
 * <AUTHOR>
 * @desc
 * @date 2024/7/26
 */
public class HttpClientDiscoveryInterceptor implements Interceptor {

    private final AtomicLong addressIndex = new AtomicLong(0L);
    private volatile List<InetSocketAddress> addresses;

    public HttpClientDiscoveryInterceptor(String zone, NamingResolver namingResolver) {
        if (Strings.isNullOrEmpty(zone)) {
            zone = Environment.of(EnvironmentKeys.ZONE).get();
        }
        HttpClientNameResolver.registerRefreshListener(zone, namingResolver, addr -> {
            this.addresses = addr;
        });
        this.addresses = HttpClientNameResolver.addresses(zone, namingResolver);
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        if (addresses == null || addresses.isEmpty()) {
            throw new IOException("Empty address");
        }
        HttpUrl originURL = chain.request().url();
        InetSocketAddress target = getAddress();
        if (target != null) {
            originURL = originURL.newBuilder()
                    .scheme("http")
                    .host(target.getHostString())
                    .port(target.getPort())
                    .build();
        }
        return chain.proceed(chain.request().newBuilder().url(originURL).build());
    }

    private InetSocketAddress getAddress() {
        long sequenceNumber = addressIndex.getAndIncrement();
        return this.addresses.get((int) (sequenceNumber % addresses.size()));
    }
}
