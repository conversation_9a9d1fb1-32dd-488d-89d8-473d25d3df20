package com.bilibili.mgk.material.center.util;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import javax.annotation.Nullable;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @desc
 * @date 2022/6/7
 */
public class IteratorHelper {

    public static <ID, E> Iterator<List<E>> buildIterator(
            BiFunction<ID, Integer, List<E>> valueGetter, Function<E, ID> idGetter, Integer pageSize,
            ID firstIdPresent) {

        return buildIterator(valueGetter, idGetter, pageSize, firstIdPresent, null);
    }

    public static <ID, E> Iterator<List<E>> buildIterator(
            BiFunction<ID, Integer, List<E>> valueGetter, Function<E, ID> idGetter, Integer pageSize) {

        return buildIterator(valueGetter, idGetter, pageSize, null, null);
    }


    /**
     * @param valueGetter
     * @param idGetter
     * @param pageSize
     * @param postHandler 对迭代后得到数据的后置处理操作
     * @param <ID>
     * @param <E>
     * @return
     */
    public static <ID, E> Iterator<List<E>> buildIterator(
            BiFunction<ID, Integer, List<E>> valueGetter,
            Function<E, ID> idGetter,
            Integer pageSize,
            ID firstIdPresent,
            @Nullable Consumer<List<E>> postHandler) {

        return new Iterator<List<E>>() {

            private final AtomicBoolean hasNext = new AtomicBoolean(true);

            private final AtomicReference<ID> currentIndex = new AtomicReference<>();


            @Override
            public boolean hasNext() {
                return hasNext.get();
            }

            @Override
            public List<E> next() {

                if (!hasNext.get()) {
                    throw new NoSuchElementException();
                }

                List<E> allAccount = valueGetter.apply(
                        Optional.ofNullable(currentIndex.get()).orElse(firstIdPresent),
                        pageSize);

                if (CollectionUtils.isEmpty(allAccount) || allAccount.size() < pageSize) {
                    hasNext.set(false);
                } else {
                    // last index getter,
                    currentIndex.set(idGetter.apply(allAccount.get(allAccount.size() - 1)));
                }

                if (CollectionUtils.isEmpty(allAccount)) {
                    return new ArrayList<>();
                }

                if (postHandler != null) {
                    postHandler.accept(allAccount);
                }

                return allAccount;
            }
        };
    }
}
