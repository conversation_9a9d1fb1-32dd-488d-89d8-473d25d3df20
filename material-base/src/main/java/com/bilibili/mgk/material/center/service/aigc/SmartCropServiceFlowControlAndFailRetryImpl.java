package com.bilibili.mgk.material.center.service.aigc;

import com.bilibili.mgk.material.center.config.SmartCropConfig;
import com.bilibili.mgk.material.center.repository.base_ad.LauMaterialSmartCropImgMapper;
import com.bilibili.mgk.material.center.service.aigc.RedissonRateLimiter.LockAcquireFailedException;
import com.bilibili.mgk.material.center.service.aigc.model.CropOriginImage;
import com.bilibili.mgk.material.center.service.aigc.model.CropResultImage;
import com.bilibili.mgk.material.center.service.aigc.model.ImageRatioType;
import com.bilibili.mgk.material.center.service.aigc.model.SmartCropFailureRetryContext;
import io.vavr.Tuple;
import io.vavr.control.Try;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 这一层主要负责 0. 已生成过的裁切图不再重复生成 1. 控制流量 2. 失败重试（进入重试队列）
 *
 * <AUTHOR>
 * @desc
 * @date 2024/9/12
 */
@Slf4j
@Primary
@Service
public class SmartCropServiceFlowControlAndFailRetryImpl implements SmartCropService {

    @Resource
    private SmartCropServiceSimpleHttpImpl delegate;

    @Resource
    private LauMaterialSmartCropImgMapper lauMaterialSmartCropImgMapper;


    @Resource
    private SmartCropFailureRetryQueue retryQueue;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private SmartCropConfig smartCropConfig;

    private RedissonRateLimiter redissonRateLimiter;

    @PostConstruct
    public void init() {

        redissonRateLimiter = new RedissonRateLimiter(redissonClient,
                smartCropConfig.getMaxConcurrency(),
                smartCropConfig.getReserveConcurrency4realtime(),
                smartCropConfig.getConcurrencyLockKey());

    }

    @Override
    public List<CropResultImage> smartCrop(CropOriginImage originImg) throws Exception {
        return this.smartCropWithRetry(originImg);
    }

    public List<CropResultImage> smartCropWithRetry(CropOriginImage originImage) throws Exception {

        return this.smartCropWithRetry(originImage, new SmartCropFailureRetryContext()
                .setCompleteAttempts(0)
                .setHighPriority(true)
                .setMaxAttempts(smartCropConfig.getCropRetryAttempts())
                .setOriginImage(originImage)
        );

    }

    public List<CropResultImage> smartCropWithRetry(
            CropOriginImage originImage,
            SmartCropFailureRetryContext context) throws Exception {

        return Try.of(() -> {

            originImage.validate();

            RLock lock = redissonClient.getLock(
                    String.format(smartCropConfig.getMd5LockKey(), originImage.getOriginImgMd5()));

            if (!tryLock(lock)) {
                log.warn("Fail to acquire MD5 lock for origin image, originImg={}", originImage);
                throw new LockAcquireFailedException("获取图文md5锁失败,可能有同一图片正在执行裁剪,可稍后重试");
            }
            try {
                return this.doSmartCrop(originImage, context.isHighPriority());
            } finally {
                releaseLock(lock);
            }

        }).onFailure(t -> {

            retryQueue.offer(context, originImage, t);

        }).get();

    }


    /**
     * 这一层会抛出失败，可以理解
     *
     * @param originImg
     * @return
     */
    public List<CropResultImage> doSmartCrop(CropOriginImage originImg, boolean highPriority) throws Exception {

        return Try.of(() -> redissonRateLimiter.handleRequest(() -> {

            List<CropResultImage> existed;
            if (originImg.getUsePersistCache()) {
                existed =
                        Optional.ofNullable(
                                        lauMaterialSmartCropImgMapper.selectByOriginImgMd5(originImg.getOriginImgMd5()))
                                .orElse(new ArrayList<>());

                if (existed.size() == 3 && ImageRatioType.containsAll(ImageRatioType.smartCropExpectedRatioTypes(),
                        existed.stream().map(CropResultImage::getCropImgRatioType).collect(Collectors.toSet()))) {

                    return Tuple.of(existed, existed);
                }
            } else {
                existed = new ArrayList<>();
            }

            List<CropResultImage> results = delegate.smartCrop(originImg);

            return Tuple.of(existed, results);

        }, highPriority)).map(existedAndResults -> {

            if (originImg.getIsForceOverwriteCroppedImg()) {

                this.doRefreshCroppedImageData(originImg, existedAndResults._2);
                return existedAndResults._2;
            }

            List<CropResultImage> existed = existedAndResults._1;
            List<CropResultImage> results = existedAndResults._2;

            if (existed == results) {
                // 1.无需更新
                return existed;
            }

            if (existed.isEmpty()) {

                if (originImg.getUsePersistCache()) {
                    // 2.插入全量

                    Try.run(() -> lauMaterialSmartCropImgMapper.insertBatch(results)).onFailure(t -> {
                        log.warn("Fail to insert batch crop result images, try insert one by one, originImg={}",
                                originImg, t);
                        results.stream().forEach(result -> {
                            Try.run(() ->
                                    lauMaterialSmartCropImgMapper.insertSelective(result));
                        });

                        List<CropResultImage> resultsAfterStepByStepInsert = Optional.ofNullable(
                                lauMaterialSmartCropImgMapper.selectByOriginImgMd5(
                                        originImg.getOriginImgMd5())).orElse(new ArrayList<>());
                        if (resultsAfterStepByStepInsert.size() != 3) {
                            log.error("Fail to persist crop result after save one by one, results={}", results, t);
                            throw new RuntimeException("持久化智能裁切结果失败[100001]", t);
                        }
                    });
                } else {
                    // 没有用cache ，存量必为空
                    // 那么要考虑是insert 还是forceUpdate
                    log.warn(
                            "目前这个分支不应该走到，后续这个分支可以用与强制更新持久化数据，但是注意要注意，更新迁先判断是否存在，是更新还是插入");
                }
            } else {

                // 插入缺失的部分
                Set<Integer> existedRatioType = existed.stream().map(CropResultImage::getCropImgRatioType)
                        .collect(Collectors.toSet());

                Try.run(() -> {
                    results.stream().forEach(result -> {
                        if (existedRatioType.contains(result.getCropImgRatioType())) {
                            return;
                        }
                        lauMaterialSmartCropImgMapper.insertSelective(result);
                    });
                }).getOrElseThrow(t -> {
                    log.error("Fail to insert crop result images, originImg={}", originImg, t);
                    throw new RuntimeException("持久化智能裁切结果失败[100002],", t);
                });

            }

            return existedAndResults._2;

        }).get();
    }


    private void doRefreshCroppedImageData(CropOriginImage originImg, List<CropResultImage> reCroppedImages) {

        reCroppedImages.stream().forEach(img -> {

            Try.of(() -> {
                return lauMaterialSmartCropImgMapper.updateByPrimaryKeySelective(img
                        .setId(originImg.getRatioTypeToCroppedImagePrimaryKey().get(img.getCropImgRatioType()))
                );
            }).onFailure(t -> {
                log.error("Fail to  overwrite cropped result image, img={}", img, t);
            });
        });


    }


    private boolean tryLock(RLock lock) {

        try {
            return lock.tryLock(smartCropConfig.getMd5LockWait(), smartCropConfig.getMd5LockLease(),
                    TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            throw new LockAcquireFailedException("获取锁失败, interrupted", e);
        }
    }


    private void releaseLock(RLock lock) {
        Try.run(lock::unlock).onFailure(t -> {
            log.warn("Fail to unlock crop lock", t);
        });
    }

}
