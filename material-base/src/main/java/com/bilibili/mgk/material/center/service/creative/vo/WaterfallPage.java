package com.bilibili.mgk.material.center.service.creative.vo;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.function.Function;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/10/25
 */
@Data
@Accessors(chain = true)
public class WaterfallPage<T> implements SnakeCaseBody {


    @ApiModelProperty("是否还有更多，当为false时，到达底部，不能继续下滑；当为true时，使用next_cursor作为下一页的cursor参数填入，进行下滑；")
    private Boolean hasMore;


    @ApiModelProperty("下一页的游标，当has_more为true时，使用该游标进行下一页的请求")
    private String nextCursor;


    @ApiModelProperty("数据, 需要注意的时候由于可能存在拼页和后过滤， 所以每页数据量可能不同，极端情况下甚至为空，但是前端请以has_more为准进行翻页")
    private List<T> data;


    public <R> WaterfallPage<R> map(Function<List<T>, List<R>> mapping) {

        return new WaterfallPage<R>()
                .setHasMore(hasMore)
                .setNextCursor(nextCursor)
                .setData(mapping.apply(data));
    }

}
