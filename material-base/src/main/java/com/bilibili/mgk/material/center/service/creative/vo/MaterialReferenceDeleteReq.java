package com.bilibili.mgk.material.center.service.creative.vo;

import io.swagger.annotations.ApiModelProperty;
import io.vavr.Tuple;
import io.vavr.Tuple3;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/5/15
 */
@Data
@Accessors(chain = true)
public class MaterialReferenceDeleteReq implements SnakeCaseBody {

    @ApiModelProperty("素材id")
    private String materialId;

    @ApiModelProperty("素材id类型")
    private String materialIdType;

    @ApiModelProperty("素材uk,通常为md5")
    private String materialUk;

    @ApiModelProperty("素材引用uk，通常为业务primary-id")
    private String referenceUk;


    public void validate() {

        if (StringUtils.isEmpty(referenceUk)) {
            throw new IllegalArgumentException("referenceUk必须提供");
        }

        if (StringUtils.isEmpty(materialId)) {

            if (StringUtils.isEmpty(materialIdType) || StringUtils.isEmpty(materialUk)) {
                throw new IllegalArgumentException("materialId 或者 materialTypeUk组合 必须提供");
            }

        }

    }


    public Tuple3<String, String, String> selectMaterialIdOrTypeUk() {
        if (StringUtils.isNotEmpty(materialId)) {
            return Tuple.of(materialId, null, null);
        } else {
            return Tuple.of(null, materialIdType, materialUk);
        }
    }

}
