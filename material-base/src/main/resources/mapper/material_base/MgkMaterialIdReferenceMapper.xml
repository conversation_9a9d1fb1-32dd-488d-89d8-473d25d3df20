<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.material.center.repository.base.MgkMaterialIdReferenceMapper">

  <resultMap id="BaseResultMap" type="com.bilibili.mgk.material.center.service.creative.model.MaterialIdReference">
    <id property="id" column="id" jdbcType="BIGINT"/>
    <result property="materialId" column="material_id" jdbcType="VARCHAR"/>
    <result property="materialIdType" column="material_id_type" jdbcType="VARCHAR"/>
    <result property="materialUk" column="material_uk" jdbcType="VARCHAR"/>
    <result property="referenceUk" column="reference_uk" jdbcType="VARCHAR"/>
    <result property="name" column="name" jdbcType="VARCHAR"/>
    <result property="searchWord" column="search_word" jdbcType="VARCHAR"/>
    <result property="content" column="content" jdbcType="VARCHAR"/>
    <result property="accountId" column="account_id" jdbcType="VARCHAR"/>
    <result property="source" column="source" jdbcType="VARCHAR"/>
    <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
    <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
    <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    <result property="version" column="version" jdbcType="INTEGER"/>
  </resultMap>

  <sql id="Base_Column_List">
    id,material_id,material_id_type,
        material_uk,reference_uk,`name`, search_word,content,
        account_id,`source`,ctime,
        mtime,deleted,version
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from mgk_material_id_reference
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectAllByAccountIdEqAndMaterialIdInAndNameLike"
    resultType="java.util.ArrayList" resultMap="BaseResultMap">

    select
    <include refid="Base_Column_List"/>
    from mgk_material_id_reference
    <where>


      <if test="accountIds != null and accountIds.size > 0 ">
        and account_id in
        <foreach close=")" collection="accountIds" item="listItem" open="(" separator=",">
          #{listItem}
        </foreach>
      </if>

      <if test="materialIds != null and materialIds.size > 0 ">
        and material_id in
        <foreach close=")" collection="materialIds" item="listItem" open="(" separator=",">
          #{listItem}
        </foreach>
      </if>

      <if test="name != null and name != '' ">
        and `name` like CONCAT('%',#{name},'%')
      </if>

      <if test="searchWord != null and searchWord != '' ">
        and search_word like CONCAT('%',#{searchWord},'%')
      </if>
      <if test="deleted != null ">
        and deleted = #{deleted}
      </if>
    </where>
    order by id desc
  </select>
  <select id="selectByMaterialIdAndReferenceUk"
    resultType="com.bilibili.mgk.material.center.service.creative.model.MaterialIdReference" resultMap="BaseResultMap">

    select
    <include refid="Base_Column_List"/>
    from mgk_material_id_reference
    <where>
      <if test="materialId != null and materialId != '' ">
        and material_id = #{materialId}
      </if>

      <if test="materialIdType != null and materialIdType != '' ">
        and material_id_type = #{materialIdType}
      </if>

      <if test="materialUk != null and materialUk != '' ">
        and material_uk = #{materialUk}
      </if>

      <if test="referenceUk != null and referenceUk != '' ">
        and reference_uk = #{referenceUk}
      </if>

      <if test="deleted != null ">
        and deleted = #{deleted}
      </if>
    </where>

  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from mgk_material_id_reference
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id"
    parameterType="com.bilibili.mgk.material.center.service.creative.model.MaterialIdReference" useGeneratedKeys="true">
    insert into mgk_material_id_reference
    ( id, material_id, material_id_type
    , material_uk, reference_uk, `name`, search_word, content
    , account_id, `source`, ctime
    , mtime, deleted, version)
    values ( #{id,jdbcType=BIGINT}, #{materialId,jdbcType=VARCHAR}, #{materialIdType,jdbcType=VARCHAR}
           , #{materialUk,jdbcType=VARCHAR}, #{referenceUk,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
             #{searchWord,jdbcType=VARCHAR}
           , #{content,jdbcType=VARCHAR}
           , #{accountId,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}
           , #{mtime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=TINYINT}, #{version,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id"
    parameterType="com.bilibili.mgk.material.center.service.creative.model.MaterialIdReference" useGeneratedKeys="true">
    insert into mgk_material_id_reference
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">id,</if>
      <if test="materialId != null">material_id,</if>
      <if test="materialIdType != null">material_id_type,</if>
      <if test="materialUk != null">material_uk,</if>
      <if test="referenceUk != null">reference_uk,</if>
      <if test="name != null">name,</if>
      <if test="searchWord != null">search_word,</if>
      <if test="content != null">content,</if>
      <if test="accountId != null">account_id,</if>
      <if test="source != null">source,</if>
      <if test="ctime != null">ctime,</if>
      <if test="mtime != null">mtime,</if>
      <if test="deleted != null">deleted,</if>
      <if test="version != null">version,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">#{id,jdbcType=BIGINT},</if>
      <if test="materialId != null">#{materialId,jdbcType=VARCHAR},</if>
      <if test="materialIdType != null">#{materialIdType,jdbcType=VARCHAR},</if>
      <if test="materialUk != null">#{materialUk,jdbcType=VARCHAR},</if>
      <if test="referenceUk != null">#{referenceUk,jdbcType=VARCHAR},</if>
      <if test="name != null">#{name,jdbcType=VARCHAR},</if>
      <if test="searchWord != null">#{searchWord,jdbcType=VARCHAR},</if>
      <if test="content != null">#{content,jdbcType=VARCHAR},</if>
      <if test="accountId != null">#{accountId,jdbcType=VARCHAR},</if>
      <if test="source != null">#{source,jdbcType=VARCHAR},</if>
      <if test="ctime != null">#{ctime,jdbcType=TIMESTAMP},</if>
      <if test="mtime != null">#{mtime,jdbcType=TIMESTAMP},</if>
      <if test="deleted != null">#{deleted,jdbcType=TINYINT},</if>
      <if test="version != null">#{version,jdbcType=INTEGER},</if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="com.bilibili.mgk.material.center.service.creative.model.MaterialIdReference">
    update mgk_material_id_reference
    <set>
      <if test="materialId != null">
        material_id = #{materialId,jdbcType=VARCHAR},
      </if>
      <if test="materialIdType != null">
        material_id_type = #{materialIdType,jdbcType=VARCHAR},
      </if>
      <if test="materialUk != null">
        material_uk = #{materialUk,jdbcType=VARCHAR},
      </if>
      <if test="referenceUk != null">
        reference_uk = #{referenceUk,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="searchWord != null">
        search_word = #{searchWord,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey"
    parameterType="com.bilibili.mgk.material.center.service.creative.model.MaterialIdReference">
    update mgk_material_id_reference
    set material_id      = #{materialId,jdbcType=VARCHAR},
        material_id_type = #{materialIdType,jdbcType=VARCHAR},
        material_uk      = #{materialUk,jdbcType=VARCHAR},
        reference_uk     = #{referenceUk,jdbcType=VARCHAR},
        `name`           = #{name,jdbcType=VARCHAR},
        search_word      = #{searchWord,jdbcType=VARCHAR},
        content          = #{content,jdbcType=VARCHAR},
        account_id       = #{accountId,jdbcType=VARCHAR},
        `source`         = #{source,jdbcType=VARCHAR},
        ctime            = #{ctime,jdbcType=TIMESTAMP},
        mtime            = #{mtime,jdbcType=TIMESTAMP},
        deleted          = #{deleted,jdbcType=TINYINT},
        version          = #{version,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
