package com.bilibili.mgk.platform.job;

import com.bilibili.mgk.platform.api.video_library.service.IVideoStatusService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@JobHandler("RefreshVideoStatusJob")
public class RefreshVideoStatusJob extends IJobHandler {
    private final IVideoStatusService videoStatusService;

    public RefreshVideoStatusJob(IVideoStatusService videoStatusService) {
        this.videoStatusService = videoStatusService;
    }

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("开始刷新视频状态");
        try {
            videoStatusService.refreshVideoStatus();
        } catch (Exception e) {
            log.error("刷新视频状态失败");
        }
        log.info("结束刷新视频状态");
        return SUCCESS;
    }
}
