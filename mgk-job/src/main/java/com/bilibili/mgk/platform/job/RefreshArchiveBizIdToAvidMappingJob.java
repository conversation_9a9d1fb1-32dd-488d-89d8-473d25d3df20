package com.bilibili.mgk.platform.job;

import com.bilibili.mgk.platform.api.archive.service.IMgkCmArchiveService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @file: RefreshArchiveBizIdToAvidMappingJob
 * @author: gaoming
 * @date: 2021/12/20
 * @version: 1.0
 * @description:
 **/

@Slf4j
@Component
@JobHandler("RefreshArchiveBizIdToAvidMappingJob")
public class RefreshArchiveBizIdToAvidMappingJob extends IJobHandler {


    @Autowired
    private IMgkCmArchiveService mgkCmArchiveService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            mgkCmArchiveService.refreshBizIdToAvidMappingInRedis();
        } catch (Exception e) {
            log.info("RefreshArchiveBizIdToAvidMappingJob Failed e {}", e.getMessage());
        }
        log.info("RefreshArchiveBizIdToAvidMappingJob finished");
        return SUCCESS;
    }
}
