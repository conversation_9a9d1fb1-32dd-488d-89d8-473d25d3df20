package com.bilibili.mgk.platform.job;

import com.bilibili.mgk.platform.api.data.service.IMgkWechatPackageDataService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @ClassName RefreshWechatPackageUnReportedData
 * <AUTHOR>
 * @Date 2022/7/14 4:25 下午
 * @Version 1.0
 **/
@Component
@Slf4j
@JobHandler("RefreshWechatPackageReportDataJob")
public class RefreshWechatPackageReportDataJob extends IJobHandler {

    @Value("${mgk.wechat.package.report.data.before.day:3}")
    private Integer beforeDay;

    @Autowired
    private IMgkWechatPackageDataService mgkWechatPackageDataService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            int result = mgkWechatPackageDataService.refreshUnReportedData(beforeDay);
            log.info("RefreshWechatPackageReportDataJob success, result:{}", result);
        } catch (Exception e) {
            log.error("RefreshWechatPackageReportDataJob encounter an exception:{}", e.getMessage());
        }
        log.info("RefreshWechatPackageReportDataJob finish.......");
        return SUCCESS;
    }
}
