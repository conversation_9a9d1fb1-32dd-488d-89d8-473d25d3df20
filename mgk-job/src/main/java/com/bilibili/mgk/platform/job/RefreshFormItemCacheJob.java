package com.bilibili.mgk.platform.job;

import com.bilibili.mgk.platform.api.data.service.IMgkDataService;
import com.bilibili.mgk.platform.api.form.service.IMgkFormService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName RefreshFormItemJob
 * <AUTHOR>
 * @Date 2022/5/4 10:54 下午
 * @Version 1.0
 **/
@Component
@Slf4j
@JobHandler("RefreshFormItemCacheJob")
public class RefreshFormItemCacheJob extends IJobHandler {

    @Autowired
    private IMgkFormService mgkFormService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            mgkFormService.refreshFormItemCache();
        } catch (Exception e) {
            log.info("refreshFormItemCacheJob Failed e {}", e.getMessage());
        }
        log.info("refreshFormItemCacheJob finished");
        return SUCCESS;
    }
}
