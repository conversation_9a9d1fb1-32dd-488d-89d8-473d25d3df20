package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @file: MgkEnvironmentEnum
 * @author: gaoming
 * @date: 2021/04/14
 * @version: 1.0
 * @description:
 **/
@AllArgsConstructor
public enum MgkEnvironmentEnum {
    /**
     * 测试环境
     */
    TEST(1, "测试环境"),
    /**
     * 预发环境
     */
    PRE(2, "预发环境"),
    /**
     * UAT环境
     */
    UAT(3, "UAT环境"),
    /**
     * 线上环境
     */
    PROD(4, "线上环境")

    ;
    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static MgkEnvironmentEnum getByCode(int code) {
        for (MgkEnvironmentEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code MgkEnvironmentEnum " + code);
    }
}
