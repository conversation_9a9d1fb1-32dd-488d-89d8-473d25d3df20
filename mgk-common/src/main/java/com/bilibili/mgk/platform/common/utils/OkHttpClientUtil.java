package com.bilibili.mgk.platform.common.utils;


import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName OkHttpClientUtil.java
 * @Description
 * @createTime 2020-07-09 16:31:00
 */
public class OkHttpClientUtil {
//    public static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    public String postRequest(String url, Map<String,Object> headers,String payLoad){
        Request.Builder builder = new Request.Builder();
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, Object> kv : headers.entrySet()) {
                builder.addHeader(kv.getKey(), String.valueOf(kv.getValue()));
            }
        }

        OkHttpClient client = new OkHttpClient.Builder().build();
        okhttp3.RequestBody body = okhttp3.RequestBody.create(MediaType.parse("application/json"), payLoad);
        Request request = builder.post(body).url(url).build();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String result = response.body() != null ? response.body().string() : null;
                if(result == null||result.isEmpty()){
                    return "";
                }else {
                    return result;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        }
        return "";
    }


}
