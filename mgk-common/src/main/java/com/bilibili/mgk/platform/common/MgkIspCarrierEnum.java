package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @file: MgkIspCarrierEnum
 * @author: gaoming
 * @date: 2021/11/02
 * @version: 1.0
 * @description:
 **/

@AllArgsConstructor
@Getter
public enum MgkIspCarrierEnum {

    /**
     * 移动
     */
    MOBILE(1, "mobile", "移动"),

    /**
     * 联通
     */
    UNICOM(2, "unicom", "联通"),

    /**
     * 电信
     */
    TELECOM(3, "telecom", "电信");
    private Integer code;
    private String value;
    private String desc;

    public static List<String> CARRIERS = Arrays.stream(values()).map(MgkIspCarrierEnum::getValue).collect(Collectors.toList());

    public static MgkIspCarrierEnum getByCode(int code) {
        for (MgkIspCarrierEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code MgkIspCarrierEnum " + code);
    }

}
