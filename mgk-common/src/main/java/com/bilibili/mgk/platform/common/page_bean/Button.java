/** 
* <AUTHOR> 
* @date  2018年5月18日
*/ 

package com.bilibili.mgk.platform.common.page_bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Button implements Serializable {

	private static final long serialVersionUID = -5343800932764644387L;
	
	private String text="";
    private String type="";
    private String jump_url="";
    private List<String> report_urls = Collections.emptyList();
    private String dlsuc_callup_url="";
}
