package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @file: MgkHotAdsOrderByEnum
 * @author: gaoming
 * @date: 2021/01/07
 * @version: 1.0
 * @description:
 **/
@AllArgsConstructor
public enum MgkHotAdsOrderByEnum {

    PV(0, "pv", "曝光"),
    CTR(1, "ctr", "点击"),
    CVR(2, "cvr", "曝光转化数"),
    CTCVR(3, "ctcvr", "点击转化数"),
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String orderBy;
    @Getter
    private final String desc;

    public static MgkHotAdsOrderByEnum getByCode(Integer code) {
        for (MgkHotAdsOrderByEnum bean :
                values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code MgkHotAdsOrderByEnum " + code);
    }
}
