package com.bilibili.mgk.platform.common.page_bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @file: AppletsComponentDataContentFloatingLinkBehaviorFormConfig
 * @author: gaoming
 * @date: 2021/12/06
 * @version: 1.0
 * @description:
 **/

@AllArgsConstructor
@Builder
@NoArgsConstructor
@Data
public class AppletsComponentDataContentFloatingLinkBehaviorFormConfig implements Serializable {

    private static final long serialVersionUID = -4054037743741744470L;
    private Integer useTitle;
    private String title;
    private String successMessage;
}
