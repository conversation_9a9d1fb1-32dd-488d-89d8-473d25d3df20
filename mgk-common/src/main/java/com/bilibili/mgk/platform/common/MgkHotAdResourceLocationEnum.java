package com.bilibili.mgk.platform.common;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * @file: MgkHotAdResourceLocationEnum
 * @author: gaoming
 * @date: 2021/05/21
 * @version: 1.0
 * @description:
 **/
@AllArgsConstructor
@Getter
public enum MgkHotAdResourceLocationEnum {
    /**
     * 信息流
     */
    INFORMATION_FLOW(1, "信息流"),

    /**
     * 播放页相关推荐
     */
    PLAY_PAGE_RELATED_RECOMMEND(2, "播放页相关推荐"),

    /**
     * up互选位
     */
    UP_MUTUAL_SELECTION(3, "up互选位"),

    /**
     * 移动端banner
     */
    MOBILE_BANNER(4, "移动端banner"),

    /**
     * 闪屏
     */
    SPLASH_SCREEN(5, "闪屏"),

    /**
     * 焦点图
     */
    FOCUS_MAP(6, "焦点图"),

    /**
     * 通栏
     */
    COLUMN(7, "通栏"),

    /**
     * 推广视频位
     */
    PROMOTION_VIDEO_LOCATION(8, "推广视频位"),

    /**
     * 右侧焦点图
     */
    RIGHT_FOCUS_MAP(9, "右侧焦点图"),

    /**
     * 播放页横幅
     */
    PLAY_PAGE_BANNER(10, "播放页横幅"),

    /**
     * 弹幕资源位
     */
    BARRAGE_RESOURCE(11, "弹幕资源位"),

    /**
     * 动态信息流
     */
    DYNAMIC_INFORMATION_FLOW(12, "动态信息流"),

    /**
     * 播放页浮层
     */
    PLAY_PAGE_FLOATING_LAYER(13, "播放页浮层"),
    ;

    /**
     * PC 资源位
     */
    public static final List<Integer> PC_LOCATION = Lists.newArrayList(COLUMN.getCode(), PROMOTION_VIDEO_LOCATION.getCode());

    /**
     * 移动资源位
     */
    public static final List<Integer> MOBILE_LOCATION = Lists.newArrayList(INFORMATION_FLOW.getCode(), PLAY_PAGE_RELATED_RECOMMEND.getCode(), UP_MUTUAL_SELECTION.getCode(), DYNAMIC_INFORMATION_FLOW.getCode());

    @Getter
    private Integer code;
    private String desc;

    public static MgkHotAdResourceLocationEnum getByCode(Integer code) {
        for (MgkHotAdResourceLocationEnum bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code MgkHotAdResourceLocationEnum " + code);
    }
}
