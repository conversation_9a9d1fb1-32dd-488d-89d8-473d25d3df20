package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @file: MgkHotVideoOrderByEnum
 * @author: gaoming
 * @date: 2020/11/10
 * @version: 1.0
 * @description:
 **/
@AllArgsConstructor
public enum MgkHotVideoOrderByEnum {
    PLAY_DAILY(0, "play_daily", "新增播放数"),
    REPLY_DAILY(1, "reply_daily", "新增评论数"),
    FAV_DAILY(2, "fav_daily", "新增收藏数"),
    COIN_DAILY(3, "coin_daily", "新增投币数量"),
    DANMU_DAILY(4, "danmu_daily", "新增弹幕数"),
    SHARE_DAILY(5, "share_daily", "新增分享数"),
    LIKES_DAILY(6, "likes_daily", "新增点赞数")
    ;
    @Getter
    private Integer code;

    @Getter
    private String orderBy;

    private String desc;

    public static MgkHotVideoOrderByEnum getByCode(Integer code) {
        for (MgkHotVideoOrderByEnum bean :
                values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code MgkHotVideoOrderByEnum " + code);
    }
}
