package com.bilibili.mgk.platform.common.enums.page;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum BusinessToolTypeEnum {

    PAGE(0, "落地页"),

    CUSTOMER_ACQ(1, "获客链接"),

    ;

    @Getter
    private final Integer code;
    @Getter
    private final String desc;

    public static BusinessToolTypeEnum getByCode(int code) {
        for (BusinessToolTypeEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code BusinessToolTypeEnum " + code);
    }


}
