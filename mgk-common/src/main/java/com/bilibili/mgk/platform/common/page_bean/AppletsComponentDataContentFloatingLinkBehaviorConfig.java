package com.bilibili.mgk.platform.common.page_bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @file: AppletsComponentDataContentFloatingLinkBehaviorConfig
 * @author: gaoming
 * @date: 2021/12/06
 * @version: 1.0
 * @description:
 **/

@AllArgsConstructor
@Builder
@NoArgsConstructor
@Data
public class AppletsComponentDataContentFloatingLinkBehaviorConfig implements Serializable {

    private static final long serialVersionUID = 6474175820102311935L;
    private String formId;

    private AppletsComponentDataContentFloatingLinkBehaviorFormConfig form;

    private AppletsComponentDataContentFloatingLinkBehaviorIosConfig ios;

    private AppletsComponentDataContentFloatingLinkBehaviorAndroidConfig android;
}
