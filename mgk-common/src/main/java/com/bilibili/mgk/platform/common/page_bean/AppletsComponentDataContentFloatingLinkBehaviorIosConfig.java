package com.bilibili.mgk.platform.common.page_bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @file: AppletsComponentDataContentFloatingLinkBehaviorIosConfig
 * @author: gaoming
 * @date: 2021/12/06
 * @version: 1.0
 * @description:
 **/
@AllArgsConstructor
@Builder
@NoArgsConstructor
@Data
public class AppletsComponentDataContentFloatingLinkBehaviorIosConfig implements Serializable {

    private static final long serialVersionUID = 1783855971393436241L;

    private String id;

    private String url;

    private String scheme;
}
