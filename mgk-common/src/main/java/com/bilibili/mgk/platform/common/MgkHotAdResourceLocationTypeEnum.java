package com.bilibili.mgk.platform.common;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

import static com.bilibili.mgk.platform.common.MgkHotAdResourceLocationEnum.*;

/**
 * @file: MgkHotAdResourceLocationTypeEnum
 * @author: gaoming
 * @date: 2021/05/21
 * @version: 1.0
 * @description:
 **/

@AllArgsConstructor
@Getter
public enum MgkHotAdResourceLocationTypeEnum {
    /**
     * pc资源位
     */
    PC_LOCATION(1, Lists.newArrayList(COLUMN.getCode(), PROMOTION_VIDEO_LOCATION.getCode()), "pc资源位"),

    /**
     * 移动资源位
     */
    MOBILE_LOCATION(2, Lists.newArrayList(INFORMATION_FLOW.getCode(), PLAY_PAGE_RELATED_RECOMMEND.getCode(), UP_MUTUAL_SELECTION.getCode(), DYNAMIC_INFORMATION_FLOW.getCode()), "移动资源位");
    @Getter
    private Integer code;
    private List<Integer> value;
    private String desc;

    public static MgkHotAdResourceLocationTypeEnum getByCode(Integer code) {
        for (MgkHotAdResourceLocationTypeEnum bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code MgkHotAdResourceLocationTypeEnum " + code);
    }
}
