package com.bilibili.mgk.platform.common.page_bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @file: LikeDto
 * @author: gaoming
 * @date: 2021/07/01
 * @version: 1.0
 * @description:
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LikeDto implements Serializable {
    private static final long serialVersionUID = 4299084195200161233L;

    /**
     * avid
     */
    private Long avid;

    /**
     * 点赞数
     */
    private Long like_number;

    /**
     * 点踩数
     */
    private Long dislike_number;

    /**
     * 是否点赞
     */
    private Integer has_like;

    /**
     * 是否点踩
     */
    private Integer has_dislike;

}
