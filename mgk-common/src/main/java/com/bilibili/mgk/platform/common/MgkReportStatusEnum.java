package com.bilibili.mgk.platform.common;

import lombok.Getter;
import lombok.NoArgsConstructor;

@NoArgsConstructor
public enum MgkReportStatusEnum {

    UNREPORT(1, "待上报"),
    REPORTED(2, "已上报"),
    NO_NEED_REPORT(3, "无需上报"),
    INVALID_TRACKID(4, "非法的trackId");

    @Getter
    private Integer code;

    @Getter
    private String name;

    MgkReportStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
