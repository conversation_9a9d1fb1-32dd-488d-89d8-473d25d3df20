package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @file: MgkHotPageDatePeriodEnum
 * @author: gaoming
 * @date: 2021/11/11
 * @version: 1.0
 * @description:
 **/
@AllArgsConstructor
public enum MgkHotPageDatePeriodEnum {

    /**
     * 昨天
     */
    LAST_1_DAYS(1, "1d", "昨天"),

    /**
     * 7天
     */
    LAST_7_DAYS(2, "7d", "7天"),

    /**
     * 30天
     */
    LAST_30_DAYS(3, "30d", "30天"),

    ;
    @Getter
    private Integer code;

    @Getter
    private String value;

    @Getter
    private String desc;

    public static MgkHotPageDatePeriodEnum getByCode(Integer code) {
        for (MgkHotPageDatePeriodEnum bean :
                values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code MgkHotPageDatePeriodEnum " + code);
    }
}
