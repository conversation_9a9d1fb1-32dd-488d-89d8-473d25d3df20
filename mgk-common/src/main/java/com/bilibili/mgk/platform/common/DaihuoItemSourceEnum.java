package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 */

@AllArgsConstructor
public enum DaihuoItemSourceEnum {

    TAO_BAO(1, "淘宝"),

    JD(3, "京东"),

    PIN_DUO_DUO(10, "拼多多"),;

    @Getter
    private final Integer code;
    @Getter
    private final String desc;

    public static DaihuoItemSourceEnum getByCode(int code) {
        for (DaihuoItemSourceEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code DaihuoItemSourceEnum " + code);
    }

}
