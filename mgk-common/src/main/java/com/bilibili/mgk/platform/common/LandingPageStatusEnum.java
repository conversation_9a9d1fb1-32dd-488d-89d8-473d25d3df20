package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.EnumSet;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017年6月14日
 * @desc 落地页状态: 1-未发布 2-已发布 3-已下线 4-管理员驳回 5-已删除
 */

@AllArgsConstructor
public enum LandingPageStatusEnum {
    UNPUBLISHED(1, "未发布") {
        @Override
        public boolean isModifiable() {
            return true;
        }

        @Override
        public boolean validateToStatus(LandingPageStatusEnum toStatus) {
            return EnumSet.of(PUBLISHED, DELETED, WAIT_AUDIT, UNPUBLISHED).contains(toStatus);
        }

        @Override
        public LogOperateTypeEnum getLogOperateType() {
            return null;
        }

        @Override
        public MgkOperationType getMgkOperationType() {
            return null;
        }
    },
    PUBLISHED(2, "已发布") {
        @Override
        public boolean isModifiable() {
            return true;
        }

        @Override
        public boolean validateToStatus(LandingPageStatusEnum toStatus) {
            return EnumSet.of(DOWNLINE, DELETED, ADMIN_REJECT, AUDIT_REJECT, PUBLISHED, WAIT_AUDIT)
                    .contains(toStatus);
        }

        @Override
        public LogOperateTypeEnum getLogOperateType() {
            return LogOperateTypeEnum.LANDING_PAGE_PUBLISHED;
        }

        @Override
        public MgkOperationType getMgkOperationType() {
            return MgkOperationType.PUBLISH;
        }
    },
    DOWNLINE(3, "已下线") {
        @Override
        public boolean isModifiable() {
            return true;
        }

        @Override
        public boolean validateToStatus(LandingPageStatusEnum toStatus) {
            return EnumSet.of(DELETED, PUBLISHED, WAIT_AUDIT).contains(toStatus);
        }

        @Override
        public LogOperateTypeEnum getLogOperateType() {
            return LogOperateTypeEnum.LANDING_PAGE_DOWNLINE;
        }

        @Override
        public MgkOperationType getMgkOperationType() {
            return MgkOperationType.DOWNLINE;
        }
    },
    ADMIN_REJECT(4, "管理员驳回") {
        @Override
        public boolean isModifiable() {
            return false;
        }

        @Override
        public boolean validateToStatus(LandingPageStatusEnum toStatus) {
            return false;
        }

        @Override
        public LogOperateTypeEnum getLogOperateType() {
            return LogOperateTypeEnum.LANDING_PAGE_REJECT;
        }

        @Override
        public MgkOperationType getMgkOperationType() {
            return MgkOperationType.ADMIN_REJECT;
        }
    },
    DELETED(5, "已删除") {
        @Override
        public boolean isModifiable() {
            return false;
        }

        @Override
        public boolean validateToStatus(LandingPageStatusEnum toStatus) {
            return false;
        }

        @Override
        public LogOperateTypeEnum getLogOperateType() {
            return LogOperateTypeEnum.LANDING_PAGE_DELETED;
        }

        @Override
        public MgkOperationType getMgkOperationType() {
            return MgkOperationType.DISABLE;
        }
    },
    WAIT_AUDIT(6, "待审核") {
        @Override
        public boolean isModifiable() {
            return true;
        }

        @Override
        public boolean validateToStatus(LandingPageStatusEnum toStatus) {
            return EnumSet.of(PUBLISHED, AUDIT_REJECT, UNPUBLISHED, WAIT_AUDIT, DOWNLINE, DELETED).contains(toStatus);
        }

        @Override
        public LogOperateTypeEnum getLogOperateType() {
            return LogOperateTypeEnum.LANDING_PAGE_AUDIT_COMMIT;
        }

        @Override
        public MgkOperationType getMgkOperationType() {
            return MgkOperationType.SEND_AUDIT;
        }
    },
    AUDIT_REJECT(7, "审核拒绝") {
        @Override
        public boolean isModifiable() {
            return true;
        }

        @Override
        public boolean validateToStatus(LandingPageStatusEnum toStatus) {
            return EnumSet.of(AUDIT_REJECT ,WAIT_AUDIT, DOWNLINE, DELETED, PUBLISHED).contains(toStatus);
        }

        @Override
        public LogOperateTypeEnum getLogOperateType() {
            return LogOperateTypeEnum.LANDING_PAGE_AUDIT_REJECT;
        }

        @Override
        public MgkOperationType getMgkOperationType() {
            return MgkOperationType.AUDIT_REJECT;
        }
    };
    @Getter
    private final Integer code;
    @Getter
    private final String desc;

    public final static List<Integer> CAN_BE_VIEW_STATUS_LIST = Arrays.asList(PUBLISHED.getCode());

    /**
     * 投放端可选择的落地页状态 不是能实际投放的状态
     */
    public final static List<Integer> LAUNCH_AVAILABLE_STATUS_LIST =
            Arrays.asList(PUBLISHED.getCode(), WAIT_AUDIT.getCode());
    public final static List<Integer> NOT_DELETED_STATUS_LIST =
            Arrays.asList(UNPUBLISHED.getCode(), PUBLISHED.getCode(),
            DOWNLINE.getCode(), WAIT_AUDIT.getCode(), AUDIT_REJECT.getCode());

    public static LandingPageStatusEnum getByCode(int code) {
        for (LandingPageStatusEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code LandingPageStatusEnum " + code);
    }

    public abstract boolean isModifiable();
    public abstract boolean validateToStatus(LandingPageStatusEnum toStatus);
    public abstract LogOperateTypeEnum getLogOperateType();
    public abstract MgkOperationType getMgkOperationType();

}
