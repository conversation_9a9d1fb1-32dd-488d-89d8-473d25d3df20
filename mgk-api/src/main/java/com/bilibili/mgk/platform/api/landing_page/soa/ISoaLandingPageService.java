package com.bilibili.mgk.platform.api.landing_page.soa;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Page;
import com.bilibili.mgk.platform.api.landing_page.dto.*;
import com.bilibili.mgk.platform.common.page_bean.MgkLandingPageBean;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/1/17
 **/
public interface ISoaLandingPageService {

    void downline(Operator operator, long pageId);

    List<Long> batchDownlineByMiniGameId(Operator operator, Integer miniGameId);

    void refreshCDN(Operator operator, long pageId);

    void updateMgkPageStatusByAppPackageId(Operator operator, Integer appPackageId);

    Integer getAdVersionControllIdByTemplateStyle(Integer templateStyle);

    Integer getAdVersionControlIdByPageId(Long pageId);

    LandingPageConfigDto getLandingPageConfigDtoByPageId(Long pageId);

    MgkLandingPageBean validatePageIdAndGetLandingPage(Integer jumpType, String promotionPurposeContent);

    @Deprecated
    MgkLandingPageBean validatePageIdAndGetLandingPage(String promotionPurposeContent, Integer isPc);

    MgkLandingPageBean validatePageIdAndGetLandingPage(String promotionPurposeContent);

    List<Long> getPublishedLandingPageIds(List<Long> pageIds);

    /**
     * 查询落地页列表
     *
     * @param queryLandingPageParamDto
     * @return
     */
    List<MgkLandingPageDto> getLandingPageDtos(QueryLandingPageParamDto queryLandingPageParamDto);

    /**
     * 分页查询落地页列表
     *
     * @param queryLandingPageParamDto
     * @param page
     * @return
     */
    PageResult<MgkLandingPageDto> getLandingPageDtos(QueryLandingPageParamDto queryLandingPageParamDto, Page page);

    /**
     * 分页查询带有表单落地页列表
     *
     * @param queryDto
     * @param page
     * @return
     */
    PageResult<MgkLandingPageWithFormDto> queryLandingPageWithForm(QueryLandingPageWithFormParamDto queryDto, Page page);

    /**
     * 分页查询带转化回传参数的落地页
     */
    PageResult<MgkLandingPageWithMacroParamDto> queryLandingPageWithMacroParam(QueryMgkLandingPageWithMacroParamDto queryDto, Page page);

    String getNativeLaunchUrlByPageId(Long pageId);

    /**
     * 获取带有宏参数投放地址
     *
     * @param pageId
     * @return
     */
    Map<Integer, String> getPlatformLaunchUrlWithParams(Long pageId);

    Map<Long, String> getJumpUrlByPageIds(List<Long> pageIdList);

    /**
     * 模板落地页
     * 根据 落地页 pageId, 游戏中心 gameId, Ios应用包packageId, 外链地址 url 生成对应的模板落地页
     * 其中 pageId 针对 H5 生成对应的 204 模板， 针对 401 生成 401 模板
     * gameId 根据游戏中心的 gameId 生成 206 模板
     * packageId 根据 ios 应用包生成 205 模板
     * url 根据外链地址 生成 204 模板
     * <p>
     * https://www.tapd.bilibili.co/67874887/documents/view/1167874887001007783?file_type=mindmap
     * https://www.tapd.bilibili.co/67874887/prong/stories/view/1167874887002464281
     *
     * @param operator
     * @param pageId
     * @param gameBaseId
     * @param packageId
     * @param url
     * @return 落地页信息
     */
    TemplatePageDto getMgkTemplatePage(Operator operator, Long pageId, Integer gameBaseId, Integer packageId, String url);

    /**
     * 通过应用包id查询的落地页id列表
     *
     * @param apkIds 应用包id列表
     * @return 落地页id列表
     */
    Map<Integer, List<Long>> getLandingPageIdsByApkIds(List<Integer> apkIds);

    /**
     * 更新落地页config里面的app链接信息
     *
     * @param oldApkUrl 旧应用链接
     * @param newApkUrl 新应用链接
     */
    void updatePageAppConfigByPageId(Long pageId, String oldApkUrl, String newApkUrl, Operator operator);

    /*
     * 创建落地页 包含了落地页创建的所有流程
     * @return 落地页id
     */
    long create(Operator operator, NewLandingPageDto newLandingPageDto);

    /*
     * 更新落地页 包含了落地页更新的所有流程
     */
    void update(Operator operator, UpdateLandingPageDto updateLandingPageDto);

    /*
     * 创建落地页-此接口仅仅创建了基础落地页，不会创建落地页模板和无视频图文替换模板
     * 现阶段使用方：1.评论转换组件生成暗投落地页
     */
    long createBaseInfo(Operator operator, NewLandingPageDto newLandingPageDto);

    /*
     * 删除落地页
     */
    void disable(Operator operator, Long pageId);

    /*
     * 刷新落地页缓存
     */
    void refreshLandingPageCache(long pageId);
}
