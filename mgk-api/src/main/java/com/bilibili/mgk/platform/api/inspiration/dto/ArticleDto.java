package com.bilibili.mgk.platform.api.inspiration.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * @file: ArticleVo
 * @author: gaoming
 * @date: 2021/03/24
 * @version: 1.0
 * @description:
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class ArticleDto implements Serializable {

    private static final long serialVersionUID = -6460750693666343886L;
    /**
     * 文章id
     */
    private Long articleId;

    /**
     * 标题
     */
    private String title;

    /**
     * 封面地址
     */
    private String cover;

    /**
     * 行业 1-电商 2-游戏 3-网服 4-教育 5-其他
     */
    private String industry;

    /**
     * 阅读数
     */
    private Long articleRead;

    /**
     * 点赞数
     */
    private Long articleLike;

    /**
     * 状态 0-启用 1-禁用
     */
    private Integer articleStatus;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 内容
     */
    private String content;

    /**
     * 是否点赞 0-没有点赞 1-点赞
     */
    private Integer isLike;

    /**
     * 用户名称
     */
    private String creator;
}
