package com.bilibili.mgk.platform.api.form.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CmPhoneQueryReq implements Serializable {

    private static final long serialVersionUID = -5477426200668490536L;

    private String cityCode;

    private String provinceCode;

    private String touchCode;

    private Integer size;

    private String numberKeyword;

    private List<SaleTagInfo> tagInfo;

}
