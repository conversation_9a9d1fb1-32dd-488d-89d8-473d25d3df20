package com.bilibili.mgk.platform.api.form.dto;

import com.bilibili.mgk.platform.common.DealStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkFormCustomizeLbsDto implements Serializable{

    private static final long serialVersionUID = -4848306830221483872L;
    /**
     * 表单ID
     */
    private Long formItemId;

    /**
     * 处理状态
     */
    private Integer dealStatus;

    /**
     * 处理进度
     */
    private Integer processingProgress;

    /**
     * 失败原因
     */
    private String failMsg;

    /**
     * 表单项配置信息
     */
    private String config;

    public static MgkFormCustomizeLbsDto initStatus(long formItemId){
        return MgkFormCustomizeLbsDto.builder()
                .dealStatus(DealStatusEnum.PROCESSING.getCode()).processingProgress(0)
                .formItemId(formItemId).build();
    }

    public static MgkFormCustomizeLbsDto finishStatus(long formItemId, String config){
        return MgkFormCustomizeLbsDto.builder()
                .dealStatus(DealStatusEnum.FINISHED.getCode()).processingProgress(100)
                .config(config)
                .formItemId(formItemId).build();
    }


}
