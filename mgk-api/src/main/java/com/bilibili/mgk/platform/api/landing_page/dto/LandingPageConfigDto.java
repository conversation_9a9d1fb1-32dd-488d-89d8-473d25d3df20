package com.bilibili.mgk.platform.api.landing_page.dto;

import com.bilibili.mgk.platform.common.page_bean.GameDto;
import com.bilibili.mgk.platform.common.page_bean.WorkChatCustomerAcquisitionInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/1/18
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LandingPageConfigDto implements Serializable {
    private static final long serialVersionUID = -2938202319875687461L;
    /**
     * 自增id
     */
    private Integer id;

    /**
     * 账号ID
     */
    private Integer accountId;

    /**
     * 页面ID
     */
    private Long pageId;

    /**
     * 落地页名称
     */
    private String name;

    /**
     * 页面标题
     */
    private String title;

    /**
     * 模板样式：1-浮层样式 2-图文样式 3-橱窗样式 4-视频样式 100-自定义
     */
    private Integer templateStyle;

    /**
     * 生效开始时间
     */
    private Timestamp effectiveStartTime;

    /**
     * 生效结束时间
     */
    private Timestamp effectiveEndTime;

    /**
     * 状态: 1-未发布 2-已发布 3-已下线 4-管理员驳回 5-已删除
     */
    private Integer status;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 删除或下线原因
     */
    private String reason;

    /**
     * 视频Ids
     */
    private List<Integer> bizIds;

    /**
     * 页面是否含有header 0-没有 1-有
     */
    private Integer header;

    /**
     * 是否需要转场 0-不需要 1-需要
     */
    private Integer hasTransition;

    /**
     * 转场信息
     */
    private String transition;

    /**
     * 落地页是否包含滚动信息 0-不包含 1-包含
     */
    private Integer isFormScroll;


    /**
     * 稿件Ids
     */
    private List<Long> avIds;

    /**
     * 落地页类型
     */
    private Integer type;

    /**
     * 应用包id
     */
    private List<Integer> appPackageIds;

    /**
     * 页面配置
     */
    private String config;

    /**
     * 曝光监控URL
     */
    private List<String> showUrls;

    /**
     * 模板ID
     */
    private Long modelId;

    /**
     * 是否模板
     */
    private Integer isModel;

    /**
     * 是否支持PC
     */
    private Integer isPc;

    /**
     * 表单id 新版已经弃用但有历史原因校验
     */
    private Long formId;

    /**
     * 表单ID
     */
    private List<Long> formIds;

    /**
     * 微信小游戏id
     */
    private List<Integer> miniGameIds;

    /**
     * 微信加粉微信包id
     */
    private Integer wechatPackageId;

    /**
     * 微信加粉手动切换开关
     */
    private Integer wechatIsManual;

    /**
     * pc发布地址
     */
    private String pcLaunchUrl;

    /**
     * 移动发布地址
     */
    private String mobileLaunchUrl;

    /**
     * 客户名称
     */
    private String privacyName;

    /**
     * 隐私协议的url
     */
    private String privacyUrl;

    /**
     * 落地页背景颜色
     */
    private String pageBgColor;

    /**
     * 落地页背景地址
     */
    private String pageBgUrl;

    /**
     * 封面地址
     */
    private String pageCover;

    /**
     * 是否包含动态商品组件 0-不包含 1-包含
     */
    private Integer hasDpaGoods;

    /**
     * 是否视频落地页 0-不是 1-是
     */
    private Integer isVideoPage;

    /**
     * 落地页版本
     */
    private String pageVersion;

    /**
     * 总模块高度
     */
    private Integer totalBlockSize;

    /**
     * 总下载组件高度
     */
    private Integer totalDownloadComponentSize;

    /**
     * 最大的下载组件高度
     */
    private Integer maxDownloadComponentSize;

    /**
     * 第一个屏幕下载组件的大小
     */
    private Integer totalFirstScreenDownloadComponentSize;

    /**
     * 下载组件高度超过限制
     */
    private Integer isDownloadOverLimit;

    //获客链接id列表
    private List<String> customerAcquisitionLinkIds;

    //是否是表单预约落地页 0-否 1-是
    private Integer isGameFormReserve;

    private List<GameDto> games;

    private Integer copySourceAccountId;


}
