package com.bilibili.mgk.platform.api.dynamic.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @file: MgkDynamicLikeStatsReplyDto
 * @author: gaoming
 * @date: 2021/05/17
 * @version: 1.0
 * @description:
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MgkDynamicLikeStatsReplyDto implements Serializable {
    private static final long serialVersionUID = -1201119713941399931L;

    /**
     * 视频id
     */
    private Long bizId;

    /**
     * 点赞数
     */
    private Long likeNumber;

    /**
     * 点踩数
     */
    private Long dislikeNumber;

    /**
     * 是否点赞
     */
    private Integer hasLike;

    /**
     * 是否点踩
     */
    private Integer hasDislike;

}
