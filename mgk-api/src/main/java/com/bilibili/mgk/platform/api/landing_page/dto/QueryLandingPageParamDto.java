package com.bilibili.mgk.platform.api.landing_page.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/1/18
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryLandingPageParamDto implements Serializable {
    private static final long serialVersionUID = -3109020988235522953L;
    private List<Integer> idList;
    private List<Integer> accountIdList;
    private List<Long> pageIdList;
    private List<Integer> templateStyleList;
    private List<Integer> statusList;
    private List<Integer> typeList;
    private List<Long> formIdList;
    private Timestamp effectiveStartTime;
    private Timestamp effectiveEndTime;
    private boolean formIdLtZero;

    private boolean withAvid;

    private String nameLike;
    private String titleLike;
    private String creatorLike;

    private String orderBy;
    private Integer limit;
    private List<Long> modelIdList;
    private List<Integer> isVideoPages;
    /**
     * 刷选固定的avids
     */
    private List<Long> avids;
    /**
     * dpa组件
     */
    private Integer hasDpaGoods;

    //以下字段只用于新版 我的落地页 页面
    /**
     * 投放开始时间
     */
    private String launchBeginDate;

    /**
     * 投放结束时间
     */
    private String launchEndDate;

    /**
     * 排序方式
     * @See com.bilibili.mgk.platform.common.MyMgkSortTypeEnum
     */
    private Integer sortType;

    /**
     * 排序字段 0-创建时间 1-消费 2-点击 3-转化 4-转化率
     * @See com.bilibili.mgk.platform.common.MyMgkSortDataEnum
     */
    private Integer sortData;

    /**
     * 页面版本 0-旧版七日内数据 1-新版根据时间范围筛选
     * @See com.bilibili.mgk.platform.common.MyMgkPageVersionEnum
     */
    private Integer pageVersion;

    /**
     * 不在此页面id列表中的数据
     */
    private List<Long> excludePageIdList;


    /**
     * 落地页模型 0-普通落地页 1-落地页模板 2-创意联投模板 3-去除视频副本 4-评论区暗投
     */
    private List<Integer> isModelList;

    private Integer isPush;
}
