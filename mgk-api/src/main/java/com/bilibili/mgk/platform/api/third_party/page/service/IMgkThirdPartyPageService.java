package com.bilibili.mgk.platform.api.third_party.page.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.mgk.platform.api.landing_page_group.dto.mapping.LandingPageGroupMappingListDto;
import com.bilibili.mgk.platform.api.third_party.page.dto.MgkThirdPartyPageDto;
import com.bilibili.mgk.platform.api.third_party.page.dto.QueryMgkThirdPartyPageDto;

import java.util.List;

/**
 * @ClassName IMgkThirdPartyPageService
 * <AUTHOR>
 * @Date 2023/5/19 2:27 下午
 * @Version 1.0
 **/
public interface IMgkThirdPartyPageService {

    List<LandingPageGroupMappingListDto> saveThirdPartyPage(List<LandingPageGroupMappingListDto> mappingList, Operator operator);

    List<MgkThirdPartyPageDto> queryMgkThirdPartyPageList(QueryMgkThirdPartyPageDto queryDto);

    int countMgkThirdPartyPage(QueryMgkThirdPartyPageDto queryDto);

}
