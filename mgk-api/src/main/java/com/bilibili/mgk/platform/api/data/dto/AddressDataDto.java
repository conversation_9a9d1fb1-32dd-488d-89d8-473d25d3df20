package com.bilibili.mgk.platform.api.data.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018/1/18
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddressDataDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private Item province;
    private Item city;
    private Item county;
    private String detail;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Item implements Serializable {
        private static final long serialVersionUID = 1L;

        private String id;
        private String name;

    }
}
