package com.bilibili.mgk.platform.api.landing_page.dto;

import com.bilibili.adp.common.util.Page;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @file: QueryHotPageListDto
 * @author: gaoming
 * @date: 2021/11/11
 * @version: 1.0
 * @description:
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class QueryHotPageListDto implements Serializable {
    private static final long serialVersionUID = -2195578541627343425L;

    /**
     * 行业
     */
    private List<String> categories;

    /**
     * 平台 设备 1-移动 2-PC
     */
    private List<Integer> platforms;

    /**
     * 排序 1-点击转化率
     */
    private Integer orderBy;

    /**
     * 时间 1-昨天 2-7天 3-30天
     */
    private Integer dataPeriod;

    /**
     * 落地页id
     */
    private List<String> pageIds;

    /**
     * 分页参数
     */
    private Page page;
}
