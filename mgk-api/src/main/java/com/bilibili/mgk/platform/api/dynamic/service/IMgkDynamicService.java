package com.bilibili.mgk.platform.api.dynamic.service;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.mgk.platform.api.dynamic.dto.*;

/**
 * @file: IDynamicService
 * @author: gaoming
 * @date: 2021/05/17
 * @version: 1.0
 * @description:
 **/
public interface IMgkDynamicService {

    /**
     * 点赞 or 点踩
     *
     * @param reqDto
     * @return
     */
    MgkDynamicLikeReplyDto dynamicLike(MgkDynamicLikeReqDto reqDto) throws ServiceException;

    /**
     * 点赞 or 点踩 数据查询
     *
     * @param reqDto
     * @return
     */
    MgkDynamicLikeStatsReplyDto dynamicLikeStats(MgkDynamicLikeStatsReqDto reqDto) throws ServiceException;

    /**
     * 根据创意Id获取视频Id From Redis
     *
     * @param creativeId
     * @return
     */
    MgkDynamicCreativeBizReplyDto getDynamicBizByCreativeIdFromRedis(Integer creativeId);

    /**
     * 动态点赞刷如redis
     *
     * @return
     */
    void refreshDynamicBizMappingInRedis();

    /**
     * 根据pageId获取点赞点踩数
     *
     * @param pageId
     * @param mid
     * @return
     */
    MgkDynamicLikeStatsReplyDto getDynamicBizStats(Long pageId, Long mid, String buvid) throws ServiceException;
}
