package com.bilibili.mgk.platform.api.hot_video.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @file: HotVideoBlackDto
 * @author: gaoming
 * @date: 2020/12/22
 * @version: 1.0
 * @description:
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HotVideoBlackDto implements Serializable {
    private static final long serialVersionUID = 7918722105295509563L;

    /**
     * 自增Id
     */
    private Integer id;

    /**
     * 黑名单雪花id
     */
    private Long blackId;

    /**
     * 用户Id
     */
    private String creator;

    /**
     * bvid
     */
    private String bvid;

    /**
     * 黑名单类型 0-热门视频 1-热门广告
     */
    private Integer blackType;

    /**
     * 是否删除 0-正常 1-删除
     */
    private Integer isDeleted;

    /**
     * 广告类型与创意Id
     */
    private String adTypeCreativeId;
}
