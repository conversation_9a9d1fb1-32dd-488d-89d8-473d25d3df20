package com.bilibili.mgk.platform.api.video_library.soa;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.mgk.platform.api.video_library.dto.SoaQueryVideoLibraryDto;
import com.bilibili.mgk.platform.api.video_library.dto.VideoLibraryDto;

/**
 * <AUTHOR>
 * @date 2019/3/14
 **/
public interface ISoaVideoLibraryService {

    PageResult<VideoLibraryDto> queryVideoLibraryByPage(SoaQueryVideoLibraryDto queryParam);

    /**
     * 获取视频库视频的信息
     *
     * @param id
     * @return
     */
    VideoLibraryDto getVideoLibraryDtoById(Integer id);

    /**
     * 通过落地页id获取落地页绑定的第一个视频
     *
     * @param pageId
     * @return
     */
    Integer getBizIdByPageId(Long pageId);

    /**
     * 更新视频的审核状态
     * @param operator 操作人信息
     * @param bizId 视频id
     * @param toAuditStatus 目标审核状态
     * @param reason 审核拒绝原因
     */
    void updAuditStatus(Operator operator, Integer bizId, Integer toAuditStatus, String reason);

    boolean createVideoLibraryIfUposCallbackIsReady(Integer bizId, Integer accountId, String name);
}
