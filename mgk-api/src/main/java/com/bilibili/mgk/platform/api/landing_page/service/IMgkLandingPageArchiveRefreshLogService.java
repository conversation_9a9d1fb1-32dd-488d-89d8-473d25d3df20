package com.bilibili.mgk.platform.api.landing_page.service;

import com.bilibili.mgk.platform.api.landing_page.dto.MgkLandingPageArchiveRefreshLogDto;

import java.util.List;

/**
 * @file: IMgkLandingPageArchiveRefreshLogService
 * @author: gaoming
 * @date: 2021/12/13
 * @version: 1.0
 * @description:
 **/
public interface IMgkLandingPageArchiveRefreshLogService {

    /**
     * 根据ids获取数据
     *
     * @param pageIds
     * @return
     */
    List<MgkLandingPageArchiveRefreshLogDto> getDtosByPageIds(List<Long> pageIds);

    /**
     * 根据pageId删除数据
     *
     * @param pageId
     */
    void deletedByPageId(Long pageId);
}
