package com.bilibili.mgk.platform.api.landing_page.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/1/18
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryLandingPageWithFormParamDto implements Serializable {
    private static final long serialVersionUID = -3109020988235522953L;

    private List<Integer> idList;
    private List<Integer> accountIdList;
    private List<Long> pageIdList;
    private List<Integer> statusList;
    private List<Integer> typeList;
    private Timestamp effectiveStartTime;
    private Timestamp effectiveEndTime;

    private String nameLike;
    private String titleLike;
    private String creatorLike;

    private String orderBy;
}
