package com.bilibili.mgk.platform.api.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/07/07
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryModelParamDto implements Serializable {
    private static final long serialVersionUID = 8007897425231887328L;
    /**
     * 用户id
     */
    private List<Integer> accountIds;

    /**
     * 模板id列表
     */
    private List<Long> modelIds;
    /**
     * 落地页id列表
     */
    private List<Long> pageIds;
    /**
     * 模板名称
     */
    private String nameLike;
    /**
     * 模板样式
     */
    private List<Integer> modelStyles;
    /**
     * 状态
     */
    private List<Integer> statusList;
    /**
     * 行业
     */
    private List<Long> tradeIds;
    /**
     * 是否删除
     */
    private Integer isDeleted;
    /**
     * 排序
     */
    private String orderBy;
    /**
     * 模板类型
     */
    private List<Integer> modelTypes;

    private List<Integer> moduleStyleIds;

    private List<Integer> moduleContentIds;

    /**
     * 类型 0-模版 1-模块
     */
    private Integer type;

    private Integer isAdmin;

    /**
     * 一级行业
     */
    private List<Long> parentTradeIds;
}
