package com.bilibili.mgk.platform.api.hot_video.dto;

import com.bilibili.adp.common.util.Page;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @file: QueryHotVideoCollectDto
 * @author: gaoming
 * @date: 2020/11/12
 * @version: 1.0
 * @description:
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryHotVideoCollectDto implements Serializable {
    private static final long serialVersionUID = 2550088607177651786L;

    /**
     * 收藏Id
     */
    private List<Long> collectIds;

    /**
     * 用户Id
     */
    private List<Integer> accountIds;

    /**
     * 标题
     */
    private List<String> titles;

    /**
     * 收藏类型 收藏类型 0-热门视频 1-热门广告
     */
    private List<Integer> collectTypes;

    /**
     * bvid黑名单
     */
    private List<String> blackList;

    /**
     * adTypeCreativeId黑名单
     */
    private List<String> adTypeCreativeIdBlacks;

    /**
     * bvid
     */
    private List<String> bvids;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 页面
     */
    private Page page;

    /**
     * 广告类型和创意Id
     */
    private List<String> adTypeCreativeIds;

    private String beginDate;
}
