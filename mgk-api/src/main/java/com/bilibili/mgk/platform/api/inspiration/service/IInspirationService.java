package com.bilibili.mgk.platform.api.inspiration.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.mgk.platform.api.inspiration.dto.ArticleDto;
import com.bilibili.mgk.platform.api.inspiration.dto.NewArticleDto;
import com.bilibili.mgk.platform.api.inspiration.dto.QueryArticleDto;
import com.bilibili.mgk.platform.api.inspiration.dto.UpdateArticleDto;

import java.io.File;
import java.io.IOException;
import java.util.List;


/**
 * @file: IInspirationService
 * @author: gaoming
 * @date: 2021/03/17
 * @version: 1.0
 * @description:
 **/
public interface IInspirationService {
    /**
     * 上传文件并解析pdf
     *
     * @param file
     * @return
     */
    List<String> uploadAndParsePDF(File file) throws IOException;

    /**
     * 新增文章
     *
     * @param operator
     * @param newArticleDto
     * @return
     */
    Long createArticle(Operator operator, NewArticleDto newArticleDto);

    /**
     * 更新文章
     *
     * @param operator
     * @param updateArticleDto
     */
    void updateArticle(Operator operator, UpdateArticleDto updateArticleDto);

    /**
     * 增加阅读数
     *
     * @param articleId
     */
    void addRead(Long articleId);

    /**
     * 增加点赞
     *
     * @param operator
     * @param articleId
     */
    void addLike(Operator operator, Long articleId);

    /**
     * 取消点赞
     *
     * @param operator
     * @param articleId
     */
    void unlike(Operator operator, Long articleId);

    /**
     * 列表查询文章
     *
     * @param queryArticleDto
     * @return
     */
    PageResult<ArticleDto> queryArticle(QueryArticleDto queryArticleDto);

    /**
     * 查询文章详情
     *
     * @param operator
     * @param articleId
     * @return
     */
    ArticleDto getArticleByArticleId(Operator operator, Long articleId);

    /**
     * 查询用户相关的文章
     *
     * @param operator
     * @return
     */
    List<ArticleDto> queryRelevant(Operator operator);


    /**
     * 更新文章状态
     *
     * @param articleId
     * @param code
     */
    void updateArticleStatus(Long articleId, Integer code);
}
