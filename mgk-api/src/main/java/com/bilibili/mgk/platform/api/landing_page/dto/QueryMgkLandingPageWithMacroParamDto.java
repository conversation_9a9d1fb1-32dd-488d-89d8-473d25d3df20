package com.bilibili.mgk.platform.api.landing_page.dto;

import com.bilibili.adp.common.util.Page;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * @ClassName QueryMgkLandingPageWithMacroParamDto
 * <AUTHOR>
 * @Date 2022/8/26 3:50 下午
 * @Version 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryMgkLandingPageWithMacroParamDto implements Serializable {
    private static final long serialVersionUID = -31090123123953L;

    private List<Integer> idList;
    private List<Integer> accountIdList;
    private List<Long> pageIdList;
    private List<Integer> statusList;
    private List<Integer> typeList;
    private Timestamp effectiveStartTime;
    private Timestamp effectiveEndTime;

    private Integer needWithForm;
    private Integer needWithLauMiniGame;
    private Integer lauMiniGameId;

    private String nameLike;
    private String titleLike;
    private String creatorLike;

    private String orderBy;

}
