package com.bilibili.mgk.platform.api.landing_page.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName SanlianMgkTemplatePageDto
 * <AUTHOR>
 * @Date 2024/4/7 5:36 下午
 * @Version 1.0
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class SanlianMgkTemplatePageDto {

    /**
     * 原始pageId
     */
    private Long mgkPageId;

    private Integer gameBaseId;

    private Integer appPackageId;

    private String url;

    private Integer pageStatus;

    // 模板页面id
    private Long templatePageId;
    /**
     * 移动地址
     */
    private String launchUrl;

    /**
     * pc地址
     */
    private String launchUrlSecondary;

}
