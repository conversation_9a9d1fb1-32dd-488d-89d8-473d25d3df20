package com.bilibili.mgk.platform.api.landing_page.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @file: MgkHotLandingPageDto
 * @author: gaoming
 * @date: 2021/11/11
 * @version: 1.0
 * @description:
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class MgkHotLandingPageDto implements Serializable {
    private static final long serialVersionUID = -1876223468510359393L;

    /**
     * 落地页id
     */
    private Long pageId;

    /**
     * 落地页名称
     */
    private String name;

    /**
     * 落地页标题
     */
    private String title;

    /**
     * 点击转化率
     */
    private Long ctr;

    /**
     * 点击转化率排序
     */
    private String ctrRank;

    /**
     * 点击转化率排序描述
     */
    private String ctrRankDesc;


    /**
     * 封面地址
     */
    private String pageCover;

    /**
     * 页面地址
     */
    private String hotPageUrl;

    /**
     * 落地页行业
     */
    private String hotPageCategory;
}
