package com.bilibili.mgk.platform.api.conversion;

import com.bilibili.mgk.platform.api.conversion.bos.MgkSecretBo;

import java.util.Collection;
import java.util.List;

public interface MgkSecretService {
    String get(Integer accountId);
    void delete(Integer accountId);
    List<MgkSecretBo> refresh(Collection<Integer> accountIds);
    List<MgkSecretBo> insertOrIgnore(Collection<Integer> accountIds);
    String getCachedOrReload(Integer accountId);
    boolean isSecretValid(String secret);
}
