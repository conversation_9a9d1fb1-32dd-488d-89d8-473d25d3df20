package com.bilibili.mgk.platform.api.model.dto;

import com.bilibili.mgk.platform.api.landing_page.dto.NewLandingPageDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/07/08
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NewModelDto implements Serializable {
    private static final long serialVersionUID = -936432872281936603L;
    /**
     * 账号ID
     */
    private Integer accountId;
    /**
     * 模板版本
     */
    private String modelVersion;
    /**
     * 模板名称
     */
    private String modelName;
    /**
     * 模板类型
     */
    private Integer modelStyle;
    /**
     * 模板类型
     */
    private Integer modelType;
    /**
     * 模板封面地址
     */
    private String coverUrl;
    /**
     * 备注
     */
    private String remark;
    /**
     * 所属行业
     */
    private List<Long> tradeIds;
    /**
     * 模板落地页详情
     */
    private NewLandingPageDto newLandingPageDto;


    private Integer moduleStyleId;

    private Integer moduleContentId;

    /**
     * 类型 0-模版 1-模块
     */
    private Integer type;

    //模块高度
    private Integer moduleHeight;

    //模块权重
    private Integer moduleWeight;

    /**
     * 是否是管理员账号操作
     */
    private Integer isAdmin;
}
