package com.bilibili.mgk.platform.api.hot_ads.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @file: HotAdsDto
 * @author: gaoming
 * @date: 2021/01/07
 * @version: 1.0
 * @description:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HotAdsDto implements Serializable {

    private static final long serialVersionUID = -578544113822092113L;
    /**
     * 单元名称
     */
    private String unitName;

    /**
     * 广告类型和创意ID
     */
    private String adTypeCreativeId;

    /**
     * 广告类型 cpc cpm gd
     */
    private String adType;

    /**
     * 创意ID
     */
    private String creativeId;

    /**
     * 创意标题
     */
    private String creativeTitle;

    /**
     * 创意形态 1-静态图文 2-动态图文 3-静态视频 4-广告位播放视频
     */
    private Integer styleAbility;

    /**
     * 图片gif类型为图片链接，视频类型为封面图
     */
    private String imageUrl;

    /**
     * 视频地址
     */
    private String videoUrl;

    /**
     * 账户ID
     */
    private Long accountId;

    /**
     * 行业
     */
    private String firstIndustry;

    /**
     * bvid
     */
    private String bvid;

    /**
     * 曝光
     */
    private Long pv;

    /**
     * 点击
     */
    private Long click;

    /**
     * 转化数
     */
    private Long convNum;

    /**
     * ctr
     */
    private Double ctr;

    /**
     * cvr
     */
    private Double cvr;

    /**
     * 曝光等级 S A B C
     */
    private String pvRank;

    /**
     * ctr等级
     */
    private String ctrRank;

    /**
     * cvr等级
     */
    private String cvrRank;

    /**
     * 创意创建时间
     */
    private String creativeCreateTime;

    /**
     * 同步日期
     */
    private String logDate;

    /**
     * 统计日期类型,7d:最近7天，30d:最近30天
     */
    private String dayType;

    /**
     * 是否收藏
     */
    @Builder.Default
    private Integer isCollect = 0;

    /**
     * 黑名单 0-正常 1-黑名单
     */
    private Integer blackStatus;

    /*
     * 是否是竖屏，1:是 0:否
     */
    private Integer isVerticalScreen;
}
