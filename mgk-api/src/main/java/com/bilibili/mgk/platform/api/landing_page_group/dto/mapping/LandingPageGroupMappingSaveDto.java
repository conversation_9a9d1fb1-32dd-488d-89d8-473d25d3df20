package com.bilibili.mgk.platform.api.landing_page_group.dto.mapping;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName LandingPageGroupMappingBaseDto
 * <AUTHOR>
 * @Date 2023/5/17 2:30 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LandingPageGroupMappingSaveDto {

    private Integer groupSource;

    private Long groupId;

    private Boolean hasAuditCreative;

    private List<LandingPageGroupMappingListDto> mappingList;

}
