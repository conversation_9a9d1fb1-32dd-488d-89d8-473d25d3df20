package com.bilibili.mgk.material.center.service.creative;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/10/15
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.LowerCamelCaseStrategy.class)

public class TriggerReq {

    private Long accountId;

    private String videoFileName;

    private String videoMd5;

    private Boolean needEraseWatermark;
}

