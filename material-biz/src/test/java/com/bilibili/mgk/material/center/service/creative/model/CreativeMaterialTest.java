package com.bilibili.mgk.material.center.service.creative.model;

import com.bilibili.mgk.material.center.facade.converter.CreativeMaterialOnesDTO;
import com.bilibili.mgk.material.center.util.JsonUtil;
import java.util.HashMap;
import java.util.Map;
import org.junit.Test;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/15
 */
public class CreativeMaterialTest {


    @Test
    public void testDeserialize() {
        Map<String, String> mapValue = new HashMap<>();
        mapValue.put("ad_type", "cpm");

        mapValue.put("hudong", "0");
        mapValue.put("pv", "20820676");
        mapValue.put("creative_id", "123377156");
        mapValue.put("commerce_category_second_name", "综合电商（除盲盒类）");
        mapValue.put("play_10s_rate_rank", "S");
        mapValue.put("campaign_id", "3377970");
        mapValue.put("cvr_rank", "B");

        mapValue.put("verify_type", "0");
        mapValue.put("main_image_url", "NULL");
        mapValue.put("commerce_category_first_id", "71");
        mapValue.put("image_url",
                "https://i0.hdslb.com/bfs/sycp/creative_img/202307/540ed9885770d582b8aacba3613bfb6a.jpg");
        mapValue.put("commerce_category_first_name", "电商平台");
        mapValue.put("category_first_name", "带货");
        mapValue.put("click", "269700");
        mapValue.put("unit_name", "0724-宠物-提摩西牧草段-1121");

        mapValue.put("item_source", "NULL");
        mapValue.put("conv_num", "77662");
        mapValue.put("log_date", "20240314");
        mapValue.put("image_md5", "745cfff83af25adb5af93164a2042afe");
        mapValue.put("play_10s_cnt", "0");
        mapValue.put("creative_ctime", "2023-12-13 12:06:04");
        mapValue.put("src_type", "0");
        mapValue.put("avid", "*********");
        mapValue.put("daihuo_first_category", "NULL");
        mapValue.put("pv_rank", "S");
        mapValue.put("video_duration", "7");
        mapValue.put("start_play", "2");
        mapValue.put("cpa_target_type", "17");
        mapValue.put("hot", "********");
        mapValue.put("play_3s_rate_rank", "S");
        mapValue.put("creative_title", "淘宝精选");
        mapValue.put("material_type", "3");
        mapValue.put("campaign_name", "1686901-0724-宠物");
        mapValue.put("search_daihuo_keyword", "");
        mapValue.put("video_url",
                "https://www.bilibili.com/video/av*********");
        mapValue.put("material_ctime", "");
        mapValue.put("ctr_rank", "A+");
        mapValue.put("account_name", "手机淘宝-带货起飞-乐言-18");
        mapValue.put("unit_id", "********");
        mapValue.put("day_type", "3d");
        mapValue.put("search_keyword", "淘宝精选");
        mapValue.put("cost", "36233.***********");
        mapValue.put("item_id", "NULL");
        mapValue.put("promotion_purpose_type", "15");
        mapValue.put("ctcvr_rank", "B+");
        mapValue.put("item_name", "NULL");
        mapValue.put("play_3s_cnt", "2");
        mapValue.put("style_ability", "3");
        mapValue.put("cpa_level", "2");
        mapValue.put("hudong_rank", "S");
        mapValue.put("hot_rank", "S");
        mapValue.put("account_id", "1686901");
        mapValue.put("is_vertical_screen", "0");
        mapValue.put("commerce_category_second_id", "94");
        mapValue.put("material_id", "*********");
        mapValue.put("daihuo_second_category", "NULL");

        CreativeMaterialOnesDTO value = JsonUtil.fromJson(mapValue, CreativeMaterialOnesDTO.class);

        System.out.println(value);

    }

}