package com.bilibili.mgk.material.center.service.mainsite;

import com.bilibili.mgk.material.center.util.ImageUtil;
import io.vavr.control.Try;
import java.util.regex.Pattern;
import org.junit.Test;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/10/28
 */
public class AuditAdviceServiceTest {


    @Test
    public void test() {

        Pattern pattern = Pattern.compile(".*hdslb.com/bfs.*");

        System.out.println(pattern.matcher("https://i0.hdslb.com/bfs/archive/1.jpg").matches());
        System.out.println(pattern.matcher("https://i0.hdslb.com1/bfs/archive/1.jpg").matches());
    }


    @Test
    public void testKb() {

        Integer imgByteSizeKb = Try.of(() -> {

            return ImageUtil.url2data(
                    "https://i0.hdslb.com/bfs/sycp/mgk/collage/png/202409/3999305bf62bde183cf2b994d72fce24.png").length
                    / 1024;
        }).getOrElse(0);

        System.out.println(imgByteSizeKb);

    }
}