package com.bilibili.mgk.material.center.service.creative.model;

import com.bilibili.mgk.material.center.service.converter.BusinessPojoConverter;
import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/6/27
 */
@Data
@Accessors(chain = true)
public class RisingFastVideoCurve implements SnakeCaseBody {

    private static DateTimeFormatter curveTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @ApiModelProperty("增量曲线")
    public List<RisingFastVideoCurvePoint> pointsIncr;


    @ApiModelProperty("累计曲线")
    public List<RisingFastVideoCurvePoint> pointsAcc;


    @ApiModelProperty("视频bvid")
    private String bvid;

    /**
     * avid
     */
    @ApiModelProperty(value = "视频id")
    private Long avid;


    /**
     * 视频标题
     */
    @ApiModelProperty(value = "视频标题")
    private String avidTitle;

    /**
     * 视频封面
     */
    @ApiModelProperty(value = "视频封面")
    private String avidCover;

    /**
     * 视频简介
     */

    @ApiModelProperty(value = "视频简介")
    private String avidContent;

    /**
     * 视频标签
     */

    @ApiModelProperty(value = "视频标签")
    private String avidTag;


    /**
     * 视频一级分类，e.g 游戏、番剧
     */
    @ApiModelProperty(value = "视频一级分类，e.g 游戏、番剧")
    private String avidTidName;

    /**
     * 是否竖屏
     */
    @ApiModelProperty(value = "是否竖屏")
    private Integer isVerticalScreen;

    /**
     * 视频时长, s
     */
    @ApiModelProperty(value = "视频时长")
    private Long avidDuration;


    /**
     * 视频发布时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "视频发布时间")
    private LocalDateTime avidPubtime;


    /**
     * 视频up主
     */
    @ApiModelProperty(value = "视频up主")
    private Long upMid;

    /**
     * 视频up主昵称
     */
    @ApiModelProperty(value = "视频up主昵称")
    private String upName;

    /**
     * up认证
     */
    @ApiModelProperty("up认证")
    private Integer verifyType;


    /**
     * 认证类型描述
     */
    @ApiModelProperty(value = "认证类型描述")
    private String verifyTypeDesc;


    /**
     * 选择dataReadyTime
     */
    private String latestUpdateTime;

    @ApiModelProperty("曲线类型")
    private RisingFastVideoCurveType curveType;

    /**
     * @param videos    要求都是同时一个视频的聚合数据， 且包含的聚合数据要符合curveType需求
     * @param curveType
     * @return
     */
    public static Optional<RisingFastVideoCurve> from(
            List<RisingFastVideo> videos,
            RisingFastVideoCurveType curveType,
            String latestUpdateTime) {

        if (CollectionUtils.isEmpty(videos)) {
            return Optional.empty();

        }

        RisingFastVideoCurve result = BusinessPojoConverter.converter.toCurveAndCopyStaticInfo(videos.get(0));

        result.setLatestUpdateTime(latestUpdateTime);
        result.setCurveType(curveType);
        result.setPointsIncr(videos.stream()
                .map(video -> RisingFastVideoCurvePoint.asVideoCurvePoint(curveType, video, true))
                .sorted(Comparator.comparing(RisingFastVideoCurvePoint::getDate))
                .collect(Collectors.toList()));

        result.setPointsAcc(videos.stream()
                .map(video -> RisingFastVideoCurvePoint.asVideoCurvePoint(curveType, video, false))
                .sorted(Comparator.comparing(RisingFastVideoCurvePoint::getDate))
                .collect(Collectors.toList()));

        return Optional.of(result);

    }


}
