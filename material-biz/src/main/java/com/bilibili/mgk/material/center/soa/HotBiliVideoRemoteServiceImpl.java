package com.bilibili.mgk.material.center.soa;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.mgk.material.center.service.converter.BusinessPojoConverter;
import com.bilibili.mgk.material.center.service.creative.HotBiliVideoService;
import com.bilibili.mgk.material.center.service.creative.model.HotBiliVideo;
import com.bilibili.mgk.material.center.service.creative.vo.DateAggregation;
import com.bilibili.mgk.material.center.service.creative.vo.HotBiliVideoQuery;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialSortBy;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.bilibili.mgk.material.center.service.creative.vo.QueryKeywordType;
import com.bilibili.mgk.platform.api.hot_video.dto.HotBiliVideoDto;
import com.bilibili.mgk.platform.api.hot_video.dto.HotBiliVideoReqQuery;
import com.bilibili.mgk.platform.api.hot_video.service.HotBiliVideoRemoteService;
import com.bilibili.mgk.platform.common.MgkHotVideoOrderByEnum;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class HotBiliVideoRemoteServiceImpl implements HotBiliVideoRemoteService {

    @Autowired
    private HotBiliVideoService hotBiliVideoService;


    @Override
    public PageResult<HotBiliVideoDto> page(HotBiliVideoReqQuery query) {
        log.info("HotBiliVideoReqQuery = {}", JSON.toJSONString(query));
        HotBiliVideoQuery hotBiliVideoQuery = new HotBiliVideoQuery();
        hotBiliVideoQuery.setAvid(query.getAvid());
        hotBiliVideoQuery.setAccountId(query.getAccountId());
        hotBiliVideoQuery.setBvid(query.getBvid());
        hotBiliVideoQuery.setBusinessInterest(query.getBussInterest());
        if (Integer.valueOf(0).equals(query.getDateType())) {
            hotBiliVideoQuery.setDayType(DateAggregation.d1);
        } else if (Integer.valueOf(1).equals(query.getDateType())) {
            hotBiliVideoQuery.setDayType(DateAggregation.d7);
        } else if (Integer.valueOf(2).equals(query.getDateType())) {
            hotBiliVideoQuery.setDayType(DateAggregation.d30);
        } else {
            hotBiliVideoQuery.setDayType(DateAggregation.d7);
        }

        if (!StringUtils.isEmpty(query.getTitle())) {
            hotBiliVideoQuery.setKeyword(query.getTitle());
            hotBiliVideoQuery.setKeywordType(QueryKeywordType.video_title);
        }
        if (query.getOrderBy() != null) {
            MgkHotVideoOrderByEnum mgkHotVideoOrderByEnum = MgkHotVideoOrderByEnum.getByCode(query.getOrderBy());
            switch (mgkHotVideoOrderByEnum) {
                case REPLY_DAILY: {
                    hotBiliVideoQuery.setSortBy(MaterialSortBy.reply);
                    break;
                }
                case FAV_DAILY: {
                    hotBiliVideoQuery.setSortBy(MaterialSortBy.fav);
                    break;
                }
                case COIN_DAILY: {
                    hotBiliVideoQuery.setSortBy(MaterialSortBy.coin);
                    break;
                }
                case DANMU_DAILY: {
                    hotBiliVideoQuery.setSortBy(MaterialSortBy.danmu);
                    break;
                }
                case SHARE_DAILY: {
                    hotBiliVideoQuery.setSortBy(MaterialSortBy.share);
                    break;
                }
                case LIKES_DAILY: {
                    hotBiliVideoQuery.setSortBy(MaterialSortBy.likes);
                    break;
                }
                default: {
                    hotBiliVideoQuery.setSortBy(MaterialSortBy.play);
                    break;
                }
            }
        } else {
            hotBiliVideoQuery.setSortBy(MaterialSortBy.play);
        }
        hotBiliVideoQuery.setPn(query.getPage());
        hotBiliVideoQuery.setPs(query.getSize());
        hotBiliVideoQuery.setBvidIn(query.getBvidIn());
        hotBiliVideoQuery.setBvidNotIn(query.getBvidNotIn());
        hotBiliVideoQuery.setUsingBlacklist(false);

        log.info("Star to query hot-bili-video by cpm-mng, hotBiliVideoQuery = {}", hotBiliVideoQuery);

        Pagination<List<HotBiliVideo>> resPage = hotBiliVideoService.page(hotBiliVideoQuery);
        List<HotBiliVideoDto> records = convertHotBiliVideo(resPage.getData());

        return PageResult.<HotBiliVideoDto>builder().total(resPage.getTotal_count()).records(records).build();
    }

    private List<HotBiliVideoDto> convertHotBiliVideo(List<HotBiliVideo> hotBiliVideoList) {
        List<HotBiliVideoDto> hotBiliVideoDtoList = new ArrayList<>();
        for (HotBiliVideo hotBiliVideo : hotBiliVideoList) {

            HotBiliVideoDto hotBiliVideoDto = BusinessPojoConverter.converter.toDto(hotBiliVideo);

//
//                    JSON.parseObject(JSON.toJSONString(hotBiliVideo),
//                    HotBiliVideoDto.class);
            hotBiliVideoDtoList.add(hotBiliVideoDto);
        }
        return hotBiliVideoDtoList;
    }
}
