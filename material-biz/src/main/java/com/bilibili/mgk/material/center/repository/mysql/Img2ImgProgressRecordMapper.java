package com.bilibili.mgk.material.center.repository.mysql;

import com.bilibili.mgk.material.center.service.aigc.model.Img2ImgProgressRecord;
import com.bilibili.mgk.material.center.service.aigc.model.Img2ImgProgressStatus;
import java.util.List;
import javax.annotation.Nullable;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/18
 */
public interface Img2ImgProgressRecordMapper {

    int deleteByPrimaryKey(Long id);


    int insertSelective(Img2ImgProgressRecord record);


    Img2ImgProgressRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Img2ImgProgressRecord record);


    List<Img2ImgProgressRecord> selectByAccountIdAndStatusInOrderByCtimeDesc(
            @Param("accountId") Long accountId,
            @Nullable @Param("progressStatus") List<Img2ImgProgressStatus> progressStatus,
            @Param("deleted") Boolean deleted
    );


}
