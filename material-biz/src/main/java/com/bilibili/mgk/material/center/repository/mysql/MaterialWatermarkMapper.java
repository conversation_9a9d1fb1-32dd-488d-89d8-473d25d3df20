package com.bilibili.mgk.material.center.repository.mysql;

import com.bilibili.mgk.material.center.service.creative.model.MaterialWatermark;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/4/1
 */
public interface MaterialWatermarkMapper {

    int deleteByPrimaryKey(Long id);

//    int insert(MaterialWatermark record);

    int insertSelective(MaterialWatermark record);

    int insertBatch(@Param("list") List<MaterialWatermark> batch);

    MaterialWatermark selectByPrimaryKey(Long id);

    int updateByMd5Selective(MaterialWatermark record);



    List<MaterialWatermark> selectAllByImgMd5(
            @Param("imgMd5s") List<String> imgMd5s
    );


}
