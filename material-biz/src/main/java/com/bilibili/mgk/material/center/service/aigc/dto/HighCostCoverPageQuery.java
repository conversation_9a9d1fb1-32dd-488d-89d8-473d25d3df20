package com.bilibili.mgk.material.center.service.aigc.dto;

import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/24
 */
@Data
@Accessors(chain = true)
public class HighCostCoverPageQuery implements SnakeCaseBody {


    private Long accountId;

    private Integer pn;

    private Integer ps;


    public void validate() {

    }
}
