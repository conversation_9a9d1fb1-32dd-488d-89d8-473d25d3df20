package com.bilibili.mgk.material.center.service.creative.model;

import com.bilibili.mgk.material.center.util.JsonUtil;
import java.util.Map;
import lombok.Data;

@Data
public class InefficientCreative {

    /**
     *
     */
    private Integer accountId;

    /**
     *
     */
    private String materialType;

    /**
     *
     */
    private String imageUrl;

    /**
     *
     */
    private String imageMd5;

    /**
     *
     */
    private String avid;

    /**
     *
     */
    private Integer creativeId;


    /**
     *
     */
    private String creativeTitle;


    /**
     *
     */
    private Integer unitId;

    /**
     *
     */
    private String unitName;

    /**
     *
     */
    private Integer campaignId;

    /**
     *
     */
    private String campaignName;

    /**
     *
     */
    private Double cost;

    /**
     *
     */
    private Integer convNum;

    /**
     *
     */
    private Double cpa;

    public static InefficientCreative fromMapValue(Map<String, String> mapValue) {

        return JsonUtil.fromJson(mapValue, InefficientCreative.class);

    }

}
