package com.bilibili.mgk.material.center.repository.mysql;

import com.bilibili.mgk.material.center.service.aigc.model.AIDerivationImageBlacklist;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/25
 */
public interface AIDerivationImageBlacklistMapper {


 int deleteByPrimaryKey(Long id);

 int insertSelective(AIDerivationImageBlacklist record);

 AIDerivationImageBlacklist selectByPrimaryKey(Long id);

 int updateByPrimaryKeySelective(AIDerivationImageBlacklist record);



}
