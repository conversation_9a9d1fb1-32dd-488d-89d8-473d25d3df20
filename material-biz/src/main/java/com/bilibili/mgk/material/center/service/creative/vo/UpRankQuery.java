package com.bilibili.mgk.material.center.service.creative.vo;

import com.bilibili.mgk.material.center.service.creative.model.FansGenderTag;
import com.bilibili.mgk.material.center.service.creative.model.GenderQuery;
import com.bilibili.mgk.material.center.service.creative.model.UpAuthType;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/30
 */
@Data
@Accessors(chain = true)
public class UpRankQuery implements SnakeCaseBody {

    private Long accountId;

    @ApiModelProperty(value = "搜索关键词")
    private String keyword;

    @ApiModelProperty(value = "搜索关键词类型")
    private QueryKeywordType keywordType;


    @ApiModelProperty(value = "主投一级分区", dataType = "string", example = "娱乐")
    private List<String> tidNames;


    @ApiModelProperty(value = "粉丝数大于，对于区间一律左开又闭，如果是>=0 , from 可不传，或者是传-1， 下同")
    private Integer fansFrom;

    @ApiModelProperty(value = "粉丝数小于等于")
    private Integer fansTo;

    /**
     * {@link com.bilibili.mgk.material.center.service.creative.model.UpRewardType}
     */
    @ApiModelProperty("开通类型， 0或不传全部， 1火花 2悬赏带货 ")
    private Integer rewardType;

    @ApiModelProperty("广告主一级行业，默认全部，支持多选")
    private List<String> firstIndustryNames;

    @ApiModelProperty("：默认不限，点击下拉不限、蓝V、普通")
    private UpAuthType upType;

    @ApiModelProperty("默认不限，点击下拉不限、男 male、女 female")
    private GenderQuery upGender;

    @ApiModelProperty("默认不限，点击下拉不限、LV1、LV2、LV3、LV4、LV5、LV6")
    private List<Integer> upLevels;

    @ApiModelProperty("默认不限，点击下拉不限、男粉多、女粉多")
    private FansGenderTag fansGender;

    @ApiModelProperty("0:(0,17], 1:[18,24], 2:[25, 30], 3:[31 *]")
    private Integer fansAgeRange;

    @ApiModelProperty("稿件数量大于等于")
    private Integer archiveNumFrom;

    @ApiModelProperty("稿件数小于等于")
    private Integer archiveNumTo;

    @ApiModelProperty("近10个稿件平均播放大于")
    private Long recent10PlayFrom;

    @ApiModelProperty("近10个稿件平均播放小于等于")
    private Long recent10PlayTo;

    @ApiModelProperty("近10个稿件互动率大于")
    private String recent10HudongRateFrom;


    @ApiModelProperty("近10个稿件互动率小于等于")
    private String recent10HudongRateTo;

    @ApiModelProperty("排序")
    private MaterialSortBy sortBy;

    @ApiModelProperty("up主id 批量用于对比")
    private List<String> upMids;

    @ApiModelProperty("页码")
    private Integer pn;

    @ApiModelProperty("每页数量")
    private Integer ps;


    public void validate() {

        Assert.notNull(pn, "pn不能为空");
        Assert.isTrue(pn > 0, "pn必须大于0");
        Assert.notNull(ps, "ps不能为空");
        Assert.isTrue(ps > 0, "ps必须大于0");
    }
}
