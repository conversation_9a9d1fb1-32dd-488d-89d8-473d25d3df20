package com.bilibili.mgk.material.center.service.sanlian.impl;

import com.bapis.ad.pandora.core.list.ListCampaignReq;
import com.bapis.ad.pandora.core.list.ListCampaignResp;
import com.bapis.ad.pandora.core.list.ListCreativeReq;
import com.bapis.ad.pandora.core.list.ListCreativeResp;
import com.bapis.ad.pandora.core.list.ListServiceGrpc;
import com.bapis.ad.pandora.core.list.ListUnitReq;
import com.bapis.ad.pandora.core.list.ListUnitResp;
import com.bapis.ad.pandora.core.v6.CopyUnitAndCreativesReq;
import com.bapis.ad.pandora.core.v6.CopyUnitAndCreativesResp;
import com.bapis.ad.pandora.core.v6.CreativeDiffInfo;
import com.bapis.ad.pandora.core.v6.CreativeImageInfo;
import com.bapis.ad.pandora.core.v6.MultiStageServiceGrpc;
import com.bapis.ad.pandora.core.v6.UnitDiffInfo;
import com.bapis.ad.pandora.resource.ImageInfo;
import com.bapis.ad.pandora.resource.ImageResourceServiceGrpc;
import com.bapis.ad.pandora.resource.ListImageReq;
import com.bapis.ad.pandora.resource.ListImageResp;
import com.bapis.ad.pandora.resource.SaveImageReq;
import com.bapis.ad.pandora.resource.SaveImageResp;
import com.bilibili.mgk.material.center.service.aigc.dto.AIDerivationImageDeliverReq;
import com.bilibili.mgk.material.center.service.aigc.model.CreativeCoverAIDerivationImage;
import com.bilibili.mgk.material.center.service.sanlian.SanlianProxyService;
import com.bilibili.mgk.material.center.service.sanlian.model.CreativeCopyDeliveryResult;
import io.vavr.Tuple;
import io.vavr.control.Try;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/25
 */
@Slf4j
@Service
public class SanlianInfoProxyServiceImpl implements SanlianProxyService {

    @Resource
    private MultiStageServiceGrpc.MultiStageServiceBlockingStub multiStageServiceBlockingStub;
    @Resource
    private ImageResourceServiceGrpc.ImageResourceServiceBlockingStub imageResourceServiceBlockingStub;
    @Resource
    private ListServiceGrpc.ListServiceBlockingStub listServiceBlockingStub;



    @Value("${material.aigc.derivation.deliver.unit-name-tpl:%s_爆量素材衍生}")
    private String unitNameTpl;

    @Value("${material.aigc.derivation.deliver.creative-name-tpl:%s_爆量素材衍生}")
    private String creativeNameTpl;


    @Value("${material.aigc.derivation.save-when-img-not-existed-enabled:true}")
    private Boolean saveWhenImgNotExistedEnabled;


    @Override
    public Map<String, ImageInfo> fetchImageDetailByMd5(Map<String, String> md5s) {

        ListImageResp resp = imageResourceServiceBlockingStub.listImage(
                ListImageReq.newBuilder()
                        .addAllMd5(md5s.keySet().stream()
                                .filter(StringUtils::isNotEmpty)
                                .distinct()
                                .collect(Collectors.toList())
                        ).build()
        );

        Map<String, ImageInfo> infoMap = resp.getImageList()
                .stream()
                .collect(Collectors.toMap(img -> img.getMd5(), img -> img, (a, b) -> a));

        return md5s.entrySet().stream()
                .map(md5 -> {

                    ImageInfo info = infoMap.get(md5.getKey());

                    if (info != null) {
                        return Tuple.of(md5.getKey(), info);
                    }

                    if (!saveWhenImgNotExistedEnabled) {
                        return null;
                    }

                    // 该接口本身不存在批量
                    SaveImageResp material = Try.of(() -> imageResourceServiceBlockingStub.saveImage(
                            SaveImageReq.newBuilder()
                                    .setUrl(md5.getValue()).build()
                    )).getOrNull();

                    if (material == null || material.getImage() == null) {
                        return null;
                    }

                    return Tuple.of(md5.getKey(), material.getImage());

                }).filter(Objects::nonNull)
                .collect(Collectors.toMap(tuple -> tuple._1, tuple -> tuple._2, (a, b) -> a));


    }


    /**
     * 1-投放中,2-账户余额不足,3-计划超预算,4-计划已暂停,5-单元超过预算,6-单元未在投放时段,7-单元已完成,8-单元已暂停,9-已暂停,10-已结束,11-已删除,12-审核中,13-审核拒绝,14-单元未开始,
     * 15-已完成,16-修改待下线,17-已下线
     *
     * @param creativeIds
     * @return
     */
    @Override
    public Map<Long, Integer> fetchCreativeStatusByCreativeIds(List<Long> creativeIds) {

        ListCreativeResp rsp = listServiceBlockingStub.listCreative(
                ListCreativeReq.newBuilder()
                        .addAllCreativeId(creativeIds.stream().map(id -> id.intValue())
                                .collect(Collectors.toList())).build()
        );

        return rsp.getCreativeInfoMap()
                .entrySet()
                .stream()
                .collect(Collectors.toMap(
                        entry -> entry.getKey().longValue(),
                        entry -> entry.getValue().getCreativeStatus()
                ));
    }


    /**
     * @param unitIds
     * @return 1-投放中,2-账户余额不足,3-计划超预算,4-计划已暂停,5-没有可投放的创意,6-已暂停,7-已结束,8-已删除,9-未开始,10-预算超限,11-不在投放时段,12-已完成,13-审核未通过,14-有效
     */
    @Override
    public Map<Long, Integer> fetchUnitStatusByUnitIds(List<Long> unitIds) {

        ListUnitResp rsp = listServiceBlockingStub.listUnit(
                ListUnitReq.newBuilder()
                        .addAllUnitId(unitIds.stream().map(id -> id.intValue())
                                .collect(Collectors.toList())).build()
        );

        return rsp.getUnitInfoMap()
                .entrySet()
                .stream()
                .collect(Collectors.toMap(
                        entry -> entry.getKey().longValue(),
                        entry -> entry.getValue().getUnitStatus()
                ));
    }


    /**
     * 1-投放中,2-账户余额不足,3-没有可投放的单元,4-已暂停,5-已结束,6-已删除,7-预算超限,8-有效
     *
     * @param campaignIds
     * @return
     */
    @Override
    public Map<Long, Integer> fetchCampaignStatusByCampaignIds(List<Long> campaignIds) {
        ListCampaignResp rsp = listServiceBlockingStub.listCampaign(
                ListCampaignReq.newBuilder()
                        .addAllCampaignId(campaignIds.stream().map(id -> id.intValue())
                                .collect(Collectors.toList())).build()
        );

        return rsp.getCampaignInfoMap()
                .entrySet()
                .stream()
                .collect(Collectors.toMap(
                        entry -> entry.getKey().longValue(),
                        entry -> entry.getValue().getCampaignStatus()
                ));

    }


    @Override
    public CreativeCopyDeliveryResult copyCreativeAndDeliver(
            CreativeCoverAIDerivationImage derivationImage,
            AIDerivationImageDeliverReq req) {

        return Try.of(() -> {
            // register and get material-id

            return req.getDeliverImages().stream().map(deliverImg -> {
                SaveImageResp material = imageResourceServiceBlockingStub.saveImage(
                        SaveImageReq.newBuilder()
                                .setUrl(deliverImg.getImgUrl()).build()
                );

                return Tuple.of(material.getImage(), deliverImg.getImgUrl());


            }).collect(Collectors.toList());


        }).map(materialIdAndUrlList -> {

            String deliveryCreativeName = String.format(creativeNameTpl, derivationImage.getCreativeName());

            String deliveryUnitName = String.format(unitNameTpl, derivationImage.getUnitName());

            CopyUnitAndCreativesResp rsp = multiStageServiceBlockingStub.copyUnitAndCreatives(
                    CopyUnitAndCreativesReq.newBuilder()
                            .setOperator(req.getOperator())
                            .setTargetCampaignId(req.getCampaignId().intValue())
                            .setUnitDiff(UnitDiffInfo.newBuilder()
                                    .setUnitName(deliveryUnitName)
                                    .setSourceUnitId(req.getUnitId().intValue())
                                    .build())
                            .addCreativeDiff(CreativeDiffInfo.newBuilder()
                                    .setCreativeName(deliveryCreativeName)
                                    .setSourceCreativeId(req.getCreativeId().intValue())
                                    .addAllImage(materialIdAndUrlList.stream().map(materialIdAndUrl -> {

                                        ImageInfo image = materialIdAndUrl._1;

                                        return CreativeImageInfo.newBuilder()
                                                .setUrl(materialIdAndUrl._2)
                                                .setMaterialId(image.getMaterialId())
                                                .build();
                                    }).collect(Collectors.toList()))
                                    .build()

                            )
                            .build()
            );

            return new CreativeCopyDeliveryResult()
                    .setUnitId((long) rsp.getUnitId())
                    .setCreativeId(rsp.getCreativeIdList().get(0).longValue())
                    .setCreativeName(deliveryCreativeName)
                    .setUnitName(deliveryUnitName);


        }).getOrElseThrow(t -> {
            log.error("Fail to copyCreativeAndDeliver, req={}", req, t);
            return new RuntimeException("复制和投放衍生图创意失败," + t.getMessage(), t);
        });


    }

//    @Override
//    public DummyUnitCreateResult createDummyUnit(DummyUnitCreateReq req) {
//        return null;
//    }
//
//    @Override
//    public DummyCreativeCreateResult createDummyCreative(DummyCreativeCreateReq req) {
//        return null;
//    }
//
//
//    @Override
//    public DummyCreativeCreateResult createBatchDummyCreativeForComponent(BatchDummyCreativeCreateReq req) {
//
//        //  plan1
//
//        DummyUnitCreateResult unit = sanlianProxyService.createDummyUnit(
//                new DummyUnitCreateReq()
//                        .setUnitName("")
//                        .setCampaignName("")
//        );
//
//        DummyCreativeCreateResult creatives = sanlianProxyService.createDummyCreative(
//                new DummyCreativeCreateReq()
//                        .setCampaignId(unit.getCampaignId())
//                        .setUnitId(unit.getUnitId())
//                        .setCreativeNames(IntStream.range(0, bluelinks.size()).mapToObj(index -> {
//                            return String.format("动态正文蓝链创意:%s,%s", index, bluelinks.get(index).getRawText());
//                        }).collect(Collectors.toList()))
//        );
//
//        //////////////plan2
//
//        //构造空计划
//        LauCampaignPo campaign = new LauCampaignPo();
//        campaign.setAccountId(defaultAccountId);
//        Integer campaignId = baseQueryFactory.insert(lauCampaign).insertGetKey(campaign);
//        //构造空单元
//        LauUnitPo unit = new LauUnitPo();
//        unit.setAccountId(defaultAccountId);
//        unit.setCampaignId(campaignId);
//        Integer unitId = baseQueryFactory.insert(lauUnit).insertGetKey(unit);
//        //构造空创意
//        Integer count = 0;
//        if (!CollectionUtils.isEmpty(bo.getDynamicIds())) {
//            count = bo.getDynamicIds().size();
//        } else if (!CollectionUtils.isEmpty(bo.getAvids())) {
//            count = bo.getAvids().size();
//        }
//        List<LauUnitCreativePo> creativeList = new ArrayList<>();
//        for (Integer i = 0; i < count; i++) {
//            LauUnitCreativePo creative = new LauUnitCreativePo();
//            creative.setAccountId(defaultAccountId);
//            creative.setCampaignId(campaignId);
//            creative.setUnitId(unitId);
//            creative.setCreativeType(0);
//            creative.setTemplateId(0);
//            creative.setAuditStatus(0);
//            creative.setStatus(0);
//            creativeList.add(creative);
//        }
//
//        List<Integer> creativeIdList = baseQueryFactory.insert(lauUnitCreative).insertGetKeys(creativeList);
//    }
}
