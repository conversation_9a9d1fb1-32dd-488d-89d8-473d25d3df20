package com.bilibili.mgk.material.center.service.asset;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.mgk.material.center.service.asset.model.MaterialDynamicId;
import com.bilibili.mgk.material.center.service.asset.model.MaterialDynamicInfo;
import com.bilibili.mgk.material.center.service.asset.vo.MaterialDynamicDrawDeleteReq;
import com.bilibili.mgk.material.center.service.asset.vo.MaterialDynamicDrawPublishReq;
import com.bilibili.mgk.material.center.service.asset.vo.MaterialDynamicPageQuery;
import com.bilibili.mgk.material.center.service.asset.vo.MaterialDynamicWaterfallPageQuery;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.bilibili.mgk.material.center.service.creative.vo.WaterfallPage;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/6/18
 */
public interface DynamicAssetService {


    /**
     * 使用授权的账号代理发布图文动态
     * @param req
     */
    MaterialDynamicId publish(MaterialDynamicDrawPublishReq req, Operator operator);

    /**
     * 列表展示账号的图文动态
     *
     * @return query
     */
    Pagination<List<MaterialDynamicInfo>> list(MaterialDynamicPageQuery query);


    WaterfallPage<MaterialDynamicInfo> list(MaterialDynamicWaterfallPageQuery query);




    List<MaterialDynamicId> triggerSchedulePublishTask();


    MaterialDynamicId delete(MaterialDynamicDrawDeleteReq deleteReq);
}
