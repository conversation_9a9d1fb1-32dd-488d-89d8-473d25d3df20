package com.bilibili.mgk.material.center.service.creative.model;

import com.bilibili.mgk.material.center.facade.converter.RisingVideoAdItem180DayAccumulationOnesDTO;
import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 飙升视频推广的商品
 *
 * <AUTHOR>
 * @desc
 * @date 2024/6/27
 */
@Data
@Accessors(chain = true)
public class RisingVideoAdItem implements SnakeCaseBody {

    @ApiModelProperty(value = "数据就绪时间")
    private String dataReadyTime;


    /**
     * hive表中日志时间
     */
    @ApiModelProperty(value = "日志时间")
    private String logDate;

    @JsonIgnore
    private LocalDate parsedLogDate;

    /**
     *
     */
    @ApiModelProperty(value = "商品id")
    private String itemId;


    @ApiModelProperty(value = "商品图片")
    private String itemImageUrl;

    private String itemName;

    @ApiModelProperty(value = "商品来源", example = "1")
    private String itemSource;


    @ApiModelProperty(value = "商品品牌")
    private String itemTitle;


    @ApiModelProperty(value = "商品品牌")
    private String itemBrandName;

    @ApiModelProperty(value = "商品原价")
    private String originalPrice;

    @ApiModelProperty(value = "商品现价")
    private String currentPrice;


    @ApiModelProperty(value = "商品详情页url")
    private String itemJumpUrl;

    @ApiModelProperty(value = "商品来源描述", example = "淘宝")
    private String itemSourceDesc;


    /**
     * distinct up count
     */
    @ApiModelProperty(value = "up主数量，day_type时间区间内")
    private Long upCountIncr;


    /**
     * distinct up
     */
    @ApiModelProperty(value = "关联视频数量，day_type时间区间内")
    private Long videoCountIncr;


    @ApiModelProperty(value = "增量粉丝数，day_type时间区间内")
    private Long fansIncr;


    @ApiModelProperty(value = "增量播放数，day_type时间区间内")
    private Long playIncr;


    @ApiModelProperty(value = "增量点赞数，day_type时间区间内")

    private Long likesIncr;


    @ApiModelProperty(value = "增量评论数，day_type时间区间内")
    private Long replyIncr;

    @ApiModelProperty(value = "增量弹幕数，day_type时间区间内")
    private Long danmuIncr;

    @ApiModelProperty(value = "增量投币数，day_type时间区间内")
    private Long coinIncr;

    @ApiModelProperty(value = "增量收藏数，day_type时间区间内")
    private Long favIncr;

    @ApiModelProperty(value = "增量分享数，day_type时间区间内")
    private Long shareIncr;


    @ApiModelProperty(value = "up主数量， 最近180天累积")

    private Long upCount;

    @ApiModelProperty(value = "关联视频数量， 最近180天累积")

    private Long videoCount;

    /// 视频动态聚合数据


    /**
     * 播放
     */
    @ApiModelProperty(value = "播放量， 最近180天累积")
    private Long play;


    /**
     * 粉丝数
     */
    @ApiModelProperty(value = "粉丝数， 最近180天累积")
    private Long fans;


    /**
     * 点赞
     */
    @ApiModelProperty(value = "点赞量， 最近180天累积")
    private Long likes;


    /**
     * 回复
     */
    @ApiModelProperty(value = "评论数， 最近180天累积")
    private Long reply;

    /**
     * 弹幕
     */
    @ApiModelProperty(value = "弹幕数， 最近180天累积")
    private Long danmu;

    /**
     * 硬币
     */
    @ApiModelProperty(value = "投币数， 最近180天累积")
    private Long coin;


    /**
     * 收藏
     */
    @ApiModelProperty(value = "收藏数， 最近180天累积")
    private Long fav;


    /**
     * 分享
     */
    @ApiModelProperty(value = "分享数， 最近180天累积")
    private Long share;


    public void fillingAccData(RisingVideoAdItem180DayAccumulationOnesDTO accData) {
        this.upCount = accData.getUpCount();
        this.videoCount = accData.getVideoCount();
        this.play = accData.getPlay();
        this.fans = accData.getFans();
        this.likes = accData.getLikes();
        this.reply = accData.getReply();
        this.danmu = accData.getDanmu();
        this.coin = accData.getCoin();
        this.fav = accData.getFav();
        this.share = accData.getShare();
    }


}
