package com.bilibili.mgk.material.center.service.mainsite;

import com.bapis.account.service.Info;
import com.bapis.account.service.v2.UserInfo;
import com.bilibili.mgk.material.center.service.creative.model.HotBiliVideo;
import com.bilibili.mgk.material.center.service.creative.model.RisingFastVideo;
import com.bilibili.mgk.material.center.service.creative.model.UpRankInfo;
import com.bilibili.mgk.material.center.service.creative.model.UpRankVideoInfo;
import com.google.common.collect.Lists;
import io.vavr.control.Try;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/2
 */
public interface BiliAccountService {

    Logger log = LoggerFactory.getLogger(BiliAccountService.class);

    Map<Long, UserInfo> findBasicUserInfo(List<Long> mids, boolean needCtrlInfo);

    default Optional<UserInfo> findBasicUserInfoByMid(Long mid) {

        return Optional.ofNullable(findBasicUserInfo(Lists.newArrayList(mid), false).get(mid));
    }


    default List<UpRankInfo> fillingUpFaceAndFilterOutInvalidMidOfUpRankInfo(List<UpRankInfo> upRankInfos) {

        return this.fillingUpAvatarAndFilterOutInvalidMids(
                upRankInfos, UpRankInfo::getUpMid, UpRankInfo::setUpFace
        );
    }

    default void fillingUpFaceOfUpRankVideoInfo(List<UpRankVideoInfo> upRankInfos) {
        this.fillingUpAvatar(
                upRankInfos, UpRankVideoInfo::getUpMid, UpRankVideoInfo::setUpFace
        );
    }


    default void fillingUpAvatarOfRisingVideo(List<RisingFastVideo> risingVideo){

        this.fillingUpAvatar(risingVideo, RisingFastVideo::getUpMid, RisingFastVideo::setUpAvatar);

    }

    default void fillingUpAvatarOfHotVideo(List<HotBiliVideo> hotBiliVideos) {
        this.fillingUpAvatar(hotBiliVideos, HotBiliVideo::getUpMid, HotBiliVideo::setUpAvatar);
    }


    default <T> void fillingUpAvatar(List<T> videos,
            Function<T, Long> midGetter,
            BiConsumer<T, String> avatarSetter
    ) {
        Try.run(() -> {
            List<Long> mids = videos.stream().map(midGetter)

                    .filter(Objects::nonNull)
                    .distinct().collect(Collectors.toList());

            Map<Long, UserInfo> userInfos = findBasicUserInfo(mids, false);

            videos.stream().forEach(video -> {

                Long mid = midGetter.apply(video);

                Optional.ofNullable(userInfos.get(mid))
                        .map(UserInfo::getBaseInfo)
                        .map(Info::getFace)
                        .ifPresent(face -> {
                            avatarSetter.accept(video, face);
                        });

            });
        }).onFailure(t -> {
            log.error("Fail to fillingUpAvatar error, data={}", videos, t);
        });
    }


    <T> List<T> fillingUpAvatarAndFilterOutInvalidMids(List<T> videos,
            Function<T, Long> midGetter,
            BiConsumer<T, String> avatarSetter);
}
