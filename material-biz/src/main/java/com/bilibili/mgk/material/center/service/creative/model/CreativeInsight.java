package com.bilibili.mgk.material.center.service.creative.model;

import java.util.Optional;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CreativeInsight {

    //// 目标dayType 的统计数据

    private Integer accountId;

    private String materialType;

    /**
     * 账户素材总数
     */
    private Integer materialTotalNum;


    /**
     * 账户在投素材数
     */
    private Integer materialDeliveryNum;

    /**
     * 账户在投低效素材数
     */
    private Integer materialInefficientNum;

    private Integer materialEfficientNum;


    private String dayType;


    /**
     * 最新更新时间
     */
    private String latestUpdateTime;

    //////// 7d的统计数据

    private Integer materialTotalNum7d;

    /**
     * 账户在投素材数
     */
    private Integer materialDeliveryNum7d;

    /**
     * 账户在投低效素材数
     */
    private Integer materialInefficientNum7d;


    /**
     * 在投素材消耗
     */
    @Deprecated
    private Double materialDeliveryCost;

    /**
     * 低效素材消耗
     */
    @Deprecated
    private Double materialInefficientCost;


    /**
     * 在投素材平均消耗
     */
    private Double materialDeliveryAverageCost;

    /**
     * 低效素材平均消耗
     */
    private Double materialInefficientAverageCost;


    /**
     * 在投素材平均消耗
     */
    private Double materialDeliverySumCost;

    /**
     * 低效素材平均消耗
     */
    private Double materialInefficientSumCost;


    private Double materialEfficientSumCost;


    /**
     * 低效素材总结话术
     */
    private String inefficientDescription;


    /**
     * 非低效素材平均开销
     */
    private Double materialEfficientAverageCost;


    /**
     * 非低效素材开销
     */
    @Deprecated
    private Double materialEfficientCost;


    private Integer inefficientRelatedCreativeNum;


    public void setAverage() {

        this.setMaterialEfficientNum(
                getMaterialDeliveryNum() - getMaterialEfficientNum()

        );
        this.setMaterialEfficientSumCost(
                getMaterialDeliverySumCost() - getMaterialInefficientSumCost()

        );

        this.setMaterialInefficientAverageCost(
                getMaterialInefficientNum() == 0 ? null :
                        (getMaterialInefficientSumCost() / getMaterialInefficientNum())
        );

        this.setMaterialEfficientAverageCost(
                getMaterialEfficientNum() == 0 ? null :
                        (getMaterialEfficientSumCost() / getMaterialEfficientNum())
        );
        this.setMaterialDeliveryAverageCost(
                getMaterialDeliveryNum() == 0 ? null :
                        (getMaterialDeliverySumCost() / getMaterialDeliveryNum())
        );

    }

//    public Double getMaterialEfficientAverageCost() {
//
//        if (materialEfficientAverageCost == null) {
//
//            if (this.getMaterialInefficientAverageCost() != null
//                    && this.getMaterialDeliveryAverageCost() != null) {
//
//            }
//            this.materialEfficientAverageCost =
//                    this.getMaterialDeliveryAverageCost() - this.getMaterialInefficientAverageCost();
//
//        }
//
//        return materialEfficientAverageCost;
//    }
//
//    public Double getMaterialEfficientCost() {
//
//        if(materialEfficientCost == null) {
//            if (this.getMaterialDeliveryCost() != null && this.getMaterialInefficientCost() != null) {
//                this.materialEfficientCost =
//                        this.getMaterialDeliveryCost() - this.getMaterialInefficientCost();
//
//            }
//        }
//
//        return materialEfficientCost;
//    }


    public Integer getMaterialTotalNum() {
        return Optional.ofNullable(materialTotalNum).orElse(0);
    }

    public Integer getMaterialDeliveryNum() {
        return Optional.ofNullable(materialDeliveryNum).orElse(0);
    }

    public Integer getMaterialInefficientNum() {
        return Optional.ofNullable(materialInefficientNum).orElse(0);
    }

    public Integer getMaterialEfficientNum() {
        return Optional.ofNullable(materialEfficientNum).orElse(0);
    }


    public Double getMaterialDeliverySumCost() {
        return Optional.ofNullable(materialDeliverySumCost).orElse(0.0);
    }

    public Double getMaterialInefficientSumCost() {
        return Optional.ofNullable(materialInefficientSumCost).orElse(0.0);
    }

    public Double getMaterialEfficientSumCost() {
        return Optional.ofNullable(materialEfficientSumCost).orElse(0.0);
    }

}
