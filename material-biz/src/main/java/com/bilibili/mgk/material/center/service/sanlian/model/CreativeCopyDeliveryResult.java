package com.bilibili.mgk.material.center.service.sanlian.model;

import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/25
 */
@Data
@Accessors(chain = true)
public class CreativeCopyDeliveryResult implements SnakeCaseBody {


    private Long creativeId;


    private Long unitId;

//    private Long campaignId;

    private String creativeName;

    private String unitName;

}
