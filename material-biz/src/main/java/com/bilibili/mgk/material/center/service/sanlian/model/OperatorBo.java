package com.bilibili.mgk.material.center.service.sanlian.model;

import com.bilibili.adp.common.bean.Operator;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OperatorBo {

    private Integer operatorId;
    private String operatorName;
    private Integer operatorType;
    private Integer systemType;
    private String bilibiliUserName;
    private Integer flag;


    public static OperatorBo fromCommonBeanOperator(Operator operator) {
        return new OperatorBo()
                .setOperatorId(operator.getOperatorId())
                .setOperatorName(operator.getOperatorName())
                .setOperatorType(operator.getOperatorType().getCode())
                .setSystemType(operator.getSystemType().getCode())
                .setBilibiliUserName(operator.getBilibiliUserName())
                ;
    }

}
