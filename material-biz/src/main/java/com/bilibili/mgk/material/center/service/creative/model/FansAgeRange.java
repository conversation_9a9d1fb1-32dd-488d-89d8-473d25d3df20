package com.bilibili.mgk.material.center.service.creative.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 沿用主站的 0:(0,17], 1:[18,24], 2:[25, 30], 3:[31 *], 空为未知
 *
 * <AUTHOR>
 * @desc
 * @date 2024/8/30
 */
@Getter
@RequiredArgsConstructor
public enum FansAgeRange {


    age0_17(0, "0-17"),

    age18_24(1, "18-24"),

    age25_30(2, "25-30"),

    age31_up(3, "31-infinite"),
    ;


    private final int code;

    private final String desc;


    public static FansAgeRange getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (FansAgeRange fansAgeRange : values()) {
            if (fansAgeRange.getCode() == code) {
                return fansAgeRange;
            }
        }

        return null;

    }


}
