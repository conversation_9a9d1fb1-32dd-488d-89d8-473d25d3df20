package com.bilibili.mgk.material.center.facade;

import com.bapis.datacenter.service.oneservice.OperatorVo;
import com.bapis.datacenter.service.oneservice.OsHeader;
import com.bapis.datacenter.service.oneservice.PageReq;
import com.bapis.datacenter.service.oneservice.PageVo;
import com.bapis.datacenter.service.oneservice.QueryReq;
import com.bapis.datacenter.service.oneservice.QueryResp;
import com.bilibili.mgk.material.center.facade.proxy.OneServiceFlowControlProxy;
import com.bilibili.mgk.material.center.service.creative.model.InefficientCreative;
import com.bilibili.mgk.material.center.service.creative.model.MaterialType;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.google.common.collect.Lists;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 低效创意明细 /inefficient/page
 */
@Slf4j
@Service
public class Inefficient7dDetailFacade {

    @Resource
    private OneServiceFlowControlProxy serviceOpenApiManagerBlockingStub;

    @Resource
    private ApiOneServiceBase apiOneServiceBase;

    private DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Value("${lowefficent.app-key}")
    private String appKey;

    @Value("${lowefficent.secret}")
    private String secret;

    @Value("${lowefficent.api-id}")
    private String apiId;


    public Pagination<List<InefficientCreative>> query(Integer accountId, MaterialType materialType, Integer pageNum,
            Integer pageSize) {

        OsHeader osHeader = OsHeader.newBuilder()
                .setAppKey(appKey)
                .setSecret(secret)
                .setApiId(apiId)
                .build();
        QueryReq openApiReq = QueryReq.newBuilder()
                .setOsHeader(osHeader)
                .addReqs(OperatorVo.newBuilder().setField("account_id").setOperator("=")
                        .addAllValues(Lists.newArrayList(accountId.toString())))
                .addReqs(OperatorVo.newBuilder().setField("log_date").setOperator("=")
                        .addAllValues(Lists.newArrayList(logDate())))
                .addReqs(OperatorVo.newBuilder().setField("material_type").setOperator("in")
                        .addAllValues(materialType.isImage() ?
                                Lists.newArrayList(MaterialType.img.getMaterialTypeId().toString(),
                                        MaterialType.gif.getMaterialTypeId().toString()) :
                                Lists.newArrayList(materialType.getMaterialTypeId().toString())
                        ))
                .addAllOrders(Lists.newArrayList("cost desc"))
                .setPageReq(PageReq.newBuilder().setPage(pageNum).setPageSize(pageSize).build())
                .build();

        QueryResp rsp = serviceOpenApiManagerBlockingStub.query(openApiReq);

        List<InefficientCreative> result = rsp.getRowsList().stream()
                .map(map -> InefficientCreative.fromMapValue(map.getValueMap()))
                .collect(Collectors.toList());

        PageVo page = rsp.getPageVo();

        return new Pagination<>(page.getPage(), (int) page.getTotalSize(), result);
    }

    private String logDate() {
        return apiOneServiceBase.findLatestOpenApiLogDate(apiId);
    }
}
