package com.bilibili.mgk.material.center.repository.clickhouse;


import com.bilibili.mgk.material.center.service.creative.model.InspirationCase;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 针对表【advertisement_analysis】的数据库操作Mapper
 * @createDate 2024-03-05 15:46:39
 * @Entity generator.domain.AdvertisementAnalysis
 * @deprecated 目前保留仅做为 clickhouse-jdbc+ mybatis的使用示例
 */
@Deprecated
public interface ClickhouseAdInspirationCaseMapper {

    int deleteByPrimaryKey(Long id);

    int insert(InspirationCase record);

    int insertSelective(InspirationCase record);

    InspirationCase selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(InspirationCase record);

    int updateByPrimaryKey(InspirationCase record);


    List<InspirationCase> selectAll();

    List<InspirationCase> selectById(@Param("where") String where);


}
