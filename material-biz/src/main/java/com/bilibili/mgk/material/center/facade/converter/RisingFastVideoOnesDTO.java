package com.bilibili.mgk.material.center.facade.converter;

import com.bilibili.mgk.material.center.service.creative.model.RisingFastVideo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * log_date, avid, avid_title, avid_cover, avid_content, avid_tag, avid_tid_name, is_vertical_screen, avid_duration,
 * avid_pubtime, up_mid, up_name, verify_type, verify_type_desc, fans_daily, fans, play_daily, likes_daily, reply_daily,
 * danmu_daily, coin_daily, fav_daily, share_daily, play, likes, reply, danmu, coin, fav, share,
 * commerce_category_first_name, product_name, item_id, item_image_url, item_name, item_source, first_category,
 * place_type, avid_app_id, avid_app_url, avid_app_name, avid_ad_type
 * <p>
 * <p>
 * avid string 视频avid 机密数据(C3) 1 avid_title string 视频标题 机密数据(C3) 2 avid_cover string 视频封面 机密数据(C3) 3 avid_content string
 * 视频简介 机密数据(C3) 4 avid_tag string 视频标签 机密数据(C3) 5 avid_tid_name string 视频一级分区 机密数据(C3) 6 is_vertical_screen bigint
 * 是否竖版视频 1是0不是 机密数据(C3) 7 avid_duration bigint 视频时长 秒 机密数据(C3) 8 avid_pubtime string 视频发布时间 机密数据(C3) 9 up_mid string
 * up主mid 机密数据(C3) 10 up_name string up主昵称 机密数据(C3) 11 verify_type bigint 认证类型 机密数据(C3) 12 verify_type_desc string
 * 认证类型描述 机密数据(C3) 13 fans_daily bigint 日增粉丝数 机密数据(C3) 14 fans bigint 累计粉丝数 机密数据(C3) 15 play_daily bigint 日增播放 机密数据(C3)
 * 16 likes_daily bigint 日增点赞 机密数据(C3) 17 reply_daily bigint 日增评论 机密数据(C3) 18 danmu_daily bigint 日增弹幕 机密数据(C3) 19
 * coin_daily bigint 日增投币 机密数据(C3) 20 fav_daily bigint 日增收藏 机密数据(C3) 21 share_daily bigint 日增分享 机密数据(C3) 22 play bigint
 * 累计播放 机密数据(C3) 23 likes bigint 累计点赞 机密数据(C3) 24 reply bigint 累计评论 机密数据(C3) 25 danmu bigint 累计弹幕 机密数据(C3) 26 coin
 * bigint 累计投币 机密数据(C3) 27 fav bigint 累计收藏 机密数据(C3) 28 share bigint 累计分享 机密数据(C3) 29 commerce_category_first_name string
 * 广告一级行业 机密数据(C3) 30 product_name string 广告品牌信息 机密数据(C3) 31 item_id string 商品id 机密数据(C3) 32 item_image_url string 商品封面图
 * 机密数据(C3) 33 item_name string 商品名称 机密数据(C3) 34 item_source string 商品来源 机密数据(C3) 35 first_category string 带货一级类目
 * 机密数据(C3) 36 place_type string 带货方式 机密数据(C3) 37 avid_app_id string 视频应用id 机密数据(C3) 38 avid_app_url string 视频应用url
 * 机密数据(C3) 39 avid_app_name string 视频应用名称 机密数据(C3) 40 avid_ad_type bigint 视频应用类型
 *
 * <AUTHOR>
 * @desc
 * @date 2024/6/25
 */
@Data
@Accessors(chain = true)
public class RisingFastVideoOnesDTO {


    /**
     * 日期
     */
    private String logDate;


    /**
     * avid
     */
    private Long avid;

    // avid 静态数据

    /**
     * 视频标题
     */
    private String avidTitle;

    /**
     * 视频封面
     */
    private String avidCover;

    /**
     * 视频简介
     */
    private String avidContent;

    /**
     * 视频标签
     */
    private String avidTag;

    /**
     * 视频一级分类，e.g 游戏、番剧
     */
    private String avidTidName;

    /**
     * 是否竖屏
     */
    private Integer isVerticalScreen;

    /**
     * 视频时长
     */
    private Long avidDuration;


    /**
     * 视频发布时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime avidPubtime;


    /**
     * 视频up主
     */
    private Long upMid;

    /**
     * 视频up主昵称
     */
    private String upName;

    /**
     * up认证
     */
    private Integer verifyType;

    /**
     * 认证类型描述
     */
    private String verifyTypeDesc;


    /**
     * 增量播放， 是play_daily的聚合，所以换了个字段名
     */
    private Long playIncr;

    /**
     *播放
     */
    private Long play;


    /**
     * 增量粉丝
     */
    private Long fansIncr;

    /**
     * 粉丝数
     */
    private Long fans;


    /**
     * 增量点赞
     */

    private Long likesIncr;


    /**
     * 点赞
     */
    private Long likes;

    /**
     * 增量回复
     */

    private Long replyIncr;

    /**
     * 回复
     */
    private Long reply;


    /**
     * 增量弹幕
     */
    private Long danmuIncr;


    /**
     * 弹幕
     */
    private Long danmu;

    // 投币数、收藏数、分享数、互动率=（点赞量+评论数+弹幕数+投币数+分享数）/ 播放量、播粉比=播放量/粉丝数

    /**
     * 增量硬币
     */
    private Long coinIncr;

    /**
     * 硬币
     */
    private Long coin;


    /**
     * 增量收藏
     */
    private Long favIncr;

    /**
     * 收藏
     */
    private Long fav;

    /**
     *增量分享
     */

    private Long shareIncr;

    /**
     * 分享
     */
    private Long share;


    /**
     * 增量互动， sum(点赞量+评论数+弹幕数+投币数+分享数)
     */
    private Long hudongIncr;

    /**
     * 互动
     */
    private Long hudong;

    //// 商品静态信息
    /**
     * 广告一级行业 是相应广告主的一级行业和品牌
     */
    private String commerceCategoryFirstName;

    /**
     * 广告品牌信息
     */
    private String productName;

    /**
     * 商品id
     */
    private String itemId;

    /**
     * 商品封面图
     */
    private String itemImageUrl;

    /**
     * 商品名称
     */
    private String itemName;


    /**
     * 商品来源 2 ， 104
     */
    private String itemSource;

    private String itemTitle;



    private String itemBrandName;

    private String originalPrice;

    private String currentPrice;

    private String itemJumpUrl;



    /**
     * 带货一级类目	，食品饮料
     */

    private String firstCategory;

    /**
     * 带货方式, 蓝链，非蓝链
     */
    private String placeType;


    /**
     * 视频应用id
     */
    private String avidAppId;


    /**
     * 视频应用url
     */
    private String avidAppUrl;


    /**
     * 视频应用名称
     */
    private String avidAppName;


    /**
     * 视频应用类型	 1是商品2是应用3是落地页
     */
    private Long avidAdType;





    public RisingFastVideo toBO() {
        return OneServiceDTOConverter.converter.toBO(this);
    }


}
