package com.bilibili.mgk.material.center.facade.converter;

import com.bilibili.mgk.material.center.service.creative.model.CreativeInsight;
import com.bilibili.mgk.material.center.util.JsonUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/29
 */
@Data
public class CreativeInsightOnesDTO {

    //// 目标dayType 的统计数据

    private Integer accountId;

    private String materialType;

    /**
     * 账户素材总数
     */
    private Integer materialTotalNum;

    /**
     * 账户在投素材数
     */
    private Integer materialDeliveryNum;

    /**
     * 账户在投低效素材数
     */
    private Integer materialInefficientNum;


    private String dayType;


    /**
     * 在投素材消耗
     */
    private Double materialDeliveryCost;

    /**
     * 低效素材消耗
     */
    private Double materialInefficientCost;


    /**
     * 在投素材平均消耗
     */
    @JsonProperty("material_delivery_average_cost")
    private Double materialDeliverySumCost;

    /**
     * 低效素材平均消耗
     */
    @JsonProperty("material_inefficient_average_cost")
    private Double materialInefficientSumCost;


    public static CreativeInsightOnesDTO fromMapValue(Map<String, String> mapValue) {

        return JsonUtil.fromJson(mapValue, CreativeInsightOnesDTO.class);

    }

    public CreativeInsight toBO() {
        return OneServiceDTOConverter.converter.toCreativeInsight(this);
    }

}
