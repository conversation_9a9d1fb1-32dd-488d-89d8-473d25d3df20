package com.bilibili.mgk.material.center.soa;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.mgk.material.center.service.converter.BusinessPojoConverter;
import com.bilibili.mgk.material.center.service.creative.CreativeMaterialService;
import com.bilibili.mgk.material.center.service.creative.model.CreativeMaterial;
import com.bilibili.mgk.material.center.service.creative.vo.CreativeMaterialQuery;
import com.bilibili.mgk.material.center.service.creative.vo.CreativeMaterialQuery.IndustrySubConditions;
import com.bilibili.mgk.material.center.service.creative.vo.DateAggregation;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialSortBy;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.bilibili.mgk.material.center.service.creative.vo.QueryKeywordType;
import com.bilibili.mgk.material.center.service.creative.vo.SceneType;
import com.bilibili.mgk.platform.api.hot_ads.dto.HotAdsDtoV2;
import com.bilibili.mgk.platform.api.hot_ads.dto.QueryHotAdsDtoV2;
import com.bilibili.mgk.platform.api.hot_ads.soa.ISoaHotAdsServiceV2;
import com.bilibili.mgk.platform.common.MgkHotAdsOrderByEnum;
import io.vavr.control.Try;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/18
 */
@Slf4j
@Service
public class SoaHotAdsServiceImplV2 implements ISoaHotAdsServiceV2 {

    @Resource
    private CreativeMaterialService creativeMaterialService;

    @Override
    public PageResult<HotAdsDtoV2> getHotAdsDtosV2(Operator operator, QueryHotAdsDtoV2 queryHotAdsDto) {
        log.info("Start to query hot-ads materials by cpm-mng, query={}", queryHotAdsDto);

        Pagination<List<CreativeMaterial>> page = creativeMaterialService.page(
                new CreativeMaterialQuery()
                        .setIndustryFilters(
                                Optional.ofNullable(queryHotAdsDto.getIndustryList())
                                        .map(industries -> {
                                            return industries.stream().collect(Collectors.toMap(
                                                    industry -> industry, industry -> new IndustrySubConditions(),
                                                    (a, b) -> a
                                            ));
                                        })
                                        .orElse(null)
                        )
                        .setTargetAccountId(queryHotAdsDto.getAccountId())
                        .setTargetAgentId(queryHotAdsDto.getAgentId())
                        .setTargetCustomerId(queryHotAdsDto.getCustomerId())
                        .setTargetAccountName(queryHotAdsDto.getAccountName())
                        .setTargetAgentName(queryHotAdsDto.getAgentName())
                        .setTargetCustomerName(queryHotAdsDto.getCustomerName())
                        .setSortBy(Try.of(() -> {
                            Integer order = queryHotAdsDto.getOrderType();

                            if (MgkHotAdsOrderByEnum.CTR.getCode().equals(queryHotAdsDto.getOrderType())) {
                                return MaterialSortBy.ctr;
                            } else if (MgkHotAdsOrderByEnum.PV.getCode().equals(order)) {
                                return MaterialSortBy.pv;
                            } else if (MgkHotAdsOrderByEnum.CVR.getCode().equals(order)) {
                                return MaterialSortBy.cvr;
                            } else if (MgkHotAdsOrderByEnum.CTCVR.getCode().equals(order)) {
                                return MaterialSortBy.ctcvr;
                            } else {
                                return MaterialSortBy.ctr;
                            }
                        }).get())
                        .setKeywordType(QueryKeywordType.ad_title)
                        .setKeyword(Try.of(() -> {
                            if (CollectionUtils.isEmpty(queryHotAdsDto.getCreativeTitles())) {
                                return null;
                            }
                            return queryHotAdsDto.getCreativeTitles().get(0);
                        }).getOrNull())
                        .setDayType(Try.of(() -> {
                            if (queryHotAdsDto.getDateType() == 1) {
                                return DateAggregation.d7;
                            } else if (queryHotAdsDto.getDateType() == 2) {
                                return DateAggregation.d30;
                            } else {
                                return DateAggregation.d3;
                            }
                        }).get())
                        .setSceneType(SceneType.all)
                        .setCreativeId(queryHotAdsDto.getCreativeId())
                        .setCreativeIdIn(queryHotAdsDto.getProtectionList())
                        .setCreativeIdNotIn(Optional.ofNullable(queryHotAdsDto.getBlackList())
                                .orElse(new ArrayList<>()))
                        .setPs(queryHotAdsDto.getPage().getPageSize())
                        .setPn(queryHotAdsDto.getPage().getPage())
                        .setUsingBlacklist(false)
        );
        return new PageResult<>(
                page.getTotal_count(),
                page.getData()
                        .stream()
                        .map(BusinessPojoConverter.converter::toMngHotAdsDto)
                        .collect(Collectors.toList())
        );

    }
}
