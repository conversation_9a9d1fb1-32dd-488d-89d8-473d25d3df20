package com.bilibili.mgk.material.center.service.bluelink.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DynamicReleaseStateDTO {

    // 动态是否可见，0：不可见，1：可见
    private Long visible;

    // 动态是否在审核中/待审，0：已有审核结果，1：审核中/待审
    private Long inAudit;

    // 动态是否已删除/驳回/失效，0：否，1：是
    private Long isDeleted;

    // 动态是否是用户删除，0：动态非用户删除，1：动态被用户删除
    private Long userRemove;

    // 动态是否仅自己可见，0：无特殊限制，1：动态仅自见
    private Long ownerVisibleOnly;

    // 动态是否仅详情可见，0：无特殊限制，1：动态仅详情可见
    private Long indexVisibleOnly;

    // 动态是否在天马禁止分发，0：天马禁止分发，1：天马可分发
    private Long pegasusVisible;

    // 动态是否在搜索禁止分发，0：搜索禁止分发，1：搜索可分发
    private Long searchVisible;

    // 动态是否仅空间页可见，0：无特殊限制，1：动态仅空间页可见
    private Long spaceVisibleOnly;
}
