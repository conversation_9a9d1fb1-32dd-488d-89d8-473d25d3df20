package com.bilibili.mgk.material.center.facade;

import com.bapis.datacenter.service.oneservice.AdvFilter;
import com.bapis.datacenter.service.oneservice.ApiStatusReq;
import com.bapis.datacenter.service.oneservice.OneServiceOpenApiGrpc.OneServiceOpenApiBlockingStub;
import com.bapis.datacenter.service.oneservice.OperatorVo;
import com.bapis.datacenter.service.oneservice.QueryReq.Builder;
import com.bapis.datacenter.service.oneservice.RespString;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/14
 */
@Slf4j
@Service
public class ApiOneServiceBase {

    @Resource
    private OneServiceOpenApiBlockingStub oneServiceOpenApiBlockingStub;

    // TODO 永不过期，定期 10min refresh
    private LoadingCache<String, String> apiLatestLogDateCache;

    private DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");

    @PostConstruct
    public void init() {

        apiLatestLogDateCache = CacheBuilder.newBuilder()
                .expireAfterWrite(10, TimeUnit.MINUTES)
                .build(new CacheLoader<String, String>() {
                    @Override
                    public String load(String key) throws Exception {
                        String logdate = tryTodayOrElseNDayBefore(key, 3);
                        log.info("Find logdate={} of api={}, ", logdate, key);
                        return logdate;
                    }
                });
    }

    public String findLatestOpenApiLogDate(String apiId) {

        return Try.of(() -> {
            return apiLatestLogDateCache.get(apiId);
        }).onFailure(t -> {
            log.error("Fail to load log_date from cache, apiId={}", apiId, t);
        }).getOrElse(formattedLogDate(LocalDateTime.now()));
    }


    public Tuple2<String, LocalDate> findLatestOpenApiLogDateAndDate(String apiId) {

        return Try.of(() -> {
            return apiLatestLogDateCache.get(apiId);
        }).onFailure(t -> {
            log.error("Fail to load log_date from cache, apiId={}", apiId, t);
        }).map(logdate -> {
            return Tuple.of(logdate, LocalDate.parse(logdate, dateTimeFormatter));
        }).getOrElse(() -> {
            String logdate = formattedLogDate(LocalDateTime.now());
            return Tuple.of(logdate, LocalDate.parse(logdate, dateTimeFormatter));
        });


    }


    /**
     * @param apiId
     * @param n     如果是3， 那么亿次尝试 今天 昨天，前天
     * @return
     */
    private String tryTodayOrElseNDayBefore(String apiId, int n) {

        String matchLogDate = null;

        for (int i = 0; i < n; i++) {

            // 0 ,1 2

            int finalI = i + 1;
            String found = Try.of(() -> {

                LocalDateTime date = LocalDateTime.now().minusDays((long) finalI);

                String logDate = formattedLogDate(date);

                RespString rsp = oneServiceOpenApiBlockingStub.checkDataStatus(
                        ApiStatusReq.newBuilder()
                                .setApiId(apiId)
                                .setLogDate(logDate)
                                .build());

                if ("ready".equals(rsp.getRespString())) {
                    return logDate;
                } else {
                    return null;
                }

            }).onFailure(t -> {
                log.error("Fail to call oneServiceOpenApiBlockingStub.checkDataStatus, apiId={}", apiId, t);
            }).getOrNull();

            if (found != null) {
                matchLogDate = found;
                break;
            }
        }

        return Optional.ofNullable(matchLogDate)
                // 如果实在找不到，或者一直报错，就选择今天的
                .orElse(formattedLogDate(LocalDateTime.now().minusDays(1)));

    }


    /**
     * @param date
     * @param dateFormat
     * @return
     */
    private String formattedLogDate(LocalDateTime date) {

        return dateTimeFormatter.format(date);

    }


    public void appendParams(Builder openApiReq, String key, String op, String... value) {
        openApiReq.addReqs(OperatorVo.newBuilder()
                .setField(key)
                .setOperator(op)
                .addAllValues(Lists.newArrayList(value)));
    }

    public void appendParams(Builder openApiReq, String key, String op, List<String> value) {
        openApiReq.addReqs(OperatorVo.newBuilder()
                .setField(key)
                .setOperator(op)
                .addAllValues(value));
    }



    public void appendAdvParams(String key, String op, String values,
            List<AdvFilter.Builder> rootAdvFilterExpressions) {

        this.appendAdvParams(key, op, Lists.newArrayList(values), rootAdvFilterExpressions);
    }


    public void appendAdvParams(String key, String op, List<String> values,
            List<AdvFilter.Builder> rootAdvFilterExpressions) {

        AdvFilter.Builder rootAndFilter = AdvFilter.newBuilder()
                .setField(key)
                .setOperator(op)
                .addAllValues(values);

        rootAdvFilterExpressions.add(rootAndFilter);
    }


}
