package com.bilibili.mgk.material.center.facade;

import com.bapis.datacenter.service.oneservice.OperatorVo;
import com.bapis.datacenter.service.oneservice.OsHeader;
import com.bapis.datacenter.service.oneservice.QueryReq;
import com.bapis.datacenter.service.oneservice.QueryReq.Builder;
import com.bapis.datacenter.service.oneservice.QueryResp;
import com.bilibili.mgk.material.center.facade.proxy.OneServiceFlowControlProxy;
import com.bilibili.mgk.material.center.service.creative.model.IndustryAudienceAnalysis;
import com.bilibili.mgk.material.center.service.creative.vo.DateAggregation;
import com.google.common.collect.Lists;
import io.vavr.control.Either;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 提供外观层是为了屏蔽API指标服务，或是直接的ClickHouse存取；
 * <p>
 * 目前应该是使用api指标服务
 *
 * <AUTHOR>
 * @desc
 * @date 2024/3/8
 * @since 2024-08-06 支持新行业， 原字段直接映射为新行业字段， 无需变更，只需更新api_id 配置
 */

/***
 SELECT
 commerce_category_first_id,
 commerce_category_second_id,
 data_type,
 profile_type,
 profile_info
 FROM
 bili_sycpb.ads_prty_mem_commerce_category_second_profile_data_a_d
 <where>
 (
 commerce_category_first_id = ${commerce_category_first_id,type=number}
 or
 commerce_category_second_id = ${commerce_category_second_id,type=number}
 )

 <if test='profile_type != null and profile_type != ""'>
 and profile_type = ${profile_type,type=string}
 </if>
 </where>




 */
@Service
public class IndustryAudienceAnalysisFacade {

    @Resource
    private OneServiceFlowControlProxy serviceOpenApiManagerBlockingStub;

    @Resource
    private ApiOneServiceBase apiOneServiceBase;

    @Value("${material.oneservice.audience-analysis-v2.app-key:496b1dd505103413f42117ff1d5840ca}")
    private String appKeyV2;

    @Value("${material.oneservice.audience-analysis-v2.secret:tL4vGnTz03chln2R6iiMEQi8ZC/WLKS+NsLSbkP0AAs=}")
    private String secretV2;

    @Value("${material.oneservice.audience-analysis-v2.api-id:api_2383}")
    private String apiIdV2;

    @Value("${material.oneservice.audience-analysis.app-key:496b1dd505103413f42117ff1d5840ca}")
    private String appKey;

    @Value("${material.oneservice.audience-analysis.secret:tL4vGnTz03chln2R6iiMEQi8ZC/WLKS+NsLSbkP0AAs=}")
    private String secret;

    @Value("${material.oneservice.audience-analysis.api-id:api_1562}")
    private String apiId;


    private DateTimeFormatter yyyyMMddFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
    private DateTimeFormatter yyyy_MM_ddFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     *
     *
     */
    /**
     *
     */
    public Map<String, List<Either<IndustryAudienceAnalysis, List<IndustryAudienceAnalysis>>>> findBySecondCategoryOrElseByFirstCategory(
            Long firstCategoryId, Long secondCategoryId, DateAggregation dayType
    ) {

        // datatype-> (profileType-> list)
        Map<String, Map<String, List<IndustryAudienceAnalysis>>> allAnalysis = this.selectByFirstOrSecondCategoryId(
                        firstCategoryId, secondCategoryId, dayType.getDayType(), null)
                .stream()
                .collect(Collectors.groupingBy(analysis -> analysis.getDataType(),

                        Collectors.groupingBy(analysis -> analysis.getProfileType())));

        return allAnalysis
                .entrySet()
                .stream()
                .collect(
                        Collectors.toMap(entry -> entry.getKey(),

                                profile2list -> profile2list.getValue().entrySet().stream()
                                        .map(entry -> {
                                            return entry.getValue().stream()
                                                    .filter(r -> Objects.equals(r.getCommerceCategorySecondId(),
                                                            secondCategoryId))
                                                    .findFirst()
                                                    .map(Either::<IndustryAudienceAnalysis, List<IndustryAudienceAnalysis>>left)
                                                    .orElse(Either.right(entry.getValue()));
                                        })
                                        .collect(Collectors.toList())
                        ));
    }


    /**
     * 1. 如果first second同时存在，且second 能够查到数据则二级行业聚合 2. 如果second不存在，或者second行业数据查不到，则一级行业聚合 3.
     * 如果first不存在，或者first行业查不到（应该不存在） ，则全部行业聚合
     *
     * @param firstCategoryId
     * @param secondCategoryId
     * @param dayType
     * @param profileType
     * @return
     */
    private List<IndustryAudienceAnalysis> selectByFirstOrSecondCategoryId(
            @Nonnull Long firstCategoryId,
            @Nonnull Long secondCategoryId,

            String dayType,
            String profileType) {

        String logDate = getApiLogDateWithCache();
        String latestUpdateTime = logDate2latestUpdateTime(logDate);

        Builder openApiReq = baseReq(logDate, dayType, profileType);

        if (isLongNull(firstCategoryId) && isLongNull(secondCategoryId)) {

            // 0.搜索全局
            QueryResp rsp = serviceOpenApiManagerBlockingStub.query(openApiReq.build());

            return rsp.getRowsList()
                    .stream()
                    .map(map -> IndustryAudienceAnalysis
                            .fromMapValue(map.getValueMap())
                            .setLatestUpdateTime(latestUpdateTime))
                    .collect(Collectors.toList());

        }

        // 1.搜索二级行业
        List<IndustryAudienceAnalysis> result = new ArrayList<>();

        if (!isLongNull(secondCategoryId)) {

            // 1.1尝试搜索二级行业
            openApiReq.addReqs(OperatorVo.newBuilder()
                    .setField("commerce_category_second_id")
                    .setOperator("=")
                    .addAllValues(Lists.newArrayList(secondCategoryId.toString())));

            QueryResp rsp = serviceOpenApiManagerBlockingStub.query(openApiReq.build());

            result = rsp.getRowsList().stream()
                    .map(map -> IndustryAudienceAnalysis.fromMapValue(map.getValueMap()))
                    .collect(Collectors.toList());

        }

        // 2. 二级行业不存在搜索一级行业
        if (CollectionUtils.isEmpty(result)) {
            openApiReq = baseReq(logDate, dayType, profileType);

            openApiReq.addReqs(OperatorVo.newBuilder()
                    .setField("commerce_category_first_id")
                    .setOperator("=")
                    .addAllValues(Lists.newArrayList(firstCategoryId.toString())));

            QueryResp rsp = serviceOpenApiManagerBlockingStub.query(openApiReq.build());

            result = rsp.getRowsList().stream()
                    .map(map -> IndustryAudienceAnalysis
                            .fromMapValue(map.getValueMap())
                            .setLatestUpdateTime(latestUpdateTime))
                    .collect(Collectors.toList());

            // 目前不存在一级行业不存在，也不需要对一级行业查不到时进行全局行业的兜底
        }

        return result;

    }


    private Builder baseReq(String logDate, String dayType, String profileType) {
        OsHeader osHeader = OsHeader.newBuilder()
                .setAppKey(appKeyV2)
                .setSecret(secretV2)
                .setApiId(apiIdV2)
                .build();
        Builder openApiReq = QueryReq.newBuilder()
                .setOsHeader(osHeader)
                .addReqs(OperatorVo.newBuilder()
                        .setField("log_date")
                        .setOperator("=")
                        .addAllValues(Lists.newArrayList(logDate)))
                .addReqs(OperatorVo.newBuilder()
                        .setField("day_type")
                        .setOperator("=")
                        .addAllValues(Lists.newArrayList(dayType)));

        // 默认所有
        if (StringUtils.isNotEmpty(profileType)) {
            openApiReq.addReqs(OperatorVo.newBuilder()
                    .setField("profile_type")
                    .setOperator("=")
                    .addAllValues(Lists.newArrayList(profileType)));
        }

        return openApiReq;

    }


    private boolean isLongPresent(Long categoryId) {
        return categoryId != null && categoryId > 0;
    }


    private boolean isLongNull(Long categoryId) {
        return categoryId == null || categoryId <= 0;
    }


    private String getApiLogDateWithCache() {

        return apiOneServiceBase.findLatestOpenApiLogDate(apiIdV2);
    }


    private String logDate2latestUpdateTime(String logDate) {
        return yyyy_MM_ddFormatter.format(yyyyMMddFormatter.parse(logDate));
    }

}
