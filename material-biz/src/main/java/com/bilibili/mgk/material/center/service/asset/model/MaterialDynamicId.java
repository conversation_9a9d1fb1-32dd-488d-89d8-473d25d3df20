package com.bilibili.mgk.material.center.service.asset.model;

import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 图文动态
 *
 * <AUTHOR>
 * @desc
 * @date 2024/7/8
 */
@Data
@Accessors(chain = true)
public class MaterialDynamicId implements SnakeCaseBody {


    @ApiModelProperty("动态id")
    private String dynId;


    @ApiModelProperty("动态类型")
    private String dynType;


    @ApiModelProperty("动态rid")
    private String dynRid;


    @ApiModelProperty("调度任务id")
    private Long scheduleId;




}
