package com.bilibili.mgk.material.center.service.creative.model;

import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import com.bilibili.mgk.material.center.util.JsonUtil;
import com.google.common.base.Splitter;
import io.vavr.Tuple;
import io.vavr.control.Try;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

/**
 * 行业受众分析
 *
 * <AUTHOR>
 * @desc
 * @date 2024/3/8
 */
@Data
@Accessors(chain = true)
public class IndustryAudienceAnalysis implements SnakeCaseBody {


    /**
     * 账户一级行业id
     */

    private Long commerceCategoryFirstId;

    /**
     * 账户一级行业名称
     */
    private String commerceCategoryFirstName;

    /**
     * 账户二级行业id
     */
    private Long commerceCategorySecondId;

    /**
     * 账户二级行业名称
     */
    private String commerceCategorySecondName;

    /**
     * 用户类型 具体类型枚举@数据组
     */
    private Integer userType;


    /**
     * 数据类型click conv
     */
    private String dataType;

    /**
     * 分布类型 {@link AudienceProfileType} age_profile "年龄分布" city_profile "城市分布" gender_profile "性别分布" province_profile
     * "省份分布"
     */
    private String profileType;


    /**
     * 数据分布原始数据，前端无需关心 e.g. 1) 河北|0.0282,澳门|0.0113,辽宁|0.0565,浙江|0.0847,.. 2) 0|0.0282,澳门|0.0113,1|0.0565,2|0.0847,..
     */
    private String profileInfo;


    /**
     * k-v map key 展示名称 value 百分比分布 { "男":"0.5", "女":"0.5" }
     */
    private Map<String, String> profileDistribution;
    /**
     * 是否使用二级行业聚合 true 二级行业分布 false 所查询的二级行业归属的一级行业兜底
     */
    private Boolean groupBySecondCategory;


    private String latestUpdateTime;


    public static IndustryAudienceAnalysis fromMapValue(Map<String, String> mapValue) {

        return JsonUtil.fromJson(mapValue, IndustryAudienceAnalysis.class);
    }


    public static IndustryAudienceAnalysis aggregate(List<IndustryAudienceAnalysis> firstCategoryAnalysises) {
        if (CollectionUtils.isEmpty(firstCategoryAnalysises)) {
            return null;
        }

        Map<String, Long> counters = new HashMap<>();

        firstCategoryAnalysises.stream()
                .map(first -> first.deserializeCounters())
                .forEach(eachCounters -> {

                    eachCounters.forEach((k, v) -> {

                        Long counter = counters.getOrDefault(k, 0L);

                        counters.put(k, counter + v);

                    });

                });

        IndustryAudienceAnalysis firstCategory = new IndustryAudienceAnalysis();
        BeanUtils.copyProperties(firstCategoryAnalysises.get(0), firstCategory);

        firstCategory.setProfileInfo("");
        firstCategory.setProfileDistribution(firstCategory.aggregateProfileDistribution(counters));

        return firstCategory;

    }


    public Map<String, String> getProfileDistribution() {

        if (profileDistribution == null) {
            if (StringUtils.isEmpty(profileInfo)) {
                this.profileDistribution = new HashMap<>();
            } else {
                Map<String, Long> counters = deserializeCounters();

                this.profileDistribution = aggregateProfileDistribution(counters);
            }
        }
        return profileDistribution;

    }

    private Map<String, Long> deserializeCounters() {

        return Try.of(() -> {
            return Splitter.on(",")
                    .splitToList(profileInfo)
                    .stream()
                    .map(tuple -> {

                        List<String> kv = Splitter.on("|").splitToList(tuple);

                        if (kv.size() >= 2) {

                            return Try.of(() -> {
                                return Tuple.of(kv.get(0), Long.valueOf(kv.get(1)));
                            }).getOrNull();
                        } else {
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(tuple -> tuple._1, tuple -> tuple._2, (a, b) -> b));
        }).getOrElse(new HashMap<>());

    }


    private Map<String, String> aggregateProfileDistribution(Map<String, Long> counters) {
        return Try.of(() -> {

            Long total = counters.entrySet()
                    .stream()
                    .map(tuple -> tuple.getValue())

                    .reduce(Long::sum)
                    .orElse(0L);

            // 0-> 17岁以下
            return counters
                    .entrySet()
                    .stream()
                    .map(tuple -> {
                        Function<String, String> mapping = Try
                                .of(() -> Optional.of(AudienceProfileType.valueOf(profileType)))
                                .getOrElse(Optional.empty())
                                .map(AudienceProfileType::getProfileInfoKeyMapping)
                                .orElse(Function.identity());
                        return Tuple.of(mapping.apply(tuple.getKey()), tuple.getValue());
                    })
                    // 17岁以下-> 0.0282
                    .collect(Collectors.toMap(tuple -> tuple._1, tuple -> {
                                return BigDecimal.valueOf(tuple._2)
                                        .divide(BigDecimal.valueOf(total), 4, BigDecimal.ROUND_HALF_UP)
                                        .toString();
                            },
                            (a, b) -> b));

        }).getOrElse(new HashMap<>());
    }

}
