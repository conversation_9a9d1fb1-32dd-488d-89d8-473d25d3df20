package com.bilibili.mgk.material.center.service.aigc.model;

import java.util.Arrays;
import java.util.Optional;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/19
 */
@Getter
@RequiredArgsConstructor
public enum Img2ImgRelevance {

    /**
     *
     */
    level_1(1, 0.8D, "低"),

    /**
     *
     */
    level_2(2, 0.5D, "中"),

    /**
     *
     */
    level_3(3, 0.3D, "高"),

    ;

    private final int level;

    private final double relevanceFloatVal;

    private final String desc;


    public static double level2float(Integer level) {

        if (level == null) {
            throw new IllegalArgumentException("level is null");
        }

        return Arrays.stream(Img2ImgRelevance.values())
                .filter(v -> v.getLevel() == level)
                .findFirst()
                .map(Img2ImgRelevance::getRelevanceFloatVal)
                .orElseThrow(() -> {
                    return new IllegalArgumentException("level is illegal");
                });
    }


    public static Optional<Img2ImgRelevance> fromLevel(Integer level) {

        if (level == null) {
            throw new IllegalArgumentException("level is null");
        }

        return Arrays.stream(Img2ImgRelevance.values())
                .filter(v -> v.getLevel() == level)
                .findFirst();
    }


}
