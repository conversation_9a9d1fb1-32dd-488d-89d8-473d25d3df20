package com.bilibili.mgk.material.center.repository;

import com.bilibili.mgk.material.center.service.aigc.dto.GeneratedImageThumbupAction;
import com.bilibili.mgk.material.center.service.aigc.model.AIGeneratedImage;
import com.bilibili.mgk.material.center.service.aigc.model.Img2ImgProgressRecord;
import com.bilibili.mgk.material.center.service.aigc.model.Img2ImgProgressStatus;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialSortBy;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialSortOrder;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import java.util.List;
import java.util.Optional;
import javax.annotation.Nullable;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/23
 */
public interface Img2ImgProgressRecordRepository {


    /**
     * 更新进度状态
     *
     * @param recordId
     * @param progressStatus
     * @param completedImgs
     */

    void updateRecordStatusAndInsertGeneratedImgs(
            Long recordId, Img2ImgProgressStatus progressStatus,
            List<AIGeneratedImage> completedImgs, @Nullable Integer code, @Nullable String errMsg);


    void updateRecordStatus(
            Long recordId, Img2ImgProgressStatus progressStatus, @Nullable Integer code, @Nullable String errMsg);


    void insertCompletedImgs(List<AIGeneratedImage> completedImgs);


    /**
     * @param recordId
     * @param withGeneratedImgs
     * @return
     */
    Optional<Img2ImgProgressRecord> selectRecordByRecordId(Long recordId, boolean withGeneratedImgs);

    /**
     * @param recordId
     * @return
     */
    List<AIGeneratedImage> selectGeneratedImgsByRecordId(Long recordId);

    /**
     * @param accountId
     * @param progressStatus
     * @param pn
     * @param ps
     * @param sortBy
     * @param order
     * @return
     */
    Pagination<List<Img2ImgProgressRecord>> pageByAccountIdAndStatusIn(
            Long accountId, @Nullable List<Img2ImgProgressStatus> progressStatus,
            Integer pn, Integer ps, MaterialSortBy sortBy, MaterialSortOrder order
    );


    /**
     * 提交图生图任务时，插入进度记录
     *
     * @return
     */
    int insertProgressRecord(Img2ImgProgressRecord insertSelective);

    /**
     * 删除记录
     *
     * @param accountId
     * @param recordId
     */
    void deleteRecordByRecordId(Long accountId, Long recordId);

    /**
     * 删除记录中图片
     *
     * @param recordId
     * @param imgMd5
     */
    void deleteGeneratedImgByRecordIdAndMd5(Long recordId, String imgMd5);

    /**
     * 更新点赞点踩状态
     *
     * @param recordId
     * @param imgMd5
     * @param action
     * @param dislikeReasons
     */
    void updateGeneratedImgLikeDislikeByRecordIdAndMd5(
            Long recordId, String imgMd5,
            GeneratedImageThumbupAction action,
            String dislikeReasons);
}
