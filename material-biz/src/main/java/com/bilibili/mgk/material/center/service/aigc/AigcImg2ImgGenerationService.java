package com.bilibili.mgk.material.center.service.aigc;

import com.bilibili.mgk.material.center.service.aigc.dto.AIGeneratedImageDeletedReq;
import com.bilibili.mgk.material.center.service.aigc.dto.DislikeReasonsResp;
import com.bilibili.mgk.material.center.service.aigc.dto.HighCostCoverPageQuery;
import com.bilibili.mgk.material.center.service.aigc.dto.ImageAttitudeUpdateReq;
import com.bilibili.mgk.material.center.service.aigc.dto.ImageGenerationRecordDeletedReq;
import com.bilibili.mgk.material.center.service.aigc.dto.ImageToImageAuditAdviceResp;
import com.bilibili.mgk.material.center.service.aigc.dto.ImageToImageHistoryPageQuery;
import com.bilibili.mgk.material.center.service.aigc.dto.ImageToImageProgressQueryReq;
import com.bilibili.mgk.material.center.service.aigc.dto.ImageToImageProgressResp;
import com.bilibili.mgk.material.center.service.aigc.dto.ImageToImageSubmitReq;
import com.bilibili.mgk.material.center.service.aigc.dto.ImageToTextReq;
import com.bilibili.mgk.material.center.service.aigc.dto.ImageToTextResp;
import com.bilibili.mgk.material.center.service.aigc.model.CreativeHighCostCover;
import com.bilibili.mgk.material.center.service.aigc.model.Img2ImgGenerationHistory;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import java.util.List;

/**
 * 图生图服务
 *
 * <AUTHOR>
 * @desc
 * @date 2024/7/18
 */
public interface AigcImg2ImgGenerationService {


    /**
     * 图升文字
     *
     * @param imageUrl
     * @return
     */
    ImageToTextResp img2txt(ImageToTextReq imageUrl);


    ImageToImageAuditAdviceResp imgCheck(String url);

    /**
     * 图生图-任务提交
     *
     * @param req
     * @return
     */
    ImageToImageProgressResp img2imgSubmit(ImageToImageSubmitReq req);


    /**
     * 图生图-对轮询得到的进度进行查询
     *
     * @param req
     * @return
     */
    ImageToImageProgressResp queryImg2ImgProgress(ImageToImageProgressQueryReq req);


    /**
     * 任务调度-进度轮询，使用后台轮询机制，而不依赖前端的http生命周期
     *
     * @param msg
     */
    void scheduleProgressProbeTask(String msg, boolean scheduleAtLocal);


    /**
     * 列表展示待选的保量素材用于后续的图生图 查询高消耗的封面图， 也是AI衍生图的原图底图；
     *
     * @param query
     * @return
     */
    Pagination<List<CreativeHighCostCover>> listHighCostCover(HighCostCoverPageQuery query);


    /**
     * 查看历史生成记录
     *
     * @param query
     * @return
     */
    Pagination<List<Img2ImgGenerationHistory>> listImageGenerationHistory(ImageToImageHistoryPageQuery query);


    /**
     * 删除生成的历史记录
     *
     * @param req
     */
    void deleteImageGenerationRecord(ImageGenerationRecordDeletedReq req);


    /**
     * 删除历史记录里的生成图片
     *
     * @param req
     */
    void deleteGeneratedImage(AIGeneratedImageDeletedReq req);


    /**
     * 对生成记录的点赞点踩；
     *
     * @param req
     */
    void likeDislikeAIGeneratedImg(ImageAttitudeUpdateReq req);


    DislikeReasonsResp dislikeReasons();


}
