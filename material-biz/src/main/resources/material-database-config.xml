<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:tx="http://www.springframework.org/schema/tx"
  xsi:schemaLocation="http://www.springframework.org/schema/beans
  		http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
  		http://www.springframework.org/schema/tx
        http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">

    <bean id="materialCkCatExecutorMybatisPlugin" class="com.bilibili.bjcom.cat.mybatis.CatExecutorMybatisPlugin"/>


    <!-- Spring 和 MyBatis -->
    <bean id="materialCkSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="clickHouseDataSource"/>
        <property name="mapperLocations" value="classpath:mapper/material_ck/*.xml"/>
        <property name="configLocation" value="classpath:material-mybatis-config.xml"/>
        <property name="plugins">
            <array>
                <ref bean="materialCkCatExecutorMybatisPlugin"/>
            </array>
        </property>
    </bean>

    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.bilibili.mgk.material.center.repository.clickhouse"/>
        <property name="sqlSessionFactoryBeanName" value="materialCkSqlSessionFactory"/>
    </bean>

    <tx:annotation-driven transaction-manager="materialCkTransactionManager"/>
    <!-- 配置事务管理器 -->
    <bean id="materialCkTransactionManager"
      class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="clickHouseDataSource"/>
    </bean>


    <bean id="materialCatExecutorMybatisPlugin" class="com.bilibili.bjcom.cat.mybatis.CatExecutorMybatisPlugin"/>

    <bean id="materialDataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource">
        <property name="driverClass" value="${mgk.jdbc.driver}"></property>
        <property name="jdbcUrl" value="${mgk.jdbc.url}"></property>
        <property name="user" value="${mgk.jdbc.username}"></property>
        <property name="password" value="${mgk.jdbc.password}"></property>
        <!--<property name="minPoolSize" value="10"></property>-->
        <property name="maxPoolSize" value="10"></property>
        <property name="maxIdleTime" value="7200"></property>
        <property name="testConnectionOnCheckin" value="true"></property>
        <property name="idleConnectionTestPeriod" value="5"></property>
        <property name="preferredTestQuery" value="SELECT 1"></property>
        <property name="checkoutTimeout" value="1800000"></property>
    </bean>


    <!-- Spring 和 MyBatis -->
    <bean id="materialSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="materialDataSource"/>
        <property name="mapperLocations" value="classpath:mapper/material/*.xml"/>
        <property name="configLocation" value="classpath:material-mybatis-config.xml"/>
        <property name="plugins">
            <array>
                <ref bean="materialCatExecutorMybatisPlugin"/>
                <bean class="com.github.pagehelper.PageInterceptor">
                    <property name="properties">
                        <!--使用下面的方式配置参数，一行配置一个 -->
                        <value>
                            params=value1
                        </value>
                    </property>
                </bean>
            </array>

        </property>
    </bean>

    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.bilibili.mgk.material.center.repository.mysql"/>
        <property name="sqlSessionFactoryBeanName" value="materialSqlSessionFactory"/>
    </bean>

    <tx:annotation-driven transaction-manager="materialTransactionManager"/>
    <!-- 配置事务管理器 -->
    <bean id="materialTransactionManager"
      class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="materialDataSource"/>
    </bean>


  <bean id="adCatExecutorMybatisPlugin" class="com.bilibili.bjcom.cat.mybatis.CatExecutorMybatisPlugin"/>

  <bean id="materialAdDataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource">
    <property name="driverClass" value="${mgk.ad.jdbc.driver}"></property>
    <property name="jdbcUrl" value="${mgk.ad.jdbc.url}"></property>
    <property name="user" value="${mgk.ad.jdbc.username}"></property>
    <property name="password" value="${mgk.ad.jdbc.password}"></property>
    <!--<property name="minPoolSize" value="10"></property>-->
    <property name="maxPoolSize" value="10"></property>
    <property name="maxIdleTime" value="7200"></property>
    <property name="testConnectionOnCheckin" value="true"></property>
    <property name="idleConnectionTestPeriod" value="5"></property>
    <property name="preferredTestQuery" value="SELECT 1"></property>
    <property name="checkoutTimeout" value="1800000"></property>
  </bean>


  <!-- Spring 和 MyBatis -->
  <bean id="materialAdSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
    <property name="dataSource" ref="materialAdDataSource"/>
    <property name="mapperLocations" value="classpath:mapper/mysql_business/*.xml"/>
    <property name="configLocation" value="classpath:material-mybatis-config.xml"/>
    <property name="plugins">
      <array>
        <ref bean="adCatExecutorMybatisPlugin"/>
        <bean class="com.github.pagehelper.PageInterceptor">
          <property name="properties">
            <!--使用下面的方式配置参数，一行配置一个 -->
            <value>
              params=value1
            </value>
          </property>
        </bean>
      </array>

    </property>
  </bean>

  <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
    <property name="basePackage" value="com.bilibili.mgk.material.center.repository.mysql_business"/>
    <property name="sqlSessionFactoryBeanName" value="materialAdSqlSessionFactory"/>
  </bean>

  <tx:annotation-driven transaction-manager="materialAdTransactionManager"/>
  <!-- 配置事务管理器 -->
  <bean id="materialAdTransactionManager"
    class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
    <property name="dataSource" ref="materialAdDataSource"/>
  </bean>

</beans>
