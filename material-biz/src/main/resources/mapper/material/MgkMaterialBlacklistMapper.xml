<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.material.center.repository.mysql.MgkMaterialBlacklistMapper">

  <resultMap id="BaseResultMap" type="com.bilibili.mgk.material.center.service.blacklist.model.MaterialBlacklist">
    <id property="id" column="id" jdbcType="BIGINT"/>
    <result property="targetId" column="target_id" jdbcType="VARCHAR"/>
    <result property="targetType" column="target_type" jdbcType="VARCHAR"/>
    <result property="operator" column="operator" jdbcType="VARCHAR"/>
    <result property="blacklistType" column="blacklist_type" jdbcType="VARCHAR"/>
    <result property="extra" column="extra" jdbcType="VARCHAR"/>
    <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
    <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
    <result property="deleted" column="deleted" jdbcType="TINYINT"/>
  </resultMap>

  <sql id="Base_Column_List">
    id,target_id,target_type,
        operator,blacklist_type,extra,
        ctime,mtime,deleted
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from mgk_material_blacklist
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectAllByBlacklistTypeAndTargetTypeInAndTargetIdIn"
    resultType="java.util.ArrayList" resultMap="BaseResultMap">

    select
    <include refid="Base_Column_List"/>
    from mgk_material_blacklist
    <where>

      blacklist_type= #{blacklistType}
      <if test="targetTypes != null and targetTypes.size > 0 ">
        and target_type in
        <foreach close=")" collection="targetTypes" item="listItem" open="(" separator=",">
          #{listItem}
        </foreach>
      </if>

      <if test="targetIds != null and targetIds.size > 0">
        and target_id in
        <foreach close=")" collection="targetIds" item="listItem" open="(" separator=",">
          #{listItem}
        </foreach>
      </if>

    </where>
    order by id desc
  </select>


  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from mgk_material_blacklist
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByPrimaryKeyBatch">
    delete from mgk_material_blacklist
    where
    <if test="ids != null and targetIds.size > 0">
      and id in
      <foreach close=")" collection="id" item="listItem" open="(" separator=",">
        #{listItem}
      </foreach>
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id"
    parameterType="com.bilibili.mgk.material.center.service.blacklist.model.MaterialBlacklist" useGeneratedKeys="true">
    insert into mgk_material_blacklist
    ( id, target_id, target_type
    , operator, blacklist_type, extra
    , ctime, mtime, deleted)
    values ( #{id,jdbcType=BIGINT}, #{targetId,jdbcType=VARCHAR}, #{targetType,jdbcType=VARCHAR}
           , #{operator,jdbcType=VARCHAR}, #{blacklistType,jdbcType=VARCHAR}, #{extra,jdbcType=VARCHAR}
           , #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id"
    parameterType="com.bilibili.mgk.material.center.service.blacklist.model.MaterialBlacklist" useGeneratedKeys="true">
    insert into mgk_material_blacklist
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">id,</if>
      <if test="targetId != null">target_id,</if>
      <if test="targetType != null">target_type,</if>
      <if test="operator != null">operator,</if>
      <if test="blacklistType != null">blacklist_type,</if>
      <if test="extra != null">extra,</if>
      <if test="ctime != null">ctime,</if>
      <if test="mtime != null">mtime,</if>
      <if test="deleted != null">deleted,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">#{id,jdbcType=BIGINT},</if>
      <if test="targetId != null">#{targetId,jdbcType=VARCHAR},</if>
      <if test="targetType != null">#{targetType,jdbcType=VARCHAR},</if>
      <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
      <if test="blacklistType != null">#{blacklistType,jdbcType=VARCHAR},</if>
      <if test="extra != null">#{extra,jdbcType=VARCHAR},</if>
      <if test="ctime != null">#{ctime,jdbcType=TIMESTAMP},</if>
      <if test="mtime != null">#{mtime,jdbcType=TIMESTAMP},</if>
      <if test="deleted != null">#{deleted,jdbcType=TINYINT},</if>
    </trim>
  </insert>
  <insert id="insertBatch">
    insert into
    mgk_material_blacklist
    ( target_id,target_type
    ,operator,blacklist_type,extra
    ,ctime,mtime,deleted
    )
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.targetId,jdbcType=VARCHAR},
        #{item.targetType,jdbcType=VARCHAR},
        #{item.operator,jdbcType=VARCHAR},
        #{item.blacklistType,jdbcType=VARCHAR},
        #{item.extra,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.deleted,jdbcType=TINYINT}
      </trim>
    </foreach>


  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="com.bilibili.mgk.material.center.service.blacklist.model.MaterialBlacklist">
    update mgk_material_blacklist
    <set>
      <if test="targetId != null">
        target_id = #{targetId,jdbcType=VARCHAR},
      </if>
      <if test="targetType != null">
        target_type = #{targetType,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="blacklistType != null">
        blacklist_type = #{blacklistType,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey"
    parameterType="com.bilibili.mgk.material.center.service.blacklist.model.MaterialBlacklist">
    update mgk_material_blacklist
    set target_id      = #{targetId,jdbcType=VARCHAR},
        target_type    = #{targetType,jdbcType=VARCHAR},
        operator       = #{operator,jdbcType=VARCHAR},
        blacklist_type = #{blacklistType,jdbcType=VARCHAR},
        extra          = #{extra,jdbcType=VARCHAR},
        ctime          = #{ctime,jdbcType=TIMESTAMP},
        mtime          = #{mtime,jdbcType=TIMESTAMP},
        deleted        = #{deleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
