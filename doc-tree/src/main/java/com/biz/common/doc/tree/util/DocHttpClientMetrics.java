/*
 * Copyright (c) 2015-2019 BiliBili Inc.
 */

package com.biz.common.doc.tree.util;

import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import pleiades.component.metrics.BiliCounter;
import pleiades.component.metrics.BiliHistogram;
import pleiades.component.model.VersionMetric;

@Slf4j
public class DocHttpClientMetrics {

    private static final String NAME_SPACE = "http_client";
    private static final String SUBSYSTEM = "requests";
    // http client 请求总数
    public static final BiliCounter HTTP_CLIENT_TOTAL = BiliCounter.build()
            .namespace(NAME_SPACE)
            .subsystem(SUBSYSTEM)
            .name("total")
            .help("http client counter")
            .labelNames("path")
            .create();

    // http client 请求耗时
    public static final BiliHistogram HTTP_CLIENT_DURATION = BiliHistogram.build()
            .namespace(NAME_SPACE)
            .subsystem(SUBSYSTEM)
            .name("duration_ms")
            .help("http client histogram")
            .labelNames("path")
            .buckets(5, 10, 25, 50, 100, 250, 500, 1000, 2500, 5000, 10000)
            .create();
    //  http client code监控
    public static final BiliCounter HTTP_CLIENT_CODE = BiliCounter.build()
            .namespace(NAME_SPACE)
            .subsystem(SUBSYSTEM)
            .name("code_total")
            .help("http client code")
            .labelNames("path", "code", "target")
            .create();
    private static final String version = "";

    static {
        VersionMetric.componentHttpClient(version);
        register();
    }

    /**
     * This method is deprecated, collector now will self register.
     * <p>
     * Call this method nothing will happened
     */
    @Deprecated
    public static void register() {

        Try.run(() -> {
            HTTP_CLIENT_TOTAL.register();
        }).onFailure(t -> {
            log.warn("Fail to register HTTP_CLIENT_TOTAL, may already registered, skip it", t);
        });

        Try.run(() -> {
            HTTP_CLIENT_DURATION.register();
        }).onFailure(t -> {
            log.warn("Fail to register HTTP_CLIENT_DURATION, may already registered, skip it", t);
        });

        Try.run(() -> {
            HTTP_CLIENT_CODE.register();
        }).onFailure(t -> {
            log.warn("Fail to register HTTP_CLIENT_CODE, may already registered, skip it", t);
        });


    }

}
