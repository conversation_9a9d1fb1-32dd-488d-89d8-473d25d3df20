package com.biz.common.doc.tree.service.vo;

import com.biz.common.doc.tree.common.SnakeCaseBody;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/5
 */
@Data
@Accessors(chain = true)
public class NodeGetReq implements SnakeCaseBody, DocRetrieveSupport {


    private List<Long> rootId;

    private Long nodeId;
    /**
     * 是否回表查询文档内存，如果文档存在时
     */
    @ApiModelProperty("可选，是否回表查询文档内存，如果文档存在时 可选，默认false")
    private Boolean retrieveDoc;
    @ApiModelProperty("可选，是否查询节点的祖先节点, 默认否")
    private Boolean retrieveAncestors;

    private String accountId;

    @ApiModelProperty("是否查询收藏状态, 默认否, 当查询时accountId需要提供")
    private Boolean retrieveFavorite = false;


    /**
     * 是否使用docType对节点进行
     */
    @ApiModelProperty("可选，是否使用docType对节点进行")
    private List<String> docType;

    private List<String> nodeType;

    private Boolean isDeleted;

    private Boolean isShow;



    public void validate() {
        Assert.notNull(nodeId, "nodeId不能为空");
    }

}
