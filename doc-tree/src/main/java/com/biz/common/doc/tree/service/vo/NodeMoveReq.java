package com.biz.common.doc.tree.service.vo;

import com.biz.common.doc.tree.common.SnakeCaseBody;
import io.swagger.annotations.ApiModelProperty;
import java.util.Optional;
import lombok.Data;
import lombok.experimental.Accessors;
import reactor.core.support.Assert;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/7
 */

@Data
@Accessors(chain = true)
public class NodeMoveReq implements SnakeCaseBody {

    @ApiModelProperty(value = "节点id")
    private Long nodeId;

    @ApiModelProperty(value = "目标父节点id")
    private Long targetParentId;

    /**
     * 默认为false，移动子孙节点伴随着重建所有受影响节点的path，当前版本不支持 forceMoveNodeWithDescendants
     */
    @ApiModelProperty(value = "是否强制移动子孙节点")
    private Boolean force;


    private NodeUpdateReq withUpdateReq;


    public void validate() {

        Assert.notNull(nodeId, "nodeId不能为空");
        Assert.notNull(targetParentId, "targetParentId不能为空");
        force = Optional.ofNullable(force).orElse(false);

        if (nodeId.equals(targetParentId)) {
            throw new IllegalArgumentException("不能移动到自己的子节点下,避免回路");
        }

    }
}
