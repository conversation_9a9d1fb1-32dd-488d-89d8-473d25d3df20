<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.dao.ext.ExtMgkFormItemDao">
  <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.po.MgkFormItemPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="form_id" jdbcType="BIGINT" property="formId" />
    <result column="form_item_id" jdbcType="BIGINT" property="formItemId" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="sort_number" jdbcType="TINYINT" property="sortNumber" />
    <result column="options_val" jdbcType="VARCHAR" property="optionsVal" />
    <result column="radio_checkbox_val" jdbcType="VARCHAR" property="radioCheckboxVal" />
      <result column="min_address" jdbcType="TINYINT" property="minAddress"/>
      <result column="is_detail_address" jdbcType="TINYINT" property="isDetailAddress"/>
    <result column="is_allow_empty" jdbcType="TINYINT" property="isAllowEmpty" />
    <result column="is_unique" jdbcType="TINYINT" property="isUnique" />
    <result column="is_submit_validate" jdbcType="TINYINT" property="isSubmitValidate" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>

  <select id="getFormItemPosInformIds" resultMap="BaseResultMap">
    SELECT form_id, form_item_id, sort_number, is_allow_empty, is_unique,is_submit_validate,min_address,is_detail_address FROM mgk_form_item
    WHERE 1 = 1
    <if test="formIds != null and formIds.size > 0">
      AND `form_id` IN
      <foreach collection="formIds" item="item" open="(" separator="," close=")">
          #{item}
      </foreach>
    </if>
    AND is_deleted=0;
  </select>

  <select id="getFormItemByIds" resultMap="BaseResultMap">
    SELECT `form_item_id`, `type` FROM `mgk_form_item`
    WHERE 1 = 1
    <if test="formIds != null and formIds.size > 0">
        AND `form_id` IN
        <foreach collection="formIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </if>
    AND is_deleted=0;
  </select>
</mapper>