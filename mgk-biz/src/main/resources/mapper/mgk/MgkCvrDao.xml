<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.dao.MgkCvrDao">
  <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.po.MgkCvrPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="page_id" jdbcType="BIGINT" property="pageId" />
    <result column="main_color" jdbcType="INTEGER" property="mainColor" />
    <result column="page_height" jdbcType="REAL" property="pageHeight" />
    <result column="page_comp_count" jdbcType="INTEGER" property="pageCompCount" />
    <result column="ocr_content_length" jdbcType="INTEGER" property="ocrContentLength" />
    <result column="ocr_content" jdbcType="VARCHAR" property="ocrContent" />
    <result column="page_style_type" jdbcType="VARCHAR" property="pageStyleType" />
    <result column="page_feature_type" jdbcType="VARCHAR" property="pageFeatureType" />
    <result column="page_form_first_screen" jdbcType="INTEGER" property="pageFormFirstScreen" />
    <result column="page_form_input_count" jdbcType="INTEGER" property="pageFormInputCount" />
    <result column="page_form_select_count" jdbcType="INTEGER" property="pageFormSelectCount" />
    <result column="page_form_percent" jdbcType="REAL" property="pageFormPercent" />
    <result column="cvr_button_first_screen" jdbcType="INTEGER" property="cvrButtonFirstScreen" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, page_id, main_color, page_height, page_comp_count, ocr_content_length, ocr_content, 
    page_style_type, page_feature_type, page_form_first_screen, page_form_input_count, 
    page_form_select_count, page_form_percent, cvr_button_first_screen, is_deleted, ctime, 
    mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkCvrPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mgk_cvr
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mgk_cvr
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mgk_cvr
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkCvrPoExample">
    delete from mgk_cvr
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.mgk.platform.biz.po.MgkCvrPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_cvr (page_id, main_color, page_height, 
      page_comp_count, ocr_content_length, ocr_content, 
      page_style_type, page_feature_type, page_form_first_screen, 
      page_form_input_count, page_form_select_count, 
      page_form_percent, cvr_button_first_screen, is_deleted, 
      ctime, mtime)
    values (#{pageId,jdbcType=BIGINT}, #{mainColor,jdbcType=INTEGER}, #{pageHeight,jdbcType=REAL}, 
      #{pageCompCount,jdbcType=INTEGER}, #{ocrContentLength,jdbcType=INTEGER}, #{ocrContent,jdbcType=VARCHAR}, 
      #{pageStyleType,jdbcType=VARCHAR}, #{pageFeatureType,jdbcType=VARCHAR}, #{pageFormFirstScreen,jdbcType=INTEGER}, 
      #{pageFormInputCount,jdbcType=INTEGER}, #{pageFormSelectCount,jdbcType=INTEGER}, 
      #{pageFormPercent,jdbcType=REAL}, #{cvrButtonFirstScreen,jdbcType=INTEGER}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkCvrPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_cvr
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pageId != null">
        page_id,
      </if>
      <if test="mainColor != null">
        main_color,
      </if>
      <if test="pageHeight != null">
        page_height,
      </if>
      <if test="pageCompCount != null">
        page_comp_count,
      </if>
      <if test="ocrContentLength != null">
        ocr_content_length,
      </if>
      <if test="ocrContent != null">
        ocr_content,
      </if>
      <if test="pageStyleType != null">
        page_style_type,
      </if>
      <if test="pageFeatureType != null">
        page_feature_type,
      </if>
      <if test="pageFormFirstScreen != null">
        page_form_first_screen,
      </if>
      <if test="pageFormInputCount != null">
        page_form_input_count,
      </if>
      <if test="pageFormSelectCount != null">
        page_form_select_count,
      </if>
      <if test="pageFormPercent != null">
        page_form_percent,
      </if>
      <if test="cvrButtonFirstScreen != null">
        cvr_button_first_screen,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pageId != null">
        #{pageId,jdbcType=BIGINT},
      </if>
      <if test="mainColor != null">
        #{mainColor,jdbcType=INTEGER},
      </if>
      <if test="pageHeight != null">
        #{pageHeight,jdbcType=REAL},
      </if>
      <if test="pageCompCount != null">
        #{pageCompCount,jdbcType=INTEGER},
      </if>
      <if test="ocrContentLength != null">
        #{ocrContentLength,jdbcType=INTEGER},
      </if>
      <if test="ocrContent != null">
        #{ocrContent,jdbcType=VARCHAR},
      </if>
      <if test="pageStyleType != null">
        #{pageStyleType,jdbcType=VARCHAR},
      </if>
      <if test="pageFeatureType != null">
        #{pageFeatureType,jdbcType=VARCHAR},
      </if>
      <if test="pageFormFirstScreen != null">
        #{pageFormFirstScreen,jdbcType=INTEGER},
      </if>
      <if test="pageFormInputCount != null">
        #{pageFormInputCount,jdbcType=INTEGER},
      </if>
      <if test="pageFormSelectCount != null">
        #{pageFormSelectCount,jdbcType=INTEGER},
      </if>
      <if test="pageFormPercent != null">
        #{pageFormPercent,jdbcType=REAL},
      </if>
      <if test="cvrButtonFirstScreen != null">
        #{cvrButtonFirstScreen,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkCvrPoExample" resultType="java.lang.Long">
    select count(*) from mgk_cvr
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mgk_cvr
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.pageId != null">
        page_id = #{record.pageId,jdbcType=BIGINT},
      </if>
      <if test="record.mainColor != null">
        main_color = #{record.mainColor,jdbcType=INTEGER},
      </if>
      <if test="record.pageHeight != null">
        page_height = #{record.pageHeight,jdbcType=REAL},
      </if>
      <if test="record.pageCompCount != null">
        page_comp_count = #{record.pageCompCount,jdbcType=INTEGER},
      </if>
      <if test="record.ocrContentLength != null">
        ocr_content_length = #{record.ocrContentLength,jdbcType=INTEGER},
      </if>
      <if test="record.ocrContent != null">
        ocr_content = #{record.ocrContent,jdbcType=VARCHAR},
      </if>
      <if test="record.pageStyleType != null">
        page_style_type = #{record.pageStyleType,jdbcType=VARCHAR},
      </if>
      <if test="record.pageFeatureType != null">
        page_feature_type = #{record.pageFeatureType,jdbcType=VARCHAR},
      </if>
      <if test="record.pageFormFirstScreen != null">
        page_form_first_screen = #{record.pageFormFirstScreen,jdbcType=INTEGER},
      </if>
      <if test="record.pageFormInputCount != null">
        page_form_input_count = #{record.pageFormInputCount,jdbcType=INTEGER},
      </if>
      <if test="record.pageFormSelectCount != null">
        page_form_select_count = #{record.pageFormSelectCount,jdbcType=INTEGER},
      </if>
      <if test="record.pageFormPercent != null">
        page_form_percent = #{record.pageFormPercent,jdbcType=REAL},
      </if>
      <if test="record.cvrButtonFirstScreen != null">
        cvr_button_first_screen = #{record.cvrButtonFirstScreen,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mgk_cvr
    set id = #{record.id,jdbcType=BIGINT},
      page_id = #{record.pageId,jdbcType=BIGINT},
      main_color = #{record.mainColor,jdbcType=INTEGER},
      page_height = #{record.pageHeight,jdbcType=REAL},
      page_comp_count = #{record.pageCompCount,jdbcType=INTEGER},
      ocr_content_length = #{record.ocrContentLength,jdbcType=INTEGER},
      ocr_content = #{record.ocrContent,jdbcType=VARCHAR},
      page_style_type = #{record.pageStyleType,jdbcType=VARCHAR},
      page_feature_type = #{record.pageFeatureType,jdbcType=VARCHAR},
      page_form_first_screen = #{record.pageFormFirstScreen,jdbcType=INTEGER},
      page_form_input_count = #{record.pageFormInputCount,jdbcType=INTEGER},
      page_form_select_count = #{record.pageFormSelectCount,jdbcType=INTEGER},
      page_form_percent = #{record.pageFormPercent,jdbcType=REAL},
      cvr_button_first_screen = #{record.cvrButtonFirstScreen,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkCvrPo">
    update mgk_cvr
    <set>
      <if test="pageId != null">
        page_id = #{pageId,jdbcType=BIGINT},
      </if>
      <if test="mainColor != null">
        main_color = #{mainColor,jdbcType=INTEGER},
      </if>
      <if test="pageHeight != null">
        page_height = #{pageHeight,jdbcType=REAL},
      </if>
      <if test="pageCompCount != null">
        page_comp_count = #{pageCompCount,jdbcType=INTEGER},
      </if>
      <if test="ocrContentLength != null">
        ocr_content_length = #{ocrContentLength,jdbcType=INTEGER},
      </if>
      <if test="ocrContent != null">
        ocr_content = #{ocrContent,jdbcType=VARCHAR},
      </if>
      <if test="pageStyleType != null">
        page_style_type = #{pageStyleType,jdbcType=VARCHAR},
      </if>
      <if test="pageFeatureType != null">
        page_feature_type = #{pageFeatureType,jdbcType=VARCHAR},
      </if>
      <if test="pageFormFirstScreen != null">
        page_form_first_screen = #{pageFormFirstScreen,jdbcType=INTEGER},
      </if>
      <if test="pageFormInputCount != null">
        page_form_input_count = #{pageFormInputCount,jdbcType=INTEGER},
      </if>
      <if test="pageFormSelectCount != null">
        page_form_select_count = #{pageFormSelectCount,jdbcType=INTEGER},
      </if>
      <if test="pageFormPercent != null">
        page_form_percent = #{pageFormPercent,jdbcType=REAL},
      </if>
      <if test="cvrButtonFirstScreen != null">
        cvr_button_first_screen = #{cvrButtonFirstScreen,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.platform.biz.po.MgkCvrPo">
    update mgk_cvr
    set page_id = #{pageId,jdbcType=BIGINT},
      main_color = #{mainColor,jdbcType=INTEGER},
      page_height = #{pageHeight,jdbcType=REAL},
      page_comp_count = #{pageCompCount,jdbcType=INTEGER},
      ocr_content_length = #{ocrContentLength,jdbcType=INTEGER},
      ocr_content = #{ocrContent,jdbcType=VARCHAR},
      page_style_type = #{pageStyleType,jdbcType=VARCHAR},
      page_feature_type = #{pageFeatureType,jdbcType=VARCHAR},
      page_form_first_screen = #{pageFormFirstScreen,jdbcType=INTEGER},
      page_form_input_count = #{pageFormInputCount,jdbcType=INTEGER},
      page_form_select_count = #{pageFormSelectCount,jdbcType=INTEGER},
      page_form_percent = #{pageFormPercent,jdbcType=REAL},
      cvr_button_first_screen = #{cvrButtonFirstScreen,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.mgk.platform.biz.po.MgkCvrPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_cvr (page_id, main_color, page_height, 
      page_comp_count, ocr_content_length, ocr_content, 
      page_style_type, page_feature_type, page_form_first_screen, 
      page_form_input_count, page_form_select_count, 
      page_form_percent, cvr_button_first_screen, is_deleted, 
      ctime, mtime)
    values (#{pageId,jdbcType=BIGINT}, #{mainColor,jdbcType=INTEGER}, #{pageHeight,jdbcType=REAL}, 
      #{pageCompCount,jdbcType=INTEGER}, #{ocrContentLength,jdbcType=INTEGER}, #{ocrContent,jdbcType=VARCHAR}, 
      #{pageStyleType,jdbcType=VARCHAR}, #{pageFeatureType,jdbcType=VARCHAR}, #{pageFormFirstScreen,jdbcType=INTEGER}, 
      #{pageFormInputCount,jdbcType=INTEGER}, #{pageFormSelectCount,jdbcType=INTEGER}, 
      #{pageFormPercent,jdbcType=REAL}, #{cvrButtonFirstScreen,jdbcType=INTEGER}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      page_id = values(page_id),
      main_color = values(main_color),
      page_height = values(page_height),
      page_comp_count = values(page_comp_count),
      ocr_content_length = values(ocr_content_length),
      ocr_content = values(ocr_content),
      page_style_type = values(page_style_type),
      page_feature_type = values(page_feature_type),
      page_form_first_screen = values(page_form_first_screen),
      page_form_input_count = values(page_form_input_count),
      page_form_select_count = values(page_form_select_count),
      page_form_percent = values(page_form_percent),
      cvr_button_first_screen = values(cvr_button_first_screen),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mgk_cvr
      (page_id,main_color,page_height,page_comp_count,ocr_content_length,ocr_content,page_style_type,page_feature_type,page_form_first_screen,page_form_input_count,page_form_select_count,page_form_percent,cvr_button_first_screen,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.pageId,jdbcType=BIGINT},
        #{item.mainColor,jdbcType=INTEGER},
        #{item.pageHeight,jdbcType=REAL},
        #{item.pageCompCount,jdbcType=INTEGER},
        #{item.ocrContentLength,jdbcType=INTEGER},
        #{item.ocrContent,jdbcType=VARCHAR},
        #{item.pageStyleType,jdbcType=VARCHAR},
        #{item.pageFeatureType,jdbcType=VARCHAR},
        #{item.pageFormFirstScreen,jdbcType=INTEGER},
        #{item.pageFormInputCount,jdbcType=INTEGER},
        #{item.pageFormSelectCount,jdbcType=INTEGER},
        #{item.pageFormPercent,jdbcType=REAL},
        #{item.cvrButtonFirstScreen,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mgk_cvr
      (page_id,main_color,page_height,page_comp_count,ocr_content_length,ocr_content,page_style_type,page_feature_type,page_form_first_screen,page_form_input_count,page_form_select_count,page_form_percent,cvr_button_first_screen,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.pageId,jdbcType=BIGINT},
        #{item.mainColor,jdbcType=INTEGER},
        #{item.pageHeight,jdbcType=REAL},
        #{item.pageCompCount,jdbcType=INTEGER},
        #{item.ocrContentLength,jdbcType=INTEGER},
        #{item.ocrContent,jdbcType=VARCHAR},
        #{item.pageStyleType,jdbcType=VARCHAR},
        #{item.pageFeatureType,jdbcType=VARCHAR},
        #{item.pageFormFirstScreen,jdbcType=INTEGER},
        #{item.pageFormInputCount,jdbcType=INTEGER},
        #{item.pageFormSelectCount,jdbcType=INTEGER},
        #{item.pageFormPercent,jdbcType=REAL},
        #{item.cvrButtonFirstScreen,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      page_id = values(page_id),
      main_color = values(main_color),
      page_height = values(page_height),
      page_comp_count = values(page_comp_count),
      ocr_content_length = values(ocr_content_length),
      ocr_content = values(ocr_content),
      page_style_type = values(page_style_type),
      page_feature_type = values(page_feature_type),
      page_form_first_screen = values(page_form_first_screen),
      page_form_input_count = values(page_form_input_count),
      page_form_select_count = values(page_form_select_count),
      page_form_percent = values(page_form_percent),
      cvr_button_first_screen = values(cvr_button_first_screen),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkCvrPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_cvr
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pageId != null">
        page_id,
      </if>
      <if test="mainColor != null">
        main_color,
      </if>
      <if test="pageHeight != null">
        page_height,
      </if>
      <if test="pageCompCount != null">
        page_comp_count,
      </if>
      <if test="ocrContentLength != null">
        ocr_content_length,
      </if>
      <if test="ocrContent != null">
        ocr_content,
      </if>
      <if test="pageStyleType != null">
        page_style_type,
      </if>
      <if test="pageFeatureType != null">
        page_feature_type,
      </if>
      <if test="pageFormFirstScreen != null">
        page_form_first_screen,
      </if>
      <if test="pageFormInputCount != null">
        page_form_input_count,
      </if>
      <if test="pageFormSelectCount != null">
        page_form_select_count,
      </if>
      <if test="pageFormPercent != null">
        page_form_percent,
      </if>
      <if test="cvrButtonFirstScreen != null">
        cvr_button_first_screen,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pageId != null">
        #{pageId,jdbcType=BIGINT},
      </if>
      <if test="mainColor != null">
        #{mainColor,jdbcType=INTEGER},
      </if>
      <if test="pageHeight != null">
        #{pageHeight,jdbcType=REAL},
      </if>
      <if test="pageCompCount != null">
        #{pageCompCount,jdbcType=INTEGER},
      </if>
      <if test="ocrContentLength != null">
        #{ocrContentLength,jdbcType=INTEGER},
      </if>
      <if test="ocrContent != null">
        #{ocrContent,jdbcType=VARCHAR},
      </if>
      <if test="pageStyleType != null">
        #{pageStyleType,jdbcType=VARCHAR},
      </if>
      <if test="pageFeatureType != null">
        #{pageFeatureType,jdbcType=VARCHAR},
      </if>
      <if test="pageFormFirstScreen != null">
        #{pageFormFirstScreen,jdbcType=INTEGER},
      </if>
      <if test="pageFormInputCount != null">
        #{pageFormInputCount,jdbcType=INTEGER},
      </if>
      <if test="pageFormSelectCount != null">
        #{pageFormSelectCount,jdbcType=INTEGER},
      </if>
      <if test="pageFormPercent != null">
        #{pageFormPercent,jdbcType=REAL},
      </if>
      <if test="cvrButtonFirstScreen != null">
        #{cvrButtonFirstScreen,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="pageId != null">
        page_id = values(page_id),
      </if>
      <if test="mainColor != null">
        main_color = values(main_color),
      </if>
      <if test="pageHeight != null">
        page_height = values(page_height),
      </if>
      <if test="pageCompCount != null">
        page_comp_count = values(page_comp_count),
      </if>
      <if test="ocrContentLength != null">
        ocr_content_length = values(ocr_content_length),
      </if>
      <if test="ocrContent != null">
        ocr_content = values(ocr_content),
      </if>
      <if test="pageStyleType != null">
        page_style_type = values(page_style_type),
      </if>
      <if test="pageFeatureType != null">
        page_feature_type = values(page_feature_type),
      </if>
      <if test="pageFormFirstScreen != null">
        page_form_first_screen = values(page_form_first_screen),
      </if>
      <if test="pageFormInputCount != null">
        page_form_input_count = values(page_form_input_count),
      </if>
      <if test="pageFormSelectCount != null">
        page_form_select_count = values(page_form_select_count),
      </if>
      <if test="pageFormPercent != null">
        page_form_percent = values(page_form_percent),
      </if>
      <if test="cvrButtonFirstScreen != null">
        cvr_button_first_screen = values(cvr_button_first_screen),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>