<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.dao.SobotAdComplainDao">
  <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.po.SobotAdComplainPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="mid" jdbcType="BIGINT" property="mid" />
    <result column="buvid" jdbcType="VARCHAR" property="buvid" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="sales_type" jdbcType="INTEGER" property="salesType" />
    <result column="creative_id" jdbcType="BIGINT" property="creativeId" />
    <result column="source_id" jdbcType="INTEGER" property="sourceId" />
    <result column="complain_type" jdbcType="TINYINT" property="complainType" />
    <result column="complain_text" jdbcType="VARCHAR" property="complainText" />
    <result column="ticket_id" jdbcType="VARCHAR" property="ticketId" />
    <result column="is_sent" jdbcType="INTEGER" property="isSent" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="second_complain_type" jdbcType="TINYINT" property="secondComplainType" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="p_id" jdbcType="BIGINT" property="pId" />
    <result column="request_id" jdbcType="VARCHAR" property="requestId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.bilibili.mgk.platform.biz.po.SobotAdComplainPo">
    <result column="complain_detail" jdbcType="LONGVARCHAR" property="complainDetail" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, mid, buvid, email, sales_type, creative_id, source_id, complain_type, complain_text, 
    ticket_id, is_sent, is_deleted, ctime, mtime, second_complain_type, status, remark, 
    p_id, request_id
  </sql>
  <sql id="Blob_Column_List">
    complain_detail
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.bilibili.mgk.platform.biz.po.SobotAdComplainPoExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from sobot_ad_complain
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.bilibili.mgk.platform.biz.po.SobotAdComplainPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sobot_ad_complain
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from sobot_ad_complain
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from sobot_ad_complain
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.mgk.platform.biz.po.SobotAdComplainPoExample">
    delete from sobot_ad_complain
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.mgk.platform.biz.po.SobotAdComplainPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sobot_ad_complain (mid, buvid, email, 
      sales_type, creative_id, source_id, 
      complain_type, complain_text, ticket_id, 
      is_sent, is_deleted, ctime, 
      mtime, second_complain_type, status, 
      remark, p_id, request_id, 
      complain_detail)
    values (#{mid,jdbcType=BIGINT}, #{buvid,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, 
      #{salesType,jdbcType=INTEGER}, #{creativeId,jdbcType=BIGINT}, #{sourceId,jdbcType=INTEGER}, 
      #{complainType,jdbcType=TINYINT}, #{complainText,jdbcType=VARCHAR}, #{ticketId,jdbcType=VARCHAR}, 
      #{isSent,jdbcType=INTEGER}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{secondComplainType,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, 
      #{remark,jdbcType=VARCHAR}, #{pId,jdbcType=BIGINT}, #{requestId,jdbcType=VARCHAR}, 
      #{complainDetail,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.mgk.platform.biz.po.SobotAdComplainPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sobot_ad_complain
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mid != null">
        mid,
      </if>
      <if test="buvid != null">
        buvid,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="salesType != null">
        sales_type,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="complainType != null">
        complain_type,
      </if>
      <if test="complainText != null">
        complain_text,
      </if>
      <if test="ticketId != null">
        ticket_id,
      </if>
      <if test="isSent != null">
        is_sent,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="secondComplainType != null">
        second_complain_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="pId != null">
        p_id,
      </if>
      <if test="requestId != null">
        request_id,
      </if>
      <if test="complainDetail != null">
        complain_detail,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mid != null">
        #{mid,jdbcType=BIGINT},
      </if>
      <if test="buvid != null">
        #{buvid,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="salesType != null">
        #{salesType,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=BIGINT},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=INTEGER},
      </if>
      <if test="complainType != null">
        #{complainType,jdbcType=TINYINT},
      </if>
      <if test="complainText != null">
        #{complainText,jdbcType=VARCHAR},
      </if>
      <if test="ticketId != null">
        #{ticketId,jdbcType=VARCHAR},
      </if>
      <if test="isSent != null">
        #{isSent,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="secondComplainType != null">
        #{secondComplainType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="pId != null">
        #{pId,jdbcType=BIGINT},
      </if>
      <if test="requestId != null">
        #{requestId,jdbcType=VARCHAR},
      </if>
      <if test="complainDetail != null">
        #{complainDetail,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.mgk.platform.biz.po.SobotAdComplainPoExample" resultType="java.lang.Long">
    select count(*) from sobot_ad_complain
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sobot_ad_complain
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.mid != null">
        mid = #{record.mid,jdbcType=BIGINT},
      </if>
      <if test="record.buvid != null">
        buvid = #{record.buvid,jdbcType=VARCHAR},
      </if>
      <if test="record.email != null">
        email = #{record.email,jdbcType=VARCHAR},
      </if>
      <if test="record.salesType != null">
        sales_type = #{record.salesType,jdbcType=INTEGER},
      </if>
      <if test="record.creativeId != null">
        creative_id = #{record.creativeId,jdbcType=BIGINT},
      </if>
      <if test="record.sourceId != null">
        source_id = #{record.sourceId,jdbcType=INTEGER},
      </if>
      <if test="record.complainType != null">
        complain_type = #{record.complainType,jdbcType=TINYINT},
      </if>
      <if test="record.complainText != null">
        complain_text = #{record.complainText,jdbcType=VARCHAR},
      </if>
      <if test="record.ticketId != null">
        ticket_id = #{record.ticketId,jdbcType=VARCHAR},
      </if>
      <if test="record.isSent != null">
        is_sent = #{record.isSent,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.secondComplainType != null">
        second_complain_type = #{record.secondComplainType,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.pId != null">
        p_id = #{record.pId,jdbcType=BIGINT},
      </if>
      <if test="record.requestId != null">
        request_id = #{record.requestId,jdbcType=VARCHAR},
      </if>
      <if test="record.complainDetail != null">
        complain_detail = #{record.complainDetail,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update sobot_ad_complain
    set id = #{record.id,jdbcType=INTEGER},
      mid = #{record.mid,jdbcType=BIGINT},
      buvid = #{record.buvid,jdbcType=VARCHAR},
      email = #{record.email,jdbcType=VARCHAR},
      sales_type = #{record.salesType,jdbcType=INTEGER},
      creative_id = #{record.creativeId,jdbcType=BIGINT},
      source_id = #{record.sourceId,jdbcType=INTEGER},
      complain_type = #{record.complainType,jdbcType=TINYINT},
      complain_text = #{record.complainText,jdbcType=VARCHAR},
      ticket_id = #{record.ticketId,jdbcType=VARCHAR},
      is_sent = #{record.isSent,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      second_complain_type = #{record.secondComplainType,jdbcType=TINYINT},
      status = #{record.status,jdbcType=TINYINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      p_id = #{record.pId,jdbcType=BIGINT},
      request_id = #{record.requestId,jdbcType=VARCHAR},
      complain_detail = #{record.complainDetail,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sobot_ad_complain
    set id = #{record.id,jdbcType=INTEGER},
      mid = #{record.mid,jdbcType=BIGINT},
      buvid = #{record.buvid,jdbcType=VARCHAR},
      email = #{record.email,jdbcType=VARCHAR},
      sales_type = #{record.salesType,jdbcType=INTEGER},
      creative_id = #{record.creativeId,jdbcType=BIGINT},
      source_id = #{record.sourceId,jdbcType=INTEGER},
      complain_type = #{record.complainType,jdbcType=TINYINT},
      complain_text = #{record.complainText,jdbcType=VARCHAR},
      ticket_id = #{record.ticketId,jdbcType=VARCHAR},
      is_sent = #{record.isSent,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      second_complain_type = #{record.secondComplainType,jdbcType=TINYINT},
      status = #{record.status,jdbcType=TINYINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      p_id = #{record.pId,jdbcType=BIGINT},
      request_id = #{record.requestId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.platform.biz.po.SobotAdComplainPo">
    update sobot_ad_complain
    <set>
      <if test="mid != null">
        mid = #{mid,jdbcType=BIGINT},
      </if>
      <if test="buvid != null">
        buvid = #{buvid,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="salesType != null">
        sales_type = #{salesType,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        creative_id = #{creativeId,jdbcType=BIGINT},
      </if>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=INTEGER},
      </if>
      <if test="complainType != null">
        complain_type = #{complainType,jdbcType=TINYINT},
      </if>
      <if test="complainText != null">
        complain_text = #{complainText,jdbcType=VARCHAR},
      </if>
      <if test="ticketId != null">
        ticket_id = #{ticketId,jdbcType=VARCHAR},
      </if>
      <if test="isSent != null">
        is_sent = #{isSent,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="secondComplainType != null">
        second_complain_type = #{secondComplainType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="pId != null">
        p_id = #{pId,jdbcType=BIGINT},
      </if>
      <if test="requestId != null">
        request_id = #{requestId,jdbcType=VARCHAR},
      </if>
      <if test="complainDetail != null">
        complain_detail = #{complainDetail,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.bilibili.mgk.platform.biz.po.SobotAdComplainPo">
    update sobot_ad_complain
    set mid = #{mid,jdbcType=BIGINT},
      buvid = #{buvid,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      sales_type = #{salesType,jdbcType=INTEGER},
      creative_id = #{creativeId,jdbcType=BIGINT},
      source_id = #{sourceId,jdbcType=INTEGER},
      complain_type = #{complainType,jdbcType=TINYINT},
      complain_text = #{complainText,jdbcType=VARCHAR},
      ticket_id = #{ticketId,jdbcType=VARCHAR},
      is_sent = #{isSent,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      second_complain_type = #{secondComplainType,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      p_id = #{pId,jdbcType=BIGINT},
      request_id = #{requestId,jdbcType=VARCHAR},
      complain_detail = #{complainDetail,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.platform.biz.po.SobotAdComplainPo">
    update sobot_ad_complain
    set mid = #{mid,jdbcType=BIGINT},
      buvid = #{buvid,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      sales_type = #{salesType,jdbcType=INTEGER},
      creative_id = #{creativeId,jdbcType=BIGINT},
      source_id = #{sourceId,jdbcType=INTEGER},
      complain_type = #{complainType,jdbcType=TINYINT},
      complain_text = #{complainText,jdbcType=VARCHAR},
      ticket_id = #{ticketId,jdbcType=VARCHAR},
      is_sent = #{isSent,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      second_complain_type = #{secondComplainType,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      p_id = #{pId,jdbcType=BIGINT},
      request_id = #{requestId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.mgk.platform.biz.po.SobotAdComplainPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sobot_ad_complain (mid, buvid, email, 
      sales_type, creative_id, source_id, 
      complain_type, complain_text, ticket_id, 
      is_sent, is_deleted, ctime, 
      mtime, second_complain_type, status, 
      remark, p_id, request_id, 
      complain_detail)
    values (#{mid,jdbcType=BIGINT}, #{buvid,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, 
      #{salesType,jdbcType=INTEGER}, #{creativeId,jdbcType=BIGINT}, #{sourceId,jdbcType=INTEGER}, 
      #{complainType,jdbcType=TINYINT}, #{complainText,jdbcType=VARCHAR}, #{ticketId,jdbcType=VARCHAR}, 
      #{isSent,jdbcType=INTEGER}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{secondComplainType,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, 
      #{remark,jdbcType=VARCHAR}, #{pId,jdbcType=BIGINT}, #{requestId,jdbcType=VARCHAR}, 
      #{complainDetail,jdbcType=LONGVARCHAR})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      mid = values(mid),
      buvid = values(buvid),
      email = values(email),
      sales_type = values(sales_type),
      creative_id = values(creative_id),
      source_id = values(source_id),
      complain_type = values(complain_type),
      complain_text = values(complain_text),
      ticket_id = values(ticket_id),
      is_sent = values(is_sent),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      second_complain_type = values(second_complain_type),
      status = values(status),
      remark = values(remark),
      p_id = values(p_id),
      request_id = values(request_id),
      complain_detail = values(complain_detail),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      sobot_ad_complain
      (mid,buvid,email,sales_type,creative_id,source_id,complain_type,complain_text,ticket_id,is_sent,is_deleted,ctime,mtime,second_complain_type,status,remark,p_id,request_id,complain_detail)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.mid,jdbcType=BIGINT},
        #{item.buvid,jdbcType=VARCHAR},
        #{item.email,jdbcType=VARCHAR},
        #{item.salesType,jdbcType=INTEGER},
        #{item.creativeId,jdbcType=BIGINT},
        #{item.sourceId,jdbcType=INTEGER},
        #{item.complainType,jdbcType=TINYINT},
        #{item.complainText,jdbcType=VARCHAR},
        #{item.ticketId,jdbcType=VARCHAR},
        #{item.isSent,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.secondComplainType,jdbcType=TINYINT},
        #{item.status,jdbcType=TINYINT},
        #{item.remark,jdbcType=VARCHAR},
        #{item.pId,jdbcType=BIGINT},
        #{item.requestId,jdbcType=VARCHAR},
        #{item.complainDetail,jdbcType=LONGVARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      sobot_ad_complain
      (mid,buvid,email,sales_type,creative_id,source_id,complain_type,complain_text,ticket_id,is_sent,is_deleted,ctime,mtime,second_complain_type,status,remark,p_id,request_id,complain_detail)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.mid,jdbcType=BIGINT},
        #{item.buvid,jdbcType=VARCHAR},
        #{item.email,jdbcType=VARCHAR},
        #{item.salesType,jdbcType=INTEGER},
        #{item.creativeId,jdbcType=BIGINT},
        #{item.sourceId,jdbcType=INTEGER},
        #{item.complainType,jdbcType=TINYINT},
        #{item.complainText,jdbcType=VARCHAR},
        #{item.ticketId,jdbcType=VARCHAR},
        #{item.isSent,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.secondComplainType,jdbcType=TINYINT},
        #{item.status,jdbcType=TINYINT},
        #{item.remark,jdbcType=VARCHAR},
        #{item.pId,jdbcType=BIGINT},
        #{item.requestId,jdbcType=VARCHAR},
        #{item.complainDetail,jdbcType=LONGVARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      mid = values(mid),
      buvid = values(buvid),
      email = values(email),
      sales_type = values(sales_type),
      creative_id = values(creative_id),
      source_id = values(source_id),
      complain_type = values(complain_type),
      complain_text = values(complain_text),
      ticket_id = values(ticket_id),
      is_sent = values(is_sent),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      second_complain_type = values(second_complain_type),
      status = values(status),
      remark = values(remark),
      p_id = values(p_id),
      request_id = values(request_id),
      complain_detail = values(complain_detail),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.mgk.platform.biz.po.SobotAdComplainPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sobot_ad_complain
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mid != null">
        mid,
      </if>
      <if test="buvid != null">
        buvid,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="salesType != null">
        sales_type,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="complainType != null">
        complain_type,
      </if>
      <if test="complainText != null">
        complain_text,
      </if>
      <if test="ticketId != null">
        ticket_id,
      </if>
      <if test="isSent != null">
        is_sent,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="secondComplainType != null">
        second_complain_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="pId != null">
        p_id,
      </if>
      <if test="requestId != null">
        request_id,
      </if>
      <if test="complainDetail != null">
        complain_detail,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mid != null">
        #{mid,jdbcType=BIGINT},
      </if>
      <if test="buvid != null">
        #{buvid,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="salesType != null">
        #{salesType,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=BIGINT},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=INTEGER},
      </if>
      <if test="complainType != null">
        #{complainType,jdbcType=TINYINT},
      </if>
      <if test="complainText != null">
        #{complainText,jdbcType=VARCHAR},
      </if>
      <if test="ticketId != null">
        #{ticketId,jdbcType=VARCHAR},
      </if>
      <if test="isSent != null">
        #{isSent,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="secondComplainType != null">
        #{secondComplainType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="pId != null">
        #{pId,jdbcType=BIGINT},
      </if>
      <if test="requestId != null">
        #{requestId,jdbcType=VARCHAR},
      </if>
      <if test="complainDetail != null">
        #{complainDetail,jdbcType=LONGVARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="mid != null">
        mid = values(mid),
      </if>
      <if test="buvid != null">
        buvid = values(buvid),
      </if>
      <if test="email != null">
        email = values(email),
      </if>
      <if test="salesType != null">
        sales_type = values(sales_type),
      </if>
      <if test="creativeId != null">
        creative_id = values(creative_id),
      </if>
      <if test="sourceId != null">
        source_id = values(source_id),
      </if>
      <if test="complainType != null">
        complain_type = values(complain_type),
      </if>
      <if test="complainText != null">
        complain_text = values(complain_text),
      </if>
      <if test="ticketId != null">
        ticket_id = values(ticket_id),
      </if>
      <if test="isSent != null">
        is_sent = values(is_sent),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="secondComplainType != null">
        second_complain_type = values(second_complain_type),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="remark != null">
        remark = values(remark),
      </if>
      <if test="pId != null">
        p_id = values(p_id),
      </if>
      <if test="requestId != null">
        request_id = values(request_id),
      </if>
      <if test="complainDetail != null">
        complain_detail = values(complain_detail),
      </if>
    </trim>
  </insert>
</mapper>