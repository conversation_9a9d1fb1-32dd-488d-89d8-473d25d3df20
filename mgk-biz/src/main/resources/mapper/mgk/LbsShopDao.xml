<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.dao.LbsShopDao">
  <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.po.LbsShopPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="form_item_id" jdbcType="BIGINT" property="formItemId" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="shop_name" jdbcType="VARCHAR" property="shopName" />
    <result column="shop_address" jdbcType="VARCHAR" property="shopAddress" />
    <result column="lng" jdbcType="DECIMAL" property="lng" />
    <result column="lat" jdbcType="DECIMAL" property="lat" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="product" jdbcType="VARCHAR" property="product" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, form_item_id, city_code, province_code, shop_name, shop_address, lng, lat, ctime, 
    mtime, is_deleted, product
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.mgk.platform.biz.po.LbsShopPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lbs_shop
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lbs_shop
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lbs_shop
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.mgk.platform.biz.po.LbsShopPoExample">
    delete from lbs_shop
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.mgk.platform.biz.po.LbsShopPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lbs_shop (form_item_id, city_code, province_code, 
      shop_name, shop_address, lng, 
      lat, ctime, mtime, 
      is_deleted, product)
    values (#{formItemId,jdbcType=BIGINT}, #{cityCode,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR}, 
      #{shopName,jdbcType=VARCHAR}, #{shopAddress,jdbcType=VARCHAR}, #{lng,jdbcType=DECIMAL}, 
      #{lat,jdbcType=DECIMAL}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT}, #{product,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.mgk.platform.biz.po.LbsShopPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lbs_shop
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="formItemId != null">
        form_item_id,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="shopName != null">
        shop_name,
      </if>
      <if test="shopAddress != null">
        shop_address,
      </if>
      <if test="lng != null">
        lng,
      </if>
      <if test="lat != null">
        lat,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="product != null">
        product,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="formItemId != null">
        #{formItemId,jdbcType=BIGINT},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="shopName != null">
        #{shopName,jdbcType=VARCHAR},
      </if>
      <if test="shopAddress != null">
        #{shopAddress,jdbcType=VARCHAR},
      </if>
      <if test="lng != null">
        #{lng,jdbcType=DECIMAL},
      </if>
      <if test="lat != null">
        #{lat,jdbcType=DECIMAL},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="product != null">
        #{product,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.mgk.platform.biz.po.LbsShopPoExample" resultType="java.lang.Long">
    select count(*) from lbs_shop
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lbs_shop
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.formItemId != null">
        form_item_id = #{record.formItemId,jdbcType=BIGINT},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.shopName != null">
        shop_name = #{record.shopName,jdbcType=VARCHAR},
      </if>
      <if test="record.shopAddress != null">
        shop_address = #{record.shopAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.lng != null">
        lng = #{record.lng,jdbcType=DECIMAL},
      </if>
      <if test="record.lat != null">
        lat = #{record.lat,jdbcType=DECIMAL},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.product != null">
        product = #{record.product,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lbs_shop
    set id = #{record.id,jdbcType=BIGINT},
      form_item_id = #{record.formItemId,jdbcType=BIGINT},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      shop_name = #{record.shopName,jdbcType=VARCHAR},
      shop_address = #{record.shopAddress,jdbcType=VARCHAR},
      lng = #{record.lng,jdbcType=DECIMAL},
      lat = #{record.lat,jdbcType=DECIMAL},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      product = #{record.product,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.platform.biz.po.LbsShopPo">
    update lbs_shop
    <set>
      <if test="formItemId != null">
        form_item_id = #{formItemId,jdbcType=BIGINT},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        province_code = #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="shopName != null">
        shop_name = #{shopName,jdbcType=VARCHAR},
      </if>
      <if test="shopAddress != null">
        shop_address = #{shopAddress,jdbcType=VARCHAR},
      </if>
      <if test="lng != null">
        lng = #{lng,jdbcType=DECIMAL},
      </if>
      <if test="lat != null">
        lat = #{lat,jdbcType=DECIMAL},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="product != null">
        product = #{product,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.platform.biz.po.LbsShopPo">
    update lbs_shop
    set form_item_id = #{formItemId,jdbcType=BIGINT},
      city_code = #{cityCode,jdbcType=VARCHAR},
      province_code = #{provinceCode,jdbcType=VARCHAR},
      shop_name = #{shopName,jdbcType=VARCHAR},
      shop_address = #{shopAddress,jdbcType=VARCHAR},
      lng = #{lng,jdbcType=DECIMAL},
      lat = #{lat,jdbcType=DECIMAL},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      product = #{product,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.mgk.platform.biz.po.LbsShopPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lbs_shop (form_item_id, city_code, province_code, 
      shop_name, shop_address, lng, 
      lat, ctime, mtime, 
      is_deleted, product)
    values (#{formItemId,jdbcType=BIGINT}, #{cityCode,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR}, 
      #{shopName,jdbcType=VARCHAR}, #{shopAddress,jdbcType=VARCHAR}, #{lng,jdbcType=DECIMAL}, 
      #{lat,jdbcType=DECIMAL}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT}, #{product,jdbcType=VARCHAR})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      form_item_id = values(form_item_id),
      city_code = values(city_code),
      province_code = values(province_code),
      shop_name = values(shop_name),
      shop_address = values(shop_address),
      lng = values(lng),
      lat = values(lat),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      product = values(product),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lbs_shop
      (form_item_id,city_code,province_code,shop_name,shop_address,lng,lat,ctime,mtime,is_deleted,product)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.formItemId,jdbcType=BIGINT},
        #{item.cityCode,jdbcType=VARCHAR},
        #{item.provinceCode,jdbcType=VARCHAR},
        #{item.shopName,jdbcType=VARCHAR},
        #{item.shopAddress,jdbcType=VARCHAR},
        #{item.lng,jdbcType=DECIMAL},
        #{item.lat,jdbcType=DECIMAL},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.product,jdbcType=VARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lbs_shop
      (form_item_id,city_code,province_code,shop_name,shop_address,lng,lat,ctime,mtime,is_deleted,product)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.formItemId,jdbcType=BIGINT},
        #{item.cityCode,jdbcType=VARCHAR},
        #{item.provinceCode,jdbcType=VARCHAR},
        #{item.shopName,jdbcType=VARCHAR},
        #{item.shopAddress,jdbcType=VARCHAR},
        #{item.lng,jdbcType=DECIMAL},
        #{item.lat,jdbcType=DECIMAL},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.product,jdbcType=VARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      form_item_id = values(form_item_id),
      city_code = values(city_code),
      province_code = values(province_code),
      shop_name = values(shop_name),
      shop_address = values(shop_address),
      lng = values(lng),
      lat = values(lat),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      product = values(product),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.mgk.platform.biz.po.LbsShopPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lbs_shop
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="formItemId != null">
        form_item_id,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="shopName != null">
        shop_name,
      </if>
      <if test="shopAddress != null">
        shop_address,
      </if>
      <if test="lng != null">
        lng,
      </if>
      <if test="lat != null">
        lat,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="product != null">
        product,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="formItemId != null">
        #{formItemId,jdbcType=BIGINT},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="shopName != null">
        #{shopName,jdbcType=VARCHAR},
      </if>
      <if test="shopAddress != null">
        #{shopAddress,jdbcType=VARCHAR},
      </if>
      <if test="lng != null">
        #{lng,jdbcType=DECIMAL},
      </if>
      <if test="lat != null">
        #{lat,jdbcType=DECIMAL},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="product != null">
        #{product,jdbcType=VARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="formItemId != null">
        form_item_id = values(form_item_id),
      </if>
      <if test="cityCode != null">
        city_code = values(city_code),
      </if>
      <if test="provinceCode != null">
        province_code = values(province_code),
      </if>
      <if test="shopName != null">
        shop_name = values(shop_name),
      </if>
      <if test="shopAddress != null">
        shop_address = values(shop_address),
      </if>
      <if test="lng != null">
        lng = values(lng),
      </if>
      <if test="lat != null">
        lat = values(lat),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="product != null">
        product = values(product),
      </if>
    </trim>
  </insert>
</mapper>