<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.dao.MgkFormDataDao">
  <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.po.MgkFormDataPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="page_id" jdbcType="BIGINT" property="pageId" />
    <result column="form_id" jdbcType="BIGINT" property="formId" />
    <result column="creative_id" jdbcType="BIGINT" property="creativeId" />
    <result column="sales_type" jdbcType="INTEGER" property="salesType" />
    <result column="mid" jdbcType="BIGINT" property="mid" />
    <result column="imei" jdbcType="VARCHAR" property="imei" />
    <result column="buvid" jdbcType="VARCHAR" property="buvid" />
    <result column="device_id" jdbcType="VARCHAR" property="deviceId" />
    <result column="os" jdbcType="TINYINT" property="os" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="request_id" jdbcType="VARCHAR" property="requestId" />
    <result column="source_id" jdbcType="INTEGER" property="sourceId" />
    <result column="track_id" jdbcType="VARCHAR" property="trackId" />
    <result column="report_status" jdbcType="TINYINT" property="reportStatus" />
    <result column="phone_order_status" jdbcType="TINYINT" property="phoneOrderStatus" />
    <result column="phone_order_id" jdbcType="BIGINT" property="phoneOrderId" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="product_id" jdbcType="INTEGER" property="productId" />
    <result column="is_cheat" jdbcType="TINYINT" property="isCheat" />
    <result column="event_source_type" jdbcType="TINYINT" property="eventSourceType" />
    <result column="avid" jdbcType="BIGINT" property="avid" />
    <result column="from_track_id" jdbcType="VARCHAR" property="fromTrackId" />
    <result column="assembly_track_id" jdbcType="VARCHAR" property="assemblyTrackId" />
    <result column="is_live_assembly_card" jdbcType="TINYINT" property="isLiveAssemblyCard" />
    <result column="report_source" jdbcType="TINYINT" property="reportSource" />
    <result column="sharer_uid" jdbcType="BIGINT" property="sharerUid" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, page_id, form_id, creative_id, sales_type, mid, imei, buvid, device_id, 
    os, is_deleted, ctime, mtime, request_id, source_id, track_id, report_status, phone_order_status, 
    phone_order_id, customer_id, product_id, is_cheat, event_source_type, avid, from_track_id, 
    assembly_track_id, is_live_assembly_card, report_source,sharer_uid
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormDataPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mgk_form_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mgk_form_data
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mgk_form_data
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormDataPoExample">
    delete from mgk_form_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormDataPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_form_data (account_id, page_id, form_id,
    creative_id, sales_type, mid,
    imei, buvid, device_id,
    os, is_deleted, ctime,
    mtime, request_id, source_id,
    track_id, report_status, phone_order_status,
    phone_order_id, customer_id, product_id,
    is_cheat, event_source_type, avid,
    from_track_id, assembly_track_id, is_live_assembly_card,
    report_source,sharer_uid)
    values (#{accountId,jdbcType=INTEGER}, #{pageId,jdbcType=BIGINT}, #{formId,jdbcType=BIGINT},
    #{creativeId,jdbcType=BIGINT}, #{salesType,jdbcType=INTEGER}, #{mid,jdbcType=BIGINT},
    #{imei,jdbcType=VARCHAR}, #{buvid,jdbcType=VARCHAR}, #{deviceId,jdbcType=VARCHAR},
    #{os,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP},
    #{mtime,jdbcType=TIMESTAMP}, #{requestId,jdbcType=VARCHAR}, #{sourceId,jdbcType=INTEGER},
    #{trackId,jdbcType=VARCHAR}, #{reportStatus,jdbcType=TINYINT}, #{phoneOrderStatus,jdbcType=TINYINT},
    #{phoneOrderId,jdbcType=BIGINT}, #{customerId,jdbcType=INTEGER}, #{productId,jdbcType=INTEGER},
    #{isCheat,jdbcType=TINYINT}, #{eventSourceType,jdbcType=TINYINT}, #{avid,jdbcType=BIGINT},
    #{fromTrackId,jdbcType=VARCHAR}, #{assemblyTrackId,jdbcType=VARCHAR}, #{isLiveAssemblyCard,jdbcType=TINYINT},
    #{reportSource,jdbcType=TINYINT},#{sharerUid,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormDataPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_form_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="pageId != null">
        page_id,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="salesType != null">
        sales_type,
      </if>
      <if test="mid != null">
        mid,
      </if>
      <if test="imei != null">
        imei,
      </if>
      <if test="buvid != null">
        buvid,
      </if>
      <if test="deviceId != null">
        device_id,
      </if>
      <if test="os != null">
        os,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="requestId != null">
        request_id,
      </if>
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="trackId != null">
        track_id,
      </if>
      <if test="reportStatus != null">
        report_status,
      </if>
      <if test="phoneOrderStatus != null">
        phone_order_status,
      </if>
      <if test="phoneOrderId != null">
        phone_order_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="isCheat != null">
        is_cheat,
      </if>
      <if test="eventSourceType != null">
        event_source_type,
      </if>
      <if test="avid != null">
        avid,
      </if>
      <if test="fromTrackId != null">
        from_track_id,
      </if>
      <if test="assemblyTrackId != null">
        assembly_track_id,
      </if>
      <if test="isLiveAssemblyCard != null">
        is_live_assembly_card,
      </if>
      <if test="reportSource != null">
        report_source,
      </if>
      <if test="sharerUid != null">
        sharer_uid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="pageId != null">
        #{pageId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=BIGINT},
      </if>
      <if test="salesType != null">
        #{salesType,jdbcType=INTEGER},
      </if>
      <if test="mid != null">
        #{mid,jdbcType=BIGINT},
      </if>
      <if test="imei != null">
        #{imei,jdbcType=VARCHAR},
      </if>
      <if test="buvid != null">
        #{buvid,jdbcType=VARCHAR},
      </if>
      <if test="deviceId != null">
        #{deviceId,jdbcType=VARCHAR},
      </if>
      <if test="os != null">
        #{os,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="requestId != null">
        #{requestId,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=INTEGER},
      </if>
      <if test="trackId != null">
        #{trackId,jdbcType=VARCHAR},
      </if>
      <if test="reportStatus != null">
        #{reportStatus,jdbcType=TINYINT},
      </if>
      <if test="phoneOrderStatus != null">
        #{phoneOrderStatus,jdbcType=TINYINT},
      </if>
      <if test="phoneOrderId != null">
        #{phoneOrderId,jdbcType=BIGINT},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="isCheat != null">
        #{isCheat,jdbcType=TINYINT},
      </if>
      <if test="eventSourceType != null">
        #{eventSourceType,jdbcType=TINYINT},
      </if>
      <if test="avid != null">
        #{avid,jdbcType=BIGINT},
      </if>
      <if test="fromTrackId != null">
        #{fromTrackId,jdbcType=VARCHAR},
      </if>
      <if test="assemblyTrackId != null">
        #{assemblyTrackId,jdbcType=VARCHAR},
      </if>
      <if test="isLiveAssemblyCard != null">
        #{isLiveAssemblyCard,jdbcType=TINYINT},
      </if>
      <if test="reportSource != null">
        #{reportSource,jdbcType=TINYINT},
      </if>
      <if test="sharerUid != null">
        #{sharerUid,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormDataPoExample" resultType="java.lang.Long">
    select count(*) from mgk_form_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mgk_form_data
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.pageId != null">
        page_id = #{record.pageId,jdbcType=BIGINT},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=BIGINT},
      </if>
      <if test="record.creativeId != null">
        creative_id = #{record.creativeId,jdbcType=BIGINT},
      </if>
      <if test="record.salesType != null">
        sales_type = #{record.salesType,jdbcType=INTEGER},
      </if>
      <if test="record.mid != null">
        mid = #{record.mid,jdbcType=BIGINT},
      </if>
      <if test="record.imei != null">
        imei = #{record.imei,jdbcType=VARCHAR},
      </if>
      <if test="record.buvid != null">
        buvid = #{record.buvid,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceId != null">
        device_id = #{record.deviceId,jdbcType=VARCHAR},
      </if>
      <if test="record.os != null">
        os = #{record.os,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.requestId != null">
        request_id = #{record.requestId,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceId != null">
        source_id = #{record.sourceId,jdbcType=INTEGER},
      </if>
      <if test="record.trackId != null">
        track_id = #{record.trackId,jdbcType=VARCHAR},
      </if>
      <if test="record.reportStatus != null">
        report_status = #{record.reportStatus,jdbcType=TINYINT},
      </if>
      <if test="record.phoneOrderStatus != null">
        phone_order_status = #{record.phoneOrderStatus,jdbcType=TINYINT},
      </if>
      <if test="record.phoneOrderId != null">
        phone_order_id = #{record.phoneOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=INTEGER},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=INTEGER},
      </if>
      <if test="record.isCheat != null">
        is_cheat = #{record.isCheat,jdbcType=TINYINT},
      </if>
      <if test="record.eventSourceType != null">
        event_source_type = #{record.eventSourceType,jdbcType=TINYINT},
      </if>
      <if test="record.avid != null">
        avid = #{record.avid,jdbcType=BIGINT},
      </if>
      <if test="record.fromTrackId != null">
        from_track_id = #{record.fromTrackId,jdbcType=VARCHAR},
      </if>
      <if test="record.assemblyTrackId != null">
        assembly_track_id = #{record.assemblyTrackId,jdbcType=VARCHAR},
      </if>
      <if test="record.isLiveAssemblyCard != null">
        is_live_assembly_card = #{record.isLiveAssemblyCard,jdbcType=TINYINT},
      </if>
      <if test="record.reportSource != null">
        report_source = #{record.reportSource,jdbcType=TINYINT},
      </if>
      <if test="record.sharerUid != null">
        sharer_uid = #{record.sharerUid,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mgk_form_data
    set id = #{record.id,jdbcType=BIGINT},
    account_id = #{record.accountId,jdbcType=INTEGER},
    page_id = #{record.pageId,jdbcType=BIGINT},
    form_id = #{record.formId,jdbcType=BIGINT},
    creative_id = #{record.creativeId,jdbcType=BIGINT},
    sales_type = #{record.salesType,jdbcType=INTEGER},
    mid = #{record.mid,jdbcType=BIGINT},
    imei = #{record.imei,jdbcType=VARCHAR},
    buvid = #{record.buvid,jdbcType=VARCHAR},
    device_id = #{record.deviceId,jdbcType=VARCHAR},
    os = #{record.os,jdbcType=TINYINT},
    is_deleted = #{record.isDeleted,jdbcType=TINYINT},
    ctime = #{record.ctime,jdbcType=TIMESTAMP},
    mtime = #{record.mtime,jdbcType=TIMESTAMP},
    request_id = #{record.requestId,jdbcType=VARCHAR},
    source_id = #{record.sourceId,jdbcType=INTEGER},
    track_id = #{record.trackId,jdbcType=VARCHAR},
    report_status = #{record.reportStatus,jdbcType=TINYINT},
    phone_order_status = #{record.phoneOrderStatus,jdbcType=TINYINT},
    phone_order_id = #{record.phoneOrderId,jdbcType=BIGINT},
    customer_id = #{record.customerId,jdbcType=INTEGER},
    product_id = #{record.productId,jdbcType=INTEGER},
    is_cheat = #{record.isCheat,jdbcType=TINYINT},
    event_source_type = #{record.eventSourceType,jdbcType=TINYINT},
    avid = #{record.avid,jdbcType=BIGINT},
    from_track_id = #{record.fromTrackId,jdbcType=VARCHAR},
    assembly_track_id = #{record.assemblyTrackId,jdbcType=VARCHAR},
    is_live_assembly_card = #{record.isLiveAssemblyCard,jdbcType=TINYINT},
    report_source = #{record.reportSource,jdbcType=TINYINT},
    sharer_uid = #{record.sharerUid,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormDataPo">
    update mgk_form_data
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="pageId != null">
        page_id = #{pageId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        form_id = #{formId,jdbcType=BIGINT},
      </if>
      <if test="creativeId != null">
        creative_id = #{creativeId,jdbcType=BIGINT},
      </if>
      <if test="salesType != null">
        sales_type = #{salesType,jdbcType=INTEGER},
      </if>
      <if test="mid != null">
        mid = #{mid,jdbcType=BIGINT},
      </if>
      <if test="imei != null">
        imei = #{imei,jdbcType=VARCHAR},
      </if>
      <if test="buvid != null">
        buvid = #{buvid,jdbcType=VARCHAR},
      </if>
      <if test="deviceId != null">
        device_id = #{deviceId,jdbcType=VARCHAR},
      </if>
      <if test="os != null">
        os = #{os,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="requestId != null">
        request_id = #{requestId,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=INTEGER},
      </if>
      <if test="trackId != null">
        track_id = #{trackId,jdbcType=VARCHAR},
      </if>
      <if test="reportStatus != null">
        report_status = #{reportStatus,jdbcType=TINYINT},
      </if>
      <if test="phoneOrderStatus != null">
        phone_order_status = #{phoneOrderStatus,jdbcType=TINYINT},
      </if>
      <if test="phoneOrderId != null">
        phone_order_id = #{phoneOrderId,jdbcType=BIGINT},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=INTEGER},
      </if>
      <if test="isCheat != null">
        is_cheat = #{isCheat,jdbcType=TINYINT},
      </if>
      <if test="eventSourceType != null">
        event_source_type = #{eventSourceType,jdbcType=TINYINT},
      </if>
      <if test="avid != null">
        avid = #{avid,jdbcType=BIGINT},
      </if>
      <if test="fromTrackId != null">
        from_track_id = #{fromTrackId,jdbcType=VARCHAR},
      </if>
      <if test="assemblyTrackId != null">
        assembly_track_id = #{assemblyTrackId,jdbcType=VARCHAR},
      </if>
      <if test="isLiveAssemblyCard != null">
        is_live_assembly_card = #{isLiveAssemblyCard,jdbcType=TINYINT},
      </if>
      <if test="reportSource != null">
        report_source = #{reportSource,jdbcType=TINYINT},
      </if>
      <if test="sharerUid != null">
        sharer_uid = #{sharerUid,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormDataPo">
    update mgk_form_data
    set account_id = #{accountId,jdbcType=INTEGER},
        page_id = #{pageId,jdbcType=BIGINT},
        form_id = #{formId,jdbcType=BIGINT},
        creative_id = #{creativeId,jdbcType=BIGINT},
        sales_type = #{salesType,jdbcType=INTEGER},
        mid = #{mid,jdbcType=BIGINT},
        imei = #{imei,jdbcType=VARCHAR},
        buvid = #{buvid,jdbcType=VARCHAR},
        device_id = #{deviceId,jdbcType=VARCHAR},
        os = #{os,jdbcType=TINYINT},
        is_deleted = #{isDeleted,jdbcType=TINYINT},
        ctime = #{ctime,jdbcType=TIMESTAMP},
        mtime = #{mtime,jdbcType=TIMESTAMP},
        request_id = #{requestId,jdbcType=VARCHAR},
        source_id = #{sourceId,jdbcType=INTEGER},
        track_id = #{trackId,jdbcType=VARCHAR},
        report_status = #{reportStatus,jdbcType=TINYINT},
        phone_order_status = #{phoneOrderStatus,jdbcType=TINYINT},
        phone_order_id = #{phoneOrderId,jdbcType=BIGINT},
        customer_id = #{customerId,jdbcType=INTEGER},
        product_id = #{productId,jdbcType=INTEGER},
        is_cheat = #{isCheat,jdbcType=TINYINT},
        event_source_type = #{eventSourceType,jdbcType=TINYINT},
        avid = #{avid,jdbcType=BIGINT},
        from_track_id = #{fromTrackId,jdbcType=VARCHAR},
        assembly_track_id = #{assemblyTrackId,jdbcType=VARCHAR},
        is_live_assembly_card = #{isLiveAssemblyCard,jdbcType=TINYINT},
        report_source = #{reportSource,jdbcType=TINYINT},
        sharer_uid = #{sharerUid,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormDataPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_form_data (account_id, page_id, form_id,
    creative_id, sales_type, mid,
    imei, buvid, device_id,
    os, is_deleted, ctime,
    mtime, request_id, source_id,
    track_id, report_status, phone_order_status,
    phone_order_id, customer_id, product_id,
    is_cheat, event_source_type, avid,
    from_track_id, assembly_track_id, is_live_assembly_card,
    report_source,sharer_uid)
    values (#{accountId,jdbcType=INTEGER}, #{pageId,jdbcType=BIGINT}, #{formId,jdbcType=BIGINT},
    #{creativeId,jdbcType=BIGINT}, #{salesType,jdbcType=INTEGER}, #{mid,jdbcType=BIGINT},
    #{imei,jdbcType=VARCHAR}, #{buvid,jdbcType=VARCHAR}, #{deviceId,jdbcType=VARCHAR},
    #{os,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP},
    #{mtime,jdbcType=TIMESTAMP}, #{requestId,jdbcType=VARCHAR}, #{sourceId,jdbcType=INTEGER},
    #{trackId,jdbcType=VARCHAR}, #{reportStatus,jdbcType=TINYINT}, #{phoneOrderStatus,jdbcType=TINYINT},
    #{phoneOrderId,jdbcType=BIGINT}, #{customerId,jdbcType=INTEGER}, #{productId,jdbcType=INTEGER},
    #{isCheat,jdbcType=TINYINT}, #{eventSourceType,jdbcType=TINYINT}, #{avid,jdbcType=BIGINT},
    #{fromTrackId,jdbcType=VARCHAR}, #{assemblyTrackId,jdbcType=VARCHAR}, #{isLiveAssemblyCard,jdbcType=TINYINT},
    #{reportSource,jdbcType=TINYINT}, #{sharerUid,jdbcType=BIGINT})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      page_id = values(page_id),
      form_id = values(form_id),
      creative_id = values(creative_id),
      sales_type = values(sales_type),
      mid = values(mid),
      imei = values(imei),
      buvid = values(buvid),
      device_id = values(device_id),
      os = values(os),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      request_id = values(request_id),
      source_id = values(source_id),
      track_id = values(track_id),
      report_status = values(report_status),
      phone_order_status = values(phone_order_status),
      phone_order_id = values(phone_order_id),
      customer_id = values(customer_id),
      product_id = values(product_id),
      is_cheat = values(is_cheat),
      event_source_type = values(event_source_type),
      avid = values(avid),
      from_track_id = values(from_track_id),
      assembly_track_id = values(assembly_track_id),
      is_live_assembly_card = values(is_live_assembly_card),
      report_source = values(report_source),
      sharer_uid = values(sharer_uid),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into
    mgk_form_data
    (account_id,page_id,form_id,creative_id,sales_type,mid,imei,buvid,device_id,os,is_deleted,ctime,mtime,request_id,source_id,track_id,report_status,phone_order_status,phone_order_id,customer_id,product_id,is_cheat,event_source_type,avid,from_track_id,assembly_track_id,is_live_assembly_card,report_source,sharer_uid)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.pageId,jdbcType=BIGINT},
        #{item.formId,jdbcType=BIGINT},
        #{item.creativeId,jdbcType=BIGINT},
        #{item.salesType,jdbcType=INTEGER},
        #{item.mid,jdbcType=BIGINT},
        #{item.imei,jdbcType=VARCHAR},
        #{item.buvid,jdbcType=VARCHAR},
        #{item.deviceId,jdbcType=VARCHAR},
        #{item.os,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.requestId,jdbcType=VARCHAR},
        #{item.sourceId,jdbcType=INTEGER},
        #{item.trackId,jdbcType=VARCHAR},
        #{item.reportStatus,jdbcType=TINYINT},
        #{item.phoneOrderStatus,jdbcType=TINYINT},
        #{item.phoneOrderId,jdbcType=BIGINT},
        #{item.customerId,jdbcType=INTEGER},
        #{item.productId,jdbcType=INTEGER},
        #{item.isCheat,jdbcType=TINYINT},
        #{item.eventSourceType,jdbcType=TINYINT},
        #{item.avid,jdbcType=BIGINT},
        #{item.fromTrackId,jdbcType=VARCHAR},
        #{item.assemblyTrackId,jdbcType=VARCHAR},
        #{item.isLiveAssemblyCard,jdbcType=TINYINT},
        #{item.reportSource,jdbcType=TINYINT},
        #{item.sharerUid,jdbcType=BIGINT},

      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into
    mgk_form_data
    (account_id,page_id,form_id,creative_id,sales_type,mid,imei,buvid,device_id,os,is_deleted,ctime,mtime,request_id,source_id,track_id,report_status,phone_order_status,phone_order_id,customer_id,product_id,is_cheat,event_source_type,avid,from_track_id,assembly_track_id,is_live_assembly_card,report_source,sharer_uid)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.pageId,jdbcType=BIGINT},
        #{item.formId,jdbcType=BIGINT},
        #{item.creativeId,jdbcType=BIGINT},
        #{item.salesType,jdbcType=INTEGER},
        #{item.mid,jdbcType=BIGINT},
        #{item.imei,jdbcType=VARCHAR},
        #{item.buvid,jdbcType=VARCHAR},
        #{item.deviceId,jdbcType=VARCHAR},
        #{item.os,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.requestId,jdbcType=VARCHAR},
        #{item.sourceId,jdbcType=INTEGER},
        #{item.trackId,jdbcType=VARCHAR},
        #{item.reportStatus,jdbcType=TINYINT},
        #{item.phoneOrderStatus,jdbcType=TINYINT},
        #{item.phoneOrderId,jdbcType=BIGINT},
        #{item.customerId,jdbcType=INTEGER},
        #{item.productId,jdbcType=INTEGER},
        #{item.isCheat,jdbcType=TINYINT},
        #{item.eventSourceType,jdbcType=TINYINT},
        #{item.avid,jdbcType=BIGINT},
        #{item.fromTrackId,jdbcType=VARCHAR},
        #{item.assemblyTrackId,jdbcType=VARCHAR},
        #{item.isLiveAssemblyCard,jdbcType=TINYINT},
        #{item.reportSource,jdbcType=TINYINT},
        #{item.sharerUid,jdbcType=BIGINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      page_id = values(page_id),
      form_id = values(form_id),
      creative_id = values(creative_id),
      sales_type = values(sales_type),
      mid = values(mid),
      imei = values(imei),
      buvid = values(buvid),
      device_id = values(device_id),
      os = values(os),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      request_id = values(request_id),
      source_id = values(source_id),
      track_id = values(track_id),
      report_status = values(report_status),
      phone_order_status = values(phone_order_status),
      phone_order_id = values(phone_order_id),
      customer_id = values(customer_id),
      product_id = values(product_id),
      is_cheat = values(is_cheat),
      event_source_type = values(event_source_type),
      avid = values(avid),
      from_track_id = values(from_track_id),
      assembly_track_id = values(assembly_track_id),
      is_live_assembly_card = values(is_live_assembly_card),
      report_source = values(report_source),
      sharer_uid = values(sharer_uid),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormDataPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_form_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="pageId != null">
        page_id,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="salesType != null">
        sales_type,
      </if>
      <if test="mid != null">
        mid,
      </if>
      <if test="imei != null">
        imei,
      </if>
      <if test="buvid != null">
        buvid,
      </if>
      <if test="deviceId != null">
        device_id,
      </if>
      <if test="os != null">
        os,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="requestId != null">
        request_id,
      </if>
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="trackId != null">
        track_id,
      </if>
      <if test="reportStatus != null">
        report_status,
      </if>
      <if test="phoneOrderStatus != null">
        phone_order_status,
      </if>
      <if test="phoneOrderId != null">
        phone_order_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="isCheat != null">
        is_cheat,
      </if>
      <if test="eventSourceType != null">
        event_source_type,
      </if>
      <if test="avid != null">
        avid,
      </if>
      <if test="fromTrackId != null">
        from_track_id,
      </if>
      <if test="assemblyTrackId != null">
        assembly_track_id,
      </if>
      <if test="isLiveAssemblyCard != null">
        is_live_assembly_card,
      </if>
      <if test="reportSource != null">
        report_source,
      </if>
      <if test="sharerUid != null">
        sharer_uid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="pageId != null">
        #{pageId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=BIGINT},
      </if>
      <if test="salesType != null">
        #{salesType,jdbcType=INTEGER},
      </if>
      <if test="mid != null">
        #{mid,jdbcType=BIGINT},
      </if>
      <if test="imei != null">
        #{imei,jdbcType=VARCHAR},
      </if>
      <if test="buvid != null">
        #{buvid,jdbcType=VARCHAR},
      </if>
      <if test="deviceId != null">
        #{deviceId,jdbcType=VARCHAR},
      </if>
      <if test="os != null">
        #{os,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="requestId != null">
        #{requestId,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=INTEGER},
      </if>
      <if test="trackId != null">
        #{trackId,jdbcType=VARCHAR},
      </if>
      <if test="reportStatus != null">
        #{reportStatus,jdbcType=TINYINT},
      </if>
      <if test="phoneOrderStatus != null">
        #{phoneOrderStatus,jdbcType=TINYINT},
      </if>
      <if test="phoneOrderId != null">
        #{phoneOrderId,jdbcType=BIGINT},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="isCheat != null">
        #{isCheat,jdbcType=TINYINT},
      </if>
      <if test="eventSourceType != null">
        #{eventSourceType,jdbcType=TINYINT},
      </if>
      <if test="avid != null">
        #{avid,jdbcType=BIGINT},
      </if>
      <if test="fromTrackId != null">
        #{fromTrackId,jdbcType=VARCHAR},
      </if>
      <if test="assemblyTrackId != null">
        #{assemblyTrackId,jdbcType=VARCHAR},
      </if>
      <if test="isLiveAssemblyCard != null">
        #{isLiveAssemblyCard,jdbcType=TINYINT},
      </if>
      <if test="reportSource != null">
        #{reportSource,jdbcType=TINYINT},
      </if>
      <if test="sharerUid != null">
        #{sharerUid,jdbcType=BIGINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="pageId != null">
        page_id = values(page_id),
      </if>
      <if test="formId != null">
        form_id = values(form_id),
      </if>
      <if test="creativeId != null">
        creative_id = values(creative_id),
      </if>
      <if test="salesType != null">
        sales_type = values(sales_type),
      </if>
      <if test="mid != null">
        mid = values(mid),
      </if>
      <if test="imei != null">
        imei = values(imei),
      </if>
      <if test="buvid != null">
        buvid = values(buvid),
      </if>
      <if test="deviceId != null">
        device_id = values(device_id),
      </if>
      <if test="os != null">
        os = values(os),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="requestId != null">
        request_id = values(request_id),
      </if>
      <if test="sourceId != null">
        source_id = values(source_id),
      </if>
      <if test="trackId != null">
        track_id = values(track_id),
      </if>
      <if test="reportStatus != null">
        report_status = values(report_status),
      </if>
      <if test="phoneOrderStatus != null">
        phone_order_status = values(phone_order_status),
      </if>
      <if test="phoneOrderId != null">
        phone_order_id = values(phone_order_id),
      </if>
      <if test="customerId != null">
        customer_id = values(customer_id),
      </if>
      <if test="productId != null">
        product_id = values(product_id),
      </if>
      <if test="isCheat != null">
        is_cheat = values(is_cheat),
      </if>
      <if test="eventSourceType != null">
        event_source_type = values(event_source_type),
      </if>
      <if test="avid != null">
        avid = values(avid),
      </if>
      <if test="fromTrackId != null">
        from_track_id = values(from_track_id),
      </if>
      <if test="assemblyTrackId != null">
        assembly_track_id = values(assembly_track_id),
      </if>
      <if test="isLiveAssemblyCard != null">
        is_live_assembly_card = values(is_live_assembly_card),
      </if>
      <if test="reportSource != null">
        report_source = values(report_source),
      </if>
      <if test="sharerUid != null">
        sharer_uid = values(sharer_uid),
      </if>
    </trim>
  </insert>
</mapper>