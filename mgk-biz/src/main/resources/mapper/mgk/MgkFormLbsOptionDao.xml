<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.dao.MgkFormLbsOptionDao">
  <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.po.MgkFormLbsOptionPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="form_id" jdbcType="BIGINT" property="formId" />
    <result column="form_item_id" jdbcType="BIGINT" property="formItemId" />
    <result column="option_code" jdbcType="BIGINT" property="optionCode" />
    <result column="option_type" jdbcType="VARCHAR" property="optionType" />
    <result column="option_content" jdbcType="VARCHAR" property="optionContent" />
    <result column="parent_option_code" jdbcType="BIGINT" property="parentOptionCode" />
    <result column="level" jdbcType="TINYINT" property="level" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, form_id, form_item_id, option_code, option_type, option_content, parent_option_code, 
    level, status, is_deleted, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormLbsOptionPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mgk_form_lbs_option
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mgk_form_lbs_option
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mgk_form_lbs_option
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormLbsOptionPoExample">
    delete from mgk_form_lbs_option
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormLbsOptionPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_form_lbs_option (form_id, form_item_id, option_code, 
      option_type, option_content, parent_option_code, 
      level, status, is_deleted, 
      ctime, mtime)
    values (#{formId,jdbcType=BIGINT}, #{formItemId,jdbcType=BIGINT}, #{optionCode,jdbcType=BIGINT}, 
      #{optionType,jdbcType=VARCHAR}, #{optionContent,jdbcType=VARCHAR}, #{parentOptionCode,jdbcType=BIGINT}, 
      #{level,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormLbsOptionPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_form_lbs_option
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="formId != null">
        form_id,
      </if>
      <if test="formItemId != null">
        form_item_id,
      </if>
      <if test="optionCode != null">
        option_code,
      </if>
      <if test="optionType != null">
        option_type,
      </if>
      <if test="optionContent != null">
        option_content,
      </if>
      <if test="parentOptionCode != null">
        parent_option_code,
      </if>
      <if test="level != null">
        level,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="formId != null">
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="formItemId != null">
        #{formItemId,jdbcType=BIGINT},
      </if>
      <if test="optionCode != null">
        #{optionCode,jdbcType=BIGINT},
      </if>
      <if test="optionType != null">
        #{optionType,jdbcType=VARCHAR},
      </if>
      <if test="optionContent != null">
        #{optionContent,jdbcType=VARCHAR},
      </if>
      <if test="parentOptionCode != null">
        #{parentOptionCode,jdbcType=BIGINT},
      </if>
      <if test="level != null">
        #{level,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormLbsOptionPoExample" resultType="java.lang.Long">
    select count(*) from mgk_form_lbs_option
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mgk_form_lbs_option
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=BIGINT},
      </if>
      <if test="record.formItemId != null">
        form_item_id = #{record.formItemId,jdbcType=BIGINT},
      </if>
      <if test="record.optionCode != null">
        option_code = #{record.optionCode,jdbcType=BIGINT},
      </if>
      <if test="record.optionType != null">
        option_type = #{record.optionType,jdbcType=VARCHAR},
      </if>
      <if test="record.optionContent != null">
        option_content = #{record.optionContent,jdbcType=VARCHAR},
      </if>
      <if test="record.parentOptionCode != null">
        parent_option_code = #{record.parentOptionCode,jdbcType=BIGINT},
      </if>
      <if test="record.level != null">
        level = #{record.level,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mgk_form_lbs_option
    set id = #{record.id,jdbcType=BIGINT},
      form_id = #{record.formId,jdbcType=BIGINT},
      form_item_id = #{record.formItemId,jdbcType=BIGINT},
      option_code = #{record.optionCode,jdbcType=BIGINT},
      option_type = #{record.optionType,jdbcType=VARCHAR},
      option_content = #{record.optionContent,jdbcType=VARCHAR},
      parent_option_code = #{record.parentOptionCode,jdbcType=BIGINT},
      level = #{record.level,jdbcType=TINYINT},
      status = #{record.status,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormLbsOptionPo">
    update mgk_form_lbs_option
    <set>
      <if test="formId != null">
        form_id = #{formId,jdbcType=BIGINT},
      </if>
      <if test="formItemId != null">
        form_item_id = #{formItemId,jdbcType=BIGINT},
      </if>
      <if test="optionCode != null">
        option_code = #{optionCode,jdbcType=BIGINT},
      </if>
      <if test="optionType != null">
        option_type = #{optionType,jdbcType=VARCHAR},
      </if>
      <if test="optionContent != null">
        option_content = #{optionContent,jdbcType=VARCHAR},
      </if>
      <if test="parentOptionCode != null">
        parent_option_code = #{parentOptionCode,jdbcType=BIGINT},
      </if>
      <if test="level != null">
        level = #{level,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormLbsOptionPo">
    update mgk_form_lbs_option
    set form_id = #{formId,jdbcType=BIGINT},
      form_item_id = #{formItemId,jdbcType=BIGINT},
      option_code = #{optionCode,jdbcType=BIGINT},
      option_type = #{optionType,jdbcType=VARCHAR},
      option_content = #{optionContent,jdbcType=VARCHAR},
      parent_option_code = #{parentOptionCode,jdbcType=BIGINT},
      level = #{level,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormLbsOptionPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_form_lbs_option (form_id, form_item_id, option_code, 
      option_type, option_content, parent_option_code, 
      level, status, is_deleted, 
      ctime, mtime)
    values (#{formId,jdbcType=BIGINT}, #{formItemId,jdbcType=BIGINT}, #{optionCode,jdbcType=BIGINT}, 
      #{optionType,jdbcType=VARCHAR}, #{optionContent,jdbcType=VARCHAR}, #{parentOptionCode,jdbcType=BIGINT}, 
      #{level,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      form_id = values(form_id),
      form_item_id = values(form_item_id),
      option_code = values(option_code),
      option_type = values(option_type),
      option_content = values(option_content),
      parent_option_code = values(parent_option_code),
      level = values(level),
      status = values(status),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mgk_form_lbs_option
      (form_id,form_item_id,option_code,option_type,option_content,parent_option_code,level,status,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.formId,jdbcType=BIGINT},
        #{item.formItemId,jdbcType=BIGINT},
        #{item.optionCode,jdbcType=BIGINT},
        #{item.optionType,jdbcType=VARCHAR},
        #{item.optionContent,jdbcType=VARCHAR},
        #{item.parentOptionCode,jdbcType=BIGINT},
        #{item.level,jdbcType=TINYINT},
        #{item.status,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mgk_form_lbs_option
      (form_id,form_item_id,option_code,option_type,option_content,parent_option_code,level,status,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.formId,jdbcType=BIGINT},
        #{item.formItemId,jdbcType=BIGINT},
        #{item.optionCode,jdbcType=BIGINT},
        #{item.optionType,jdbcType=VARCHAR},
        #{item.optionContent,jdbcType=VARCHAR},
        #{item.parentOptionCode,jdbcType=BIGINT},
        #{item.level,jdbcType=TINYINT},
        #{item.status,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      form_id = values(form_id),
      form_item_id = values(form_item_id),
      option_code = values(option_code),
      option_type = values(option_type),
      option_content = values(option_content),
      parent_option_code = values(parent_option_code),
      level = values(level),
      status = values(status),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormLbsOptionPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_form_lbs_option
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="formId != null">
        form_id,
      </if>
      <if test="formItemId != null">
        form_item_id,
      </if>
      <if test="optionCode != null">
        option_code,
      </if>
      <if test="optionType != null">
        option_type,
      </if>
      <if test="optionContent != null">
        option_content,
      </if>
      <if test="parentOptionCode != null">
        parent_option_code,
      </if>
      <if test="level != null">
        level,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="formId != null">
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="formItemId != null">
        #{formItemId,jdbcType=BIGINT},
      </if>
      <if test="optionCode != null">
        #{optionCode,jdbcType=BIGINT},
      </if>
      <if test="optionType != null">
        #{optionType,jdbcType=VARCHAR},
      </if>
      <if test="optionContent != null">
        #{optionContent,jdbcType=VARCHAR},
      </if>
      <if test="parentOptionCode != null">
        #{parentOptionCode,jdbcType=BIGINT},
      </if>
      <if test="level != null">
        #{level,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="formId != null">
        form_id = values(form_id),
      </if>
      <if test="formItemId != null">
        form_item_id = values(form_item_id),
      </if>
      <if test="optionCode != null">
        option_code = values(option_code),
      </if>
      <if test="optionType != null">
        option_type = values(option_type),
      </if>
      <if test="optionContent != null">
        option_content = values(option_content),
      </if>
      <if test="parentOptionCode != null">
        parent_option_code = values(parent_option_code),
      </if>
      <if test="level != null">
        level = values(level),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>