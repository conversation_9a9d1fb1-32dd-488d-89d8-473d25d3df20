<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd">

	<description>Spring http invoker</description>

	<bean id="mgkHttpInvokerRequestExecutor"
		class="org.springframework.remoting.httpinvoker.HttpComponentsHttpInvokerRequestExecutor">
		<property name="connectTimeout" value="3000" />
		<property name="readTimeout" value="60000" />
	</bean>

	
	<!-- ======================================CRM============================ -->
    <bean id="crmQueryAccountService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="${crm.service.url}/queryAccountService"/>
        <property name="serviceInterface"
                  value="com.bilibili.crm.platform.soa.ISoaQueryAccountService"/>
        <property name="httpInvokerRequestExecutor" ref="mgkHttpInvokerRequestExecutor"/>
    </bean>

	<bean id="crmAccountLabelService"
		  class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${crm.service.url}/soaAccountLabelService"/>
		<property name="serviceInterface"
				  value="com.bilibili.crm.platform.soa.ISoaAccountLabelService"/>
		<property name="httpInvokerRequestExecutor" ref="mgkHttpInvokerRequestExecutor"/>
	</bean>

	<bean id="crmAgentService"
		  class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${crm.service.url}/agentService"/>
		<property name="serviceInterface"
				  value="com.bilibili.crm.platform.soa.ISoaAgentService"/>
		<property name="httpInvokerRequestExecutor" ref="mgkHttpInvokerRequestExecutor"/>
	</bean>

	<bean id="soaCompanyGroupService"
		  class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${crm.service.url}/soaCompanyGroupService"/>
		<property name="serviceInterface"
				  value="com.bilibili.crm.platform.soa.ISoaCompanyGroupService"/>
		<property name="httpInvokerRequestExecutor" ref="mgkHttpInvokerRequestExecutor"/>
	</bean>

	<!-- ======================================CPT============================ -->

	<bean id="soaCptCreativeService"
		  class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${cpt.service.url}/soaCptCreativeService"/>
		<property name="serviceInterface"
				  value="com.bilibili.cpt.platform.soa.ISoaCptCreativeService"/>
		<property name="httpInvokerRequestExecutor" ref="mgkHttpInvokerRequestExecutor"/>
	</bean>

	<!-- ======================================PLARFORM============================ -->

	<bean id="soaCreativeService"
		  class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${cpm.service.url}/cpmCreativeService" />
		<property name="serviceInterface"
				  value="com.bilibili.adp.launch.api.soa.ISoaCreativeService" />
		<property name="httpInvokerRequestExecutor" ref="mgkHttpInvokerRequestExecutor" />
	</bean>

	<bean id="soaUnitService"
		class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${cpm.service.url}/cpmUnitService" />
		<property name="serviceInterface"
				  value="com.bilibili.adp.launch.api.soa.ISoaUnitService" />
		<property name="httpInvokerRequestExecutor" ref="mgkHttpInvokerRequestExecutor" />
	</bean>

	<bean id="soaLauBizCreativeMappingService"
		  class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${cpm.service.url}/soaLauBizCreativeMappingService" />
		<property name="serviceInterface"
				  value="com.bilibili.adp.launch.api.soa.ISoaLauBizCreativeMappingService" />
		<property name="httpInvokerRequestExecutor" ref="mgkHttpInvokerRequestExecutor" />
	</bean>

	<bean id="soaLauCreativeDynamicService"
		  class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${cpm.service.url}/soaLauCreativeDynamicService" />
		<property name="serviceInterface"
				  value="com.bilibili.adp.launch.api.soa.ISoaLauCreativeDynamicService" />
		<property name="httpInvokerRequestExecutor" ref="mgkHttpInvokerRequestExecutor" />
	</bean>

	<bean id="soaCampaignService"
		  class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${cpm.service.url}/cpmCampaignService" />
		<property name="serviceInterface"
				  value="com.bilibili.adp.launch.api.soa.ISoaCampaignService" />
		<property name="httpInvokerRequestExecutor" ref="mgkHttpInvokerRequestExecutor" />
	</bean>

	<bean id="soaAwakenAppWhitelistService"
		  class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${cpm.service.url}/soaAwakenAppWhitelistService" />
		<property name="serviceInterface"
				  value="com.bilibili.adp.resource.api.soa.ISoaAwakenAppWhitelistService" />
		<property name="httpInvokerRequestExecutor" ref="mgkHttpInvokerRequestExecutor" />
	</bean>

	<bean id="soaAppPackageService"
		  class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${cpm.service.url}/soaAppPackageService" />
		<property name="serviceInterface"
				  value="com.bilibili.adp.resource.api.soa.ISoaAppPackageService" />
		<property name="httpInvokerRequestExecutor" ref="mgkHttpInvokerRequestExecutor" />
	</bean>

	<bean id="soaAdpCpcAccountService"
		  class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${cpm.service.url}/soaAdpCpcAccountService" />
		<property name="serviceInterface"
				  value="com.bilibili.adp.launch.api.soa.ISoaAdpCpcAccountService" />
		<property name="httpInvokerRequestExecutor" ref="mgkHttpInvokerRequestExecutor" />
	</bean>

	<!--todo -->
	<bean id="soaGeneralVideoService"
		  class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${cpm.service.url}/soaGeneralVideoService" />
		<property name="serviceInterface"
				  value="com.bilibili.adp.launch.api.soa.ISoaGeneralVideoService" />
		<property name="httpInvokerRequestExecutor" ref="mgkHttpInvokerRequestExecutor" />
	</bean>

	<bean id="soaLauMiniGameService"
		class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${cpm.service.url}/soaLauMiniGameService" />
		<property name="serviceInterface"
			value="com.bilibili.adp.launch.api.soa.ISoaLauMiniGameService" />
		<property name="httpInvokerRequestExecutor" ref="mgkHttpInvokerRequestExecutor" />
	</bean>

	<bean id="soaArchiveEscapeService"
		  class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${cpm.service.url}/soaArchiveEscapeService" />
		<property name="serviceInterface"
				  value="com.bilibili.adp.launch.api.soa.ISoaArchiveEscapeService" />
		<property name="httpInvokerRequestExecutor" ref="mgkHttpInvokerRequestExecutor" />
	</bean>

	<!-- ======================================SSA============================ -->
	<bean id="soaUposVideoService"
		  class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${ssa.service.url}/soaUposVideoService" />
		<property name="serviceInterface"
				  value="com.bilibili.ssa.platform.soa.ISoaUposVideoService" />
		<property name="httpInvokerRequestExecutor" ref="mgkHttpInvokerRequestExecutor" />
	</bean>

	<bean id="soaAdAuth4AdpService"
		  class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${commercial-order.service.url}/soaAdAuth4AdpService" />
		<property name="serviceInterface"
				  value="com.bilibili.commercialorder.soa.adauth.service.ISoaAdAuth4AdpService" />
		<property name="httpInvokerRequestExecutor" ref="mgkHttpInvokerRequestExecutor" />
	</bean>

	<bean id="soaPickupBidSessionService"
		  class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${commercial-order.service.url}/soaPickupBidSessionService" />
		<property name="serviceInterface"
				  value="com.bilibili.commercialorder.soa.bid.ISoaPickupBidSessionService" />
		<property name="httpInvokerRequestExecutor" ref="mgkHttpInvokerRequestExecutor" />
	</bean>

	<bean id="soaPickupSessionInfoService"
		  class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${commercial-order.service.url}/soaPickupSessionInfoService" />
		<property name="serviceInterface"
				  value="com.bilibili.commercialorder.soa.session.service.ISoaPickupSessionInfoService" />
		<property name="httpInvokerRequestExecutor" ref="mgkHttpInvokerRequestExecutor" />
	</bean>

	<bean id="soaPickupSessionService"
		  class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${commercial-order.service.url}/soaSessionService" />
		<property name="serviceInterface"
				  value="com.bilibili.commercialorder.soa.session.service.ISoaSessionService" />
		<property name="httpInvokerRequestExecutor" ref="mgkHttpInvokerRequestExecutor" />
	</bean>

	<!-- ======================================SSA============================ -->
	<bean id="soaBizAccountService"
		class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${ad-account.service.url}/soaBizAccountService" />
		<property name="serviceInterface"
				  value="com.bilibili.sycpb.acc.api.service.soa.ISoaBizAccountService" />
		<property name="httpInvokerRequestExecutor" ref="mgkHttpInvokerRequestExecutor" />
	</bean>

	<!-- ======================================MAS============================ -->
	<bean id="soaMasTaskService"
		  class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${mas.service.url}/masTaskService" />
		<property name="serviceInterface"
				  value="com.bilibili.mas.api.soa.IMasSoaTaskService" />
		<property name="httpInvokerRequestExecutor" ref="mgkHttpInvokerRequestExecutor" />
	</bean>

	<bean id="soaMasCreativeService"
		  class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${mas.service.url}/masCreativeService" />
		<property name="serviceInterface"
				  value="com.bilibili.mas.api.soa.ISoaMasCreativeService" />
		<property name="httpInvokerRequestExecutor" ref="mgkHttpInvokerRequestExecutor" />
	</bean>

</beans>