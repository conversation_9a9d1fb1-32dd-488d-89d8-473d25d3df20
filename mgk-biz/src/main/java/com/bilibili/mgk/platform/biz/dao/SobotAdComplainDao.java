package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.SobotAdComplainPo;
import com.bilibili.mgk.platform.biz.po.SobotAdComplainPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface SobotAdComplainDao {
    long countByExample(SobotAdComplainPoExample example);

    int deleteByExample(SobotAdComplainPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(SobotAdComplainPo record);

    int insertBatch(List<SobotAdComplainPo> records);

    int insertUpdateBatch(List<SobotAdComplainPo> records);

    int insert(SobotAdComplainPo record);

    int insertUpdateSelective(SobotAdComplainPo record);

    int insertSelective(SobotAdComplainPo record);

    List<SobotAdComplainPo> selectByExampleWithBLOBs(SobotAdComplainPoExample example);

    List<SobotAdComplainPo> selectByExample(SobotAdComplainPoExample example);

    SobotAdComplainPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") SobotAdComplainPo record, @Param("example") SobotAdComplainPoExample example);

    int updateByExampleWithBLOBs(@Param("record") SobotAdComplainPo record, @Param("example") SobotAdComplainPoExample example);

    int updateByExample(@Param("record") SobotAdComplainPo record, @Param("example") SobotAdComplainPoExample example);

    int updateByPrimaryKeySelective(SobotAdComplainPo record);

    int updateByPrimaryKeyWithBLOBs(SobotAdComplainPo record);

    int updateByPrimaryKey(SobotAdComplainPo record);
}