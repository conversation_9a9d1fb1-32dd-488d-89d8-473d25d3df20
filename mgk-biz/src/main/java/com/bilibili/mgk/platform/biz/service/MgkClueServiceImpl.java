package com.bilibili.mgk.platform.biz.service;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.launch.api.campaign.dto.LauCampaignDto;
import com.bilibili.adp.launch.api.creative.dto.CpcCreativeDto;
import com.bilibili.adp.launch.api.creative.dto.QueryCpcCreativeDto;
import com.bilibili.adp.launch.api.soa.ISoaCampaignService;
import com.bilibili.adp.launch.api.soa.ISoaCreativeService;
import com.bilibili.adp.launch.api.soa.ISoaUnitService;
import com.bilibili.adp.launch.api.unit.dto.LauUnitBaseDto;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.bjcom.querydsl.clause.BaseQuery;
import com.bilibili.lancer2.sdk.Lancer2Client;
import com.bilibili.mas.api.creative.dto.MasCreativeBasicDto;
import com.bilibili.mas.api.soa.IMasSoaTaskService;
import com.bilibili.mas.api.soa.ISoaMasCreativeService;
import com.bilibili.mas.api.task.dto.MasTaskDto;
import com.bilibili.mgk.platform.api.common.ClueState;
import com.bilibili.mgk.platform.api.common.PageBo;
import com.bilibili.mgk.platform.api.common.SourceState;
import com.bilibili.mgk.platform.api.form.dto.GetMgkClueRequestBo;
import com.bilibili.mgk.platform.api.form.dto.GetMgkClueResponseBo;
import com.bilibili.mgk.platform.api.form.dto.MgkClueDataEntryBo;
import com.bilibili.mgk.platform.api.form.dto.PostMgkClueRequestBo;
import com.bilibili.mgk.platform.api.form.service.IMgkClueService;
import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageService;
import com.bilibili.mgk.platform.biz.ad.dao.AccAccountDao;
import com.bilibili.mgk.platform.biz.ad.po.AccAccountPo;
import com.bilibili.mgk.platform.biz.ad.po.AccAccountPoExample;
import com.bilibili.mgk.platform.biz.dao.MgkFormDao;
import com.bilibili.mgk.platform.biz.dao.MgkFormDataDao;
import com.bilibili.mgk.platform.biz.dao.MgkLandingPageDao;
import com.bilibili.mgk.platform.biz.dao.ext.ExtMgkFormDataExtraDao;
import com.bilibili.mgk.platform.biz.dao.ext.ExtMgkFormItemDao;
import com.bilibili.mgk.platform.biz.dao.querydsl.pos.MgkFormDataQueryDSLPo;
import com.bilibili.mgk.platform.biz.po.*;
import com.bilibili.mgk.platform.common.*;
import com.google.common.base.Strings;
import io.vavr.Tuple;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.mgk.platform.biz.dao.querydsl.QMgkClue.mgkClue;
import static com.bilibili.mgk.platform.biz.dao.querydsl.QMgkFormData.mgkFormData;

@Slf4j
@Service
public class MgkClueServiceImpl implements IMgkClueService {
    public static final String SEPARATOR = "\u0001";
    @Autowired
    private AccAccountDao accAccountDao;
    @Autowired
    private MgkFormDao mgkFormDao;
    @Autowired
    private MgkLandingPageDao mgkLandingPageDao;
    @Autowired
    private MgkFormDataDao mgkFormDataDao;
    @Autowired
    private ExtMgkFormDataExtraDao extMgkFormDataExtraDao;
    @Autowired
    private ExtMgkFormItemDao extMgkFormItemDao;
    @Autowired
    private ValidClueReportService validClueReportService;
    @Autowired
    private BaseQueryFactory bqf;
    @Autowired
    private ISoaCreativeService creativeService;
    @Autowired
    private ISoaUnitService unitService;
    @Autowired
    private ISoaCampaignService campaignService;
    @Autowired
    private IMgkLandingPageService landingPageService;
    @Autowired
    private IMasSoaTaskService masSoaTaskService;
    @Autowired
    private ISoaMasCreativeService soaMasCreativeService;
    @Value("${mgk.clue.api.log.lancer.log.id:026879}")
    private String logId;
    @Resource(name = "clueApiLancerClient")
    private Lancer2Client clueApiLancerClient;
    @Resource(name = "cacheTaskExecutor")
    private ThreadPoolTaskExecutor cacheTaskExecutor;

    @Override
    @Transactional("mgkPlatformTransactionManager")
    public void postMgkClue(PostMgkClueRequestBo requestBo) {
        final List<MgkCluePo> existingClues = bqf.selectFrom(mgkClue)
                .where(mgkClue.accountId.eq(requestBo.getAccountId()))
                .where(mgkClue.creativeId.eq(requestBo.getCreativeId()))
                .where(mgkClue.trackId.eq(requestBo.getTrackId()))
                .where(mgkClue.isDeleted.eq(IsDeleted.VALID.getCode()))
                .fetch(MgkCluePo.class);
        Assert.isTrue(existingClues.size() <= 1, "相同trackId存在重复的记录");

        // 这个trackId没有上报过
        if (existingClues.size() == 0) {
            final MgkFormDataPoExample condition = new MgkFormDataPoExample();
            condition.createCriteria()
                    .andAccountIdEqualTo(requestBo.getAccountId())
                    .andTrackIdEqualTo(requestBo.getTrackId())
                    .andIsCheatEqualTo(WhetherEnum.NO.getCode());
            final List<MgkFormDataPo> internalList = mgkFormDataDao.selectByExample(condition);

            // 这个trackId在建站页的表里找到了, 说明是建站页的trackId, 否则按照外链处理
            final SourceState sourceState = internalList != null && internalList.size() > 0 ? SourceState.INTERNAL : SourceState.EXTERNAL;
            bqf.insert(mgkClue).insertBean(MgkCluePo.builder()
                    .trackId(requestBo.getTrackId())
                    .accountId(requestBo.getAccountId())
                    .creativeId(requestBo.getCreativeId())
                    .clueState(requestBo.getClueState().getCode())
                    .sourceId(sourceState.getCode())
                    .build());
        } else {
            final MgkCluePo existingClue = existingClues.get(0);
            // 没有实质数据需要更新, 可能是重复请求, 直接丢弃
            if (!Objects.equals(existingClue.getClueState(), requestBo.getClueState().getCode())) {
                return;
            }
            bqf.update(mgkClue)
                    .set(mgkClue.clueState, requestBo.getClueState().getCode())
                    .where(mgkClue.id.eq(existingClue.getId()))
                    .execute();
        }
        validClueReportService.reportAsync(requestBo.getTrackId(), requestBo.getClueState().getName(), String.format("%d", System.currentTimeMillis()));
    }

    private String getAccountUsername(Integer accountId) {
        //查询账号信息
        AccAccountPoExample accountExample = new AccAccountPoExample();
        accountExample.createCriteria().andAccountIdIn(Collections.singletonList(accountId));
        List<AccAccountPo> accounts = accAccountDao.selectByExample(accountExample);
        Assert.notEmpty(accounts, "无账号信息！");
        return accounts.get(0).getUsername();
    }

    private MgkFormDataPoExample getMgkDataPoExample(GetMgkClueRequestBo reqBo) {
        final MgkFormDataPoExample mgkFormDataCondition = new MgkFormDataPoExample();
        mgkFormDataCondition.createCriteria()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andTrackIdNotEqualTo("")
                .andAccountIdEqualTo(reqBo.getAdvertiserId())
                .andIsCheatEqualTo(WhetherEnum.NO.getCode())
                .andCtimeBetween(reqBo.getStartTs(), reqBo.getEndTs());
        return mgkFormDataCondition;
    }

    private BaseQuery<MgkFormDataQueryDSLPo> mgkFormDataQueryDSLPoBaseQuery(GetMgkClueRequestBo reqBo) {
        return bqf.from(mgkFormData)
                .where(mgkFormData.isDeleted.eq(IsDeleted.VALID.getCode()))
                .where(mgkFormData.accountId.eq(reqBo.getAdvertiserId()))
                .where(mgkFormData.isCheat.eq(WhetherEnum.NO.getCode()))
                .where(mgkFormData.ctime.between(reqBo.getStartTs(), reqBo.getEndTs())
//                        .and(mgkFormData.trackId.notEqualsIgnoreCase("").or(mgkFormData.assemblyTrackId.notEqualsIgnoreCase("")))
                )
                .select(mgkFormData);
    }



    public GetMgkClueResponseBo testgetMgkClue(Integer accountId, int page, int size, long start, long end){
        return getMgkClue(GetMgkClueRequestBo.builder().advertiserId(accountId)
                .endTs(new Timestamp(end)).startTs(new Timestamp(start))
                .pageBo(PageBo.builder().pageNo(page).pageSize(size).build()).build());
    }

    @Override
    public GetMgkClueResponseBo getMgkClue(GetMgkClueRequestBo reqBo) {
        String accountUsername = getAccountUsername(reqBo.getAdvertiserId());
        // 暂时只能查建站页的数据
        final GetMgkClueResponseBo.GetMgkClueResponseBoBuilder respBoBuilder = GetMgkClueResponseBo.builder()
                .advertiserId(reqBo.getAdvertiserId())
                .advertiserName(accountUsername)
                .pageInfo(reqBo.getPageBo());
        final long totalCount = mgkFormDataQueryDSLPoBaseQuery(reqBo).fetchCount();
        reqBo.getPageBo().setTotalCount((int) totalCount);
        if (totalCount == 0) {
            return respBoBuilder.list(Collections.emptyList())
                    .build();
        }

        final List<MgkFormDataPo> mgkFormDataPoList = mgkFormDataQueryDSLPoBaseQuery(reqBo).orderBy("ctime")
                .offsetIfNotNull(reqBo.getPageBo().offset())
                .limitIfNotNull(reqBo.getPageBo().getPageSize()).fetch(MgkFormDataPo.class);
        log.info("get mgk form data list :  {}", mgkFormDataPoList.toString());

        Set<Long> formIdSet = new HashSet<>();
        Set<Long> formDataIdSet = new HashSet<>();
        Set<Long> pageIdSet = new HashSet<>();
        Set<Integer> creativeIdSet = new HashSet<>();
        mgkFormDataPoList.forEach(e -> {
            formIdSet.add(e.getFormId());
            formDataIdSet.add(e.getId());
            pageIdSet.add(e.getPageId());
            creativeIdSet.add(e.getCreativeId().intValue());
        });
        List<Long> formIds = new ArrayList<>(formIdSet);
        List<Long> formDataIds = new ArrayList<>(formDataIdSet);
        List<Long> pageIds = new ArrayList<>(pageIdSet);
        List<Integer> creativeIds = new ArrayList<>(creativeIdSet);

        final Map<Long, MgkFormPo> formIdMap;
        final Map<Long, Integer> mgkFormItemItemId2TypeMap;
        if (!CollectionUtils.isEmpty(formIds)) {
            MgkFormPoExample formExample = new MgkFormPoExample();
            formExample.createCriteria().andFormIdIn(formIds);
            formIdMap = mgkFormDao.selectByExample(formExample)
                    .stream()
                    .collect(Collectors.toMap(
                            MgkFormPo::getFormId,
                            Function.identity()));

            mgkFormItemItemId2TypeMap = extMgkFormItemDao.getFormItemByIds(formIds)
                    .stream()
                    .collect(Collectors.toMap(
                            MgkFormItemPo::getFormItemId,
                            MgkFormItemPo::getType));
        } else {
            formIdMap = Collections.emptyMap();
            mgkFormItemItemId2TypeMap = Collections.emptyMap();
        }

        final Map<Long, List<MgkFormDataExtraPo>> mgkFormDataExtraIdMap;
        if (!CollectionUtils.isEmpty(formDataIds)) {
            mgkFormDataExtraIdMap = extMgkFormDataExtraDao.getByFormDataIds(formDataIds)
                    .stream()
                    .collect(Collectors.groupingBy(MgkFormDataExtraPo::getFormDataId));
        } else {
            mgkFormDataExtraIdMap = Collections.emptyMap();
        }
        final Map<Long, MgkLandingPagePo> pageIdMap;
        if (!CollectionUtils.isEmpty(pageIds)) {
            MgkLandingPagePoExample pageExample = new MgkLandingPagePoExample();
            pageExample.createCriteria().andPageIdIn(pageIds);
            pageIdMap = mgkLandingPageDao.selectByExample(pageExample)
                    .stream()
                    .collect(Collectors.toMap(
                            MgkLandingPagePo::getPageId,
                            Function.identity()));
        } else {
            pageIdMap = Collections.emptyMap();
        }

        final List<CpcCreativeDto> creatives;
        final Map<Integer, CpcCreativeDto> creativeIdMap;
        final List<Integer> unitIds;
        final Map<Integer, LauUnitBaseDto> unitIdMap;
        final List<Integer> campaignIds;
        final Map<Integer, LauCampaignDto> campaignIdMap;

        if (!CollectionUtils.isEmpty(creativeIds)) {
            creatives = creativeService
                    .queryCreative(QueryCpcCreativeDto.builder()
                            .creativeIds(creativeIds)
                            .build());
            creativeIdMap = creatives
                    .stream()
                    .collect(Collectors.toMap(
                            CpcCreativeDto::getCreativeId,
                            Function.identity()
                    ));
        } else {
            creatives = Collections.emptyList();
            creativeIdMap = Collections.emptyMap();
        }

        unitIds = creatives
                .stream()
                .map(CpcCreativeDto::getUnitId)
                .distinct()
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(unitIds)) {
            unitIdMap = unitService.getBaseDtoMapInIds(unitIds);
        } else {
            unitIdMap = Collections.emptyMap();
        }

        campaignIds = unitIdMap
                .values()
                .stream()
                .map(LauUnitBaseDto::getCampaignId)
                .distinct()
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(campaignIds)) {
            campaignIdMap = campaignService.getCampaignDtoMapInIds(campaignIds);
        } else {
            campaignIdMap = Collections.emptyMap();
        }

        Map<Long, Long> mgkLandingTemplatePageIdMap = landingPageService.getMgkLandingTemplatePageIdMap(pageIds);
        Map<Long, Long> mgkLandingPageTemplateWithoutArcIdMap = landingPageService.getMgkLandingPageTemplateWithoutArcIdMap(pageIds);
        final List<GetMgkClueResponseBo.MgkClueForm> formList = mgkFormDataPoList
                .stream()
                .collect(Collectors.groupingBy(MgkFormDataPo::getPageId))
                .entrySet()
                .stream()
                .map(kv -> {
                    final Long pageId = kv.getKey();
                    Long templatePageId = mgkLandingTemplatePageIdMap.getOrDefault(pageId, 0L);
                    Long templateWithoutArcId = mgkLandingPageTemplateWithoutArcIdMap.getOrDefault(pageId, 0L);
                    templatePageId = Math.max(templatePageId, templateWithoutArcId);
                    final MgkLandingPagePo page = pageIdMap.getOrDefault(pageId, new MgkLandingPagePo());
                    final List<MgkFormDataPo> poList = kv.getValue();
                    final Long formId = poList.get(0).getFormId();
                    final MgkFormPo form = formIdMap.getOrDefault(formId, new MgkFormPo());
                    final GetMgkClueResponseBo.MgkClueForm.MgkClueFormBuilder formBuilder = GetMgkClueResponseBo.MgkClueForm.builder()
                            .formId(formId)
                            .formName(Optional.ofNullable(form.getName()).orElse(StringUtils.EMPTY))
                            .pageId(Utils.isPositive(templatePageId) ? templatePageId : pageId)
                            .pageName(Optional.ofNullable(page.getName()).orElse(""));
                    //评论区暗投落地页不展示页面信息
                    if(IsModelEnum.COMMENT.getCode().equals(page.getIsModel())){
                        formBuilder.pageId(0L).pageName("--");
                    }
                    final List<GetMgkClueResponseBo.MgkClueData> dataList = poList.stream()
                            .map(x -> {
                                CpcCreativeDto creative = creativeIdMap.getOrDefault(x.getCreativeId().intValue(), new CpcCreativeDto());
                                LauUnitBaseDto unit = unitIdMap.getOrDefault(creative.getUnitId(), new LauUnitBaseDto());
                                LauCampaignDto campaign = campaignIdMap.getOrDefault(unit.getCampaignId(), new LauCampaignDto());
                                GetMgkClueResponseBo.MgkClueData.MgkClueDataBuilder dataBuilder = GetMgkClueResponseBo.MgkClueData.builder()
                                        .campaignId(campaign.getCampaignId())
                                        .campaignName(Optional.ofNullable(campaign.getCampaignName()).orElse(""))
                                        .unitId(unit.getUnitId())
                                        .unitName(Optional.ofNullable(unit.getUnitName()).orElse(""))
                                        .creativeId(creative.getCreativeId())
                                        .creativeName(Optional.ofNullable(creative.getTitle()).orElse(""))
                                        .createTimeDetail(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(x.getCtime()))
                                        .trackId(x.getTrackId())
                                        .eventSource(EventSourceType.mapToEventSourceClue(x.getEventSourceType()))
                                        ;
                                final List<MgkClueDataEntryBo> entryList = mgkFormDataExtraIdMap.getOrDefault(x.getId(), Collections.emptyList())
                                        .stream()
                                        .map(y -> MgkClueDataEntryBo.builder()
                                                .label(y.getLabel())
                                                .value(y.getValue())
                                                .type(Try.of(() -> {
                                                    Integer typeId = mgkFormItemItemId2TypeMap.get(y.getFormItemId());
                                                    return FormItemTypeEnum.getById(typeId).getCode();
                                                }).recover(t -> null).get())
                                                .build())
                                        .collect(Collectors.toList());
                                return dataBuilder.list(entryList).build();
                            })
                            .collect(Collectors.toList());
                    return formBuilder.list(dataList).build();
                }).collect(Collectors.toList());
        return respBoBuilder.list(formList)
                .build();
    }

    @Override
    public GetMgkClueResponseBo getMgkClueFromCluePass(GetMgkClueRequestBo reqBo) {
        String accountUsername = getAccountUsername(reqBo.getAdvertiserId());
        // 暂时只能查建站页的数据
        final GetMgkClueResponseBo.GetMgkClueResponseBoBuilder respBoBuilder = GetMgkClueResponseBo.builder()
                .advertiserId(reqBo.getAdvertiserId())
                .advertiserName(accountUsername)
                .pageInfo(reqBo.getPageBo());

        final MgkFormDataPoExample mgkFormDataCondition = getMgkDataPoExample(reqBo);

        final long totalCount = mgkFormDataDao.countByExample(mgkFormDataCondition);
        reqBo.getPageBo().setTotalCount((int) totalCount);
        if (totalCount == 0) {
            return respBoBuilder.list(Collections.emptyList())
                    .build();
        }

        mgkFormDataCondition.setOrderByClause("ctime");
        mgkFormDataCondition.setLimit(reqBo.getPageBo().getPageSize());
        mgkFormDataCondition.setOffset(reqBo.getPageBo().offset());
        final List<MgkFormDataPo> mgkFormDataPoList = mgkFormDataDao.selectByExample(mgkFormDataCondition);
        log.info("get mgk form data list :  {}", mgkFormDataPoList.toString());

        Set<Long> formIdSet = new HashSet<>();
        Set<Long> formDataIdSet = new HashSet<>();
        Set<Long> pageIdSet = new HashSet<>();
        Set<Integer> creativeIdSet = new HashSet<>();
        mgkFormDataPoList.forEach(e -> {
            formIdSet.add(e.getFormId());
            formDataIdSet.add(e.getId());
            pageIdSet.add(e.getPageId());
            creativeIdSet.add(e.getCreativeId().intValue());
        });
        List<Long> formIds = new ArrayList<>(formIdSet);
        List<Long> formDataIds = new ArrayList<>(formDataIdSet);
        List<Long> pageIds = new ArrayList<>(pageIdSet);
        List<Integer> creativeIds = new ArrayList<>(creativeIdSet);

        final Map<Long, MgkFormPo> formIdMap;
        final Map<Long, Integer> mgkFormItemItemId2TypeMap;
        if (!CollectionUtils.isEmpty(formIds)) {
            MgkFormPoExample formExample = new MgkFormPoExample();
            formExample.createCriteria().andFormIdIn(formIds);
            formIdMap = mgkFormDao.selectByExample(formExample)
                    .stream()
                    .collect(Collectors.toMap(
                            MgkFormPo::getFormId,
                            Function.identity()));

            mgkFormItemItemId2TypeMap = extMgkFormItemDao.getFormItemByIds(formIds)
                    .stream()
                    .collect(Collectors.toMap(
                            MgkFormItemPo::getFormItemId,
                            MgkFormItemPo::getType));
        } else {
            formIdMap = Collections.emptyMap();
            mgkFormItemItemId2TypeMap = Collections.emptyMap();
        }

        final Map<Long, List<MgkFormDataExtraPo>> mgkFormDataExtraIdMap;
        if (!CollectionUtils.isEmpty(formDataIds)) {
            mgkFormDataExtraIdMap = extMgkFormDataExtraDao.getByFormDataIds(formDataIds)
                    .stream()
                    .collect(Collectors.groupingBy(MgkFormDataExtraPo::getFormDataId));
        } else {
            mgkFormDataExtraIdMap = Collections.emptyMap();
        }
        final Map<Long, MgkLandingPagePo> pageIdMap;
        if (!CollectionUtils.isEmpty(pageIds)) {
            MgkLandingPagePoExample pageExample = new MgkLandingPagePoExample();
            pageExample.createCriteria().andPageIdIn(pageIds);
            pageIdMap = mgkLandingPageDao.selectByExample(pageExample)
                    .stream()
                    .collect(Collectors.toMap(
                            MgkLandingPagePo::getPageId,
                            Function.identity()));
        } else {
            pageIdMap = Collections.emptyMap();
        }

        final List<MasCreativeBasicDto> masCreativeList;
        final Map<Integer, MasCreativeBasicDto> masCreativeIdMap;
        final List<Integer> taskIds;
        final Map<Integer, MasTaskDto> masTaskIdMap;

        if (!CollectionUtils.isEmpty(creativeIds)) {
            masCreativeIdMap = soaMasCreativeService.getCreativeBasicMapByIds(creativeIds);
            masCreativeList = new ArrayList<>(masCreativeIdMap.values());
        } else {
            masCreativeList = Collections.emptyList();
            masCreativeIdMap = Collections.emptyMap();
        }

        taskIds = masCreativeList
                .stream()
                .map(MasCreativeBasicDto::getTaskId)
                .distinct()
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(taskIds)) {
            masTaskIdMap = masSoaTaskService.getMasTaskMapInIds(taskIds);
        } else {
            masTaskIdMap = Collections.emptyMap();
        }

        final List<GetMgkClueResponseBo.MgkClueForm> formList = mgkFormDataPoList
                .stream()
                .collect(Collectors.groupingBy(MgkFormDataPo::getPageId))
                .entrySet()
                .stream()
                .map(kv -> {
                    final Long pageId = kv.getKey();
                    final MgkLandingPagePo page = pageIdMap.getOrDefault(pageId, new MgkLandingPagePo());
                    final List<MgkFormDataPo> poList = kv.getValue();
                    final Long formId = poList.get(0).getFormId();
                    final MgkFormPo form = formIdMap.getOrDefault(formId, new MgkFormPo());
                    final GetMgkClueResponseBo.MgkClueForm.MgkClueFormBuilder formBuilder = GetMgkClueResponseBo.MgkClueForm.builder()
                            .formId(formId)
                            .formName(Optional.ofNullable(form.getName()).orElse(StringUtils.EMPTY))
                            .pageId(page.getPageId())
                            .pageName(Optional.ofNullable(page.getName()).orElse(""));
                    final List<GetMgkClueResponseBo.MgkClueData> dataList = poList.stream()
                            .map(x -> {
                                MasCreativeBasicDto masCreative = masCreativeIdMap.getOrDefault(x.getCreativeId().intValue(), new MasCreativeBasicDto());
                                MasTaskDto masTask = masTaskIdMap.getOrDefault(masCreative.getTaskId(), new MasTaskDto());
                                GetMgkClueResponseBo.MgkClueData.MgkClueDataBuilder dataBuilder = GetMgkClueResponseBo.MgkClueData.builder()
                                        .campaignId(masTask.getId())
                                        .campaignName(Optional.ofNullable(masTask.getName()).orElse(""))
                                        .unitId(masTask.getId())
                                        .unitName(Optional.ofNullable(masTask.getName()).orElse(""))
                                        .creativeId(masCreative.getId())
                                        .creativeName(Optional.ofNullable(masCreative.getTitle()).orElse(""))
                                        .createTimeDetail(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(x.getCtime()))
                                        .trackId(x.getTrackId());
                                final List<MgkClueDataEntryBo> entryList = mgkFormDataExtraIdMap.getOrDefault(x.getId(), Collections.emptyList())
                                        .stream()
                                        .map(y -> MgkClueDataEntryBo.builder()
                                                .label(y.getLabel())
                                                .value(y.getValue())
                                                .type(Try.of(() -> {
                                                    Integer typeId = mgkFormItemItemId2TypeMap.get(y.getFormItemId());
                                                    return FormItemTypeEnum.getById(typeId).getCode();
                                                }).recover(t -> null).get())
                                                .build())
                                        .collect(Collectors.toList());
                                return dataBuilder.list(entryList).build();
                            })
                            .collect(Collectors.toList());
                    return formBuilder.list(dataList).build();
                }).collect(Collectors.toList());
        return respBoBuilder.list(formList)
                .build();
    }

    @Override
    public void reportClueJob() {
        final LocalDateTime now = LocalDateTime.now();
        final Timestamp startTs = Timestamp.valueOf(now.minusMinutes(50));
        final Timestamp endTs = Timestamp.valueOf(now.minusMinutes(5));
        final List<MgkCluePo> cluePoList = bqf.selectFrom(mgkClue)
                .where(mgkClue.reportStatus.eq(MgkReportStatusEnum.UNREPORT.getCode()))
                .where(mgkClue.mtime.gt(startTs))
                .where(mgkClue.mtime.lt(endTs))
                .fetch(MgkCluePo.class);
        log.info("线索上报任务: [{} - {}]; 总计上报数量: {}", startTs, endTs, cluePoList.size());
        validClueReportService.reportListAsync(cluePoList
                .stream()
                .map(x -> Tuple.of(x.getTrackId(), ClueState.fromCodeOptional(x.getClueState()).orElse(ClueState.UNKNOWN).getName(), Long.valueOf(x.getMtime().getTime()).toString()))
                .collect(Collectors.toList()));
    }

    @Override
    public void reportClues(String startDate, String endDate) {
        Assert.notNull(startDate, "开始时间不能为空");
        if (Strings.isNullOrEmpty(endDate)) {
            endDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        final Timestamp startTs = Timestamp.valueOf(LocalDate.parse(startDate).atStartOfDay());
        final Timestamp endTs = Timestamp.valueOf(LocalDate.parse(endDate).plusDays(1).atStartOfDay());
        final List<MgkCluePo> cluePoList = bqf.selectFrom(mgkClue)
                .where(mgkClue.reportStatus.eq(MgkReportStatusEnum.UNREPORT.getCode()))
                .where(mgkClue.mtime.gt(startTs))
                .where(mgkClue.mtime.lt(endTs))
                .fetch(MgkCluePo.class);
        log.info("线索上报任务: [{} - {}]; 总计上报数量: {}", startTs, endTs, cluePoList.size());
        validClueReportService.reportListAsync(cluePoList
                .stream()
                .map(x -> Tuple.of(x.getTrackId(), ClueState.fromCodeOptional(x.getClueState()).orElse(ClueState.UNKNOWN).getName(), Long.valueOf(x.getMtime().getTime()).toString()))
                .collect(Collectors.toList()));
    }

    @Override
    public void clueAsynchLancerReport(String path, Integer advertiserId, String extra) {
        try {
            cacheTaskExecutor.execute(() -> {
                StringBuilder msgBuilder = new StringBuilder();
                msgBuilder.append(path).append(SEPARATOR)
                        .append(advertiserId).append(SEPARATOR)
                        .append(0).append(SEPARATOR)
                        .append(System.currentTimeMillis()).append(SEPARATOR)
                        .append(extra).append(SEPARATOR)
                        .append("mgk-portal");

                clueApiLancerClient.put(logId, msgBuilder.toString());
            });
        } catch (Exception e) {
            log.error("clueAsynchLancerReport error ", e);
        }
    }

}
