package com.bilibili.mgk.platform.biz.service.account.factory.impl;

import com.bilibili.mgk.platform.biz.ad.po.AccAccountPo;
import com.bilibili.mgk.platform.biz.service.account.factory.AccountLabelFactory;
import com.bilibili.mgk.platform.biz.service.account.factory.bo.AccountLabelInfoBo;
import com.bilibili.mgk.platform.common.MgkAccountLabelEnum;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

@Slf4j
@Service(value = "acc2CategoryLabelFactory")
public class Acc2CategoryLabelFactory implements AccountLabelFactory {

    @Resource
    private RedisTemplate<String, String> stringRedisTemplate;

    @Override
    public void refreshLabel2redis(AccountLabelInfoBo labelInfoBo) {
        MgkAccountLabelEnum labelEnum = labelInfoBo.getLabelEnum();
        if(CollectionUtils.isEmpty(labelInfoBo.getAccountIds())){
            return;
        }
        labelInfoBo.getAccountIds().forEach(accountId->{
            try {
                AccAccountPo accAccountPo = labelInfoBo.getAccountId2PoMap().getOrDefault(accountId,
                        AccAccountPo.builder().commerceCategoryFirstId(0).build());
                String accountId2IndustryIdRedisKey = labelEnum.getPrefixInRedis() + accountId;
                Integer cmCategoryId =accAccountPo.getCommerceCategoryFirstId();
                stringRedisTemplate.opsForValue().set(accountId2IndustryIdRedisKey, cmCategoryId.toString());
            } catch (Exception e) {
                log.error("acc2ProductLabelFactory refreshLabel2redis error" + Throwables.getStackTraceAsString(e));
            }
        });
    }
}
