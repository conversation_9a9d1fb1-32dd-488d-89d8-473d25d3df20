package com.bilibili.mgk.platform.biz.service.geelyExcelImport;

import com.bilibili.mgk.platform.biz.service.MgkGeelyCustomizeServiceDelegate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/6/5
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OptionDto {
    private Integer id;
    private String name;
    private String title;
    private String content;
    /**
     * key 为 name
     */
    private List<Map<String, String>> fields;
    private List<OptionNode> children;
}
