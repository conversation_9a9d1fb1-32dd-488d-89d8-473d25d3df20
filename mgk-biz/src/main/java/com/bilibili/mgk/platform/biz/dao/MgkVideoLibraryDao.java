package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkVideoLibraryPo;
import com.bilibili.mgk.platform.biz.po.MgkVideoLibraryPoExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MgkVideoLibraryDao {
    long countByExample(MgkVideoLibraryPoExample example);

    int deleteByExample(MgkVideoLibraryPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(MgkVideoLibraryPo record);

    int insertBatch(List<MgkVideoLibraryPo> records);

    int insertUpdateBatch(List<MgkVideoLibraryPo> records);

    int insert(MgkVideoLibraryPo record);

    int insertUpdateSelective(MgkVideoLibraryPo record);

    int insertSelective(MgkVideoLibraryPo record);

    List<MgkVideoLibraryPo> selectByExample(MgkVideoLibraryPoExample example);

    MgkVideoLibraryPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") MgkVideoLibraryPo record, @Param("example") MgkVideoLibraryPoExample example);

    int updateByExample(@Param("record") MgkVideoLibraryPo record, @Param("example") MgkVideoLibraryPoExample example);

    int updateByPrimaryKeySelective(MgkVideoLibraryPo record);

    int updateByPrimaryKey(MgkVideoLibraryPo record);
}