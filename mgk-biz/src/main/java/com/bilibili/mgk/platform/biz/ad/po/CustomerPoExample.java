package com.bilibili.mgk.platform.biz.ad.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class CustomerPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public CustomerPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andUsernameIsNull() {
            addCriterion("username is null");
            return (Criteria) this;
        }

        public Criteria andUsernameIsNotNull() {
            addCriterion("username is not null");
            return (Criteria) this;
        }

        public Criteria andUsernameEqualTo(String value) {
            addCriterion("username =", value, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameNotEqualTo(String value) {
            addCriterion("username <>", value, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameGreaterThan(String value) {
            addCriterion("username >", value, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("username >=", value, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameLessThan(String value) {
            addCriterion("username <", value, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameLessThanOrEqualTo(String value) {
            addCriterion("username <=", value, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameLike(String value) {
            addCriterion("username like", value, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameNotLike(String value) {
            addCriterion("username not like", value, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameIn(List<String> values) {
            addCriterion("username in", values, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameNotIn(List<String> values) {
            addCriterion("username not in", values, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameBetween(String value1, String value2) {
            addCriterion("username between", value1, value2, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameNotBetween(String value1, String value2) {
            addCriterion("username not between", value1, value2, "username");
            return (Criteria) this;
        }

        public Criteria andCustomerCategoryIsNull() {
            addCriterion("customer_category is null");
            return (Criteria) this;
        }

        public Criteria andCustomerCategoryIsNotNull() {
            addCriterion("customer_category is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerCategoryEqualTo(Integer value) {
            addCriterion("customer_category =", value, "customerCategory");
            return (Criteria) this;
        }

        public Criteria andCustomerCategoryNotEqualTo(Integer value) {
            addCriterion("customer_category <>", value, "customerCategory");
            return (Criteria) this;
        }

        public Criteria andCustomerCategoryGreaterThan(Integer value) {
            addCriterion("customer_category >", value, "customerCategory");
            return (Criteria) this;
        }

        public Criteria andCustomerCategoryGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_category >=", value, "customerCategory");
            return (Criteria) this;
        }

        public Criteria andCustomerCategoryLessThan(Integer value) {
            addCriterion("customer_category <", value, "customerCategory");
            return (Criteria) this;
        }

        public Criteria andCustomerCategoryLessThanOrEqualTo(Integer value) {
            addCriterion("customer_category <=", value, "customerCategory");
            return (Criteria) this;
        }

        public Criteria andCustomerCategoryIn(List<Integer> values) {
            addCriterion("customer_category in", values, "customerCategory");
            return (Criteria) this;
        }

        public Criteria andCustomerCategoryNotIn(List<Integer> values) {
            addCriterion("customer_category not in", values, "customerCategory");
            return (Criteria) this;
        }

        public Criteria andCustomerCategoryBetween(Integer value1, Integer value2) {
            addCriterion("customer_category between", value1, value2, "customerCategory");
            return (Criteria) this;
        }

        public Criteria andCustomerCategoryNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_category not between", value1, value2, "customerCategory");
            return (Criteria) this;
        }

        public Criteria andIsInnerIsNull() {
            addCriterion("is_inner is null");
            return (Criteria) this;
        }

        public Criteria andIsInnerIsNotNull() {
            addCriterion("is_inner is not null");
            return (Criteria) this;
        }

        public Criteria andIsInnerEqualTo(Integer value) {
            addCriterion("is_inner =", value, "isInner");
            return (Criteria) this;
        }

        public Criteria andIsInnerNotEqualTo(Integer value) {
            addCriterion("is_inner <>", value, "isInner");
            return (Criteria) this;
        }

        public Criteria andIsInnerGreaterThan(Integer value) {
            addCriterion("is_inner >", value, "isInner");
            return (Criteria) this;
        }

        public Criteria andIsInnerGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_inner >=", value, "isInner");
            return (Criteria) this;
        }

        public Criteria andIsInnerLessThan(Integer value) {
            addCriterion("is_inner <", value, "isInner");
            return (Criteria) this;
        }

        public Criteria andIsInnerLessThanOrEqualTo(Integer value) {
            addCriterion("is_inner <=", value, "isInner");
            return (Criteria) this;
        }

        public Criteria andIsInnerIn(List<Integer> values) {
            addCriterion("is_inner in", values, "isInner");
            return (Criteria) this;
        }

        public Criteria andIsInnerNotIn(List<Integer> values) {
            addCriterion("is_inner not in", values, "isInner");
            return (Criteria) this;
        }

        public Criteria andIsInnerBetween(Integer value1, Integer value2) {
            addCriterion("is_inner between", value1, value2, "isInner");
            return (Criteria) this;
        }

        public Criteria andIsInnerNotBetween(Integer value1, Integer value2) {
            addCriterion("is_inner not between", value1, value2, "isInner");
            return (Criteria) this;
        }

        public Criteria andIsAgentIsNull() {
            addCriterion("is_agent is null");
            return (Criteria) this;
        }

        public Criteria andIsAgentIsNotNull() {
            addCriterion("is_agent is not null");
            return (Criteria) this;
        }

        public Criteria andIsAgentEqualTo(Integer value) {
            addCriterion("is_agent =", value, "isAgent");
            return (Criteria) this;
        }

        public Criteria andIsAgentNotEqualTo(Integer value) {
            addCriterion("is_agent <>", value, "isAgent");
            return (Criteria) this;
        }

        public Criteria andIsAgentGreaterThan(Integer value) {
            addCriterion("is_agent >", value, "isAgent");
            return (Criteria) this;
        }

        public Criteria andIsAgentGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_agent >=", value, "isAgent");
            return (Criteria) this;
        }

        public Criteria andIsAgentLessThan(Integer value) {
            addCriterion("is_agent <", value, "isAgent");
            return (Criteria) this;
        }

        public Criteria andIsAgentLessThanOrEqualTo(Integer value) {
            addCriterion("is_agent <=", value, "isAgent");
            return (Criteria) this;
        }

        public Criteria andIsAgentIn(List<Integer> values) {
            addCriterion("is_agent in", values, "isAgent");
            return (Criteria) this;
        }

        public Criteria andIsAgentNotIn(List<Integer> values) {
            addCriterion("is_agent not in", values, "isAgent");
            return (Criteria) this;
        }

        public Criteria andIsAgentBetween(Integer value1, Integer value2) {
            addCriterion("is_agent between", value1, value2, "isAgent");
            return (Criteria) this;
        }

        public Criteria andIsAgentNotBetween(Integer value1, Integer value2) {
            addCriterion("is_agent not between", value1, value2, "isAgent");
            return (Criteria) this;
        }

        public Criteria andAgentTypeIsNull() {
            addCriterion("agent_type is null");
            return (Criteria) this;
        }

        public Criteria andAgentTypeIsNotNull() {
            addCriterion("agent_type is not null");
            return (Criteria) this;
        }

        public Criteria andAgentTypeEqualTo(Integer value) {
            addCriterion("agent_type =", value, "agentType");
            return (Criteria) this;
        }

        public Criteria andAgentTypeNotEqualTo(Integer value) {
            addCriterion("agent_type <>", value, "agentType");
            return (Criteria) this;
        }

        public Criteria andAgentTypeGreaterThan(Integer value) {
            addCriterion("agent_type >", value, "agentType");
            return (Criteria) this;
        }

        public Criteria andAgentTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("agent_type >=", value, "agentType");
            return (Criteria) this;
        }

        public Criteria andAgentTypeLessThan(Integer value) {
            addCriterion("agent_type <", value, "agentType");
            return (Criteria) this;
        }

        public Criteria andAgentTypeLessThanOrEqualTo(Integer value) {
            addCriterion("agent_type <=", value, "agentType");
            return (Criteria) this;
        }

        public Criteria andAgentTypeIn(List<Integer> values) {
            addCriterion("agent_type in", values, "agentType");
            return (Criteria) this;
        }

        public Criteria andAgentTypeNotIn(List<Integer> values) {
            addCriterion("agent_type not in", values, "agentType");
            return (Criteria) this;
        }

        public Criteria andAgentTypeBetween(Integer value1, Integer value2) {
            addCriterion("agent_type between", value1, value2, "agentType");
            return (Criteria) this;
        }

        public Criteria andAgentTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("agent_type not between", value1, value2, "agentType");
            return (Criteria) this;
        }

        public Criteria andNickNameIsNull() {
            addCriterion("nick_name is null");
            return (Criteria) this;
        }

        public Criteria andNickNameIsNotNull() {
            addCriterion("nick_name is not null");
            return (Criteria) this;
        }

        public Criteria andNickNameEqualTo(String value) {
            addCriterion("nick_name =", value, "nickName");
            return (Criteria) this;
        }

        public Criteria andNickNameNotEqualTo(String value) {
            addCriterion("nick_name <>", value, "nickName");
            return (Criteria) this;
        }

        public Criteria andNickNameGreaterThan(String value) {
            addCriterion("nick_name >", value, "nickName");
            return (Criteria) this;
        }

        public Criteria andNickNameGreaterThanOrEqualTo(String value) {
            addCriterion("nick_name >=", value, "nickName");
            return (Criteria) this;
        }

        public Criteria andNickNameLessThan(String value) {
            addCriterion("nick_name <", value, "nickName");
            return (Criteria) this;
        }

        public Criteria andNickNameLessThanOrEqualTo(String value) {
            addCriterion("nick_name <=", value, "nickName");
            return (Criteria) this;
        }

        public Criteria andNickNameLike(String value) {
            addCriterion("nick_name like", value, "nickName");
            return (Criteria) this;
        }

        public Criteria andNickNameNotLike(String value) {
            addCriterion("nick_name not like", value, "nickName");
            return (Criteria) this;
        }

        public Criteria andNickNameIn(List<String> values) {
            addCriterion("nick_name in", values, "nickName");
            return (Criteria) this;
        }

        public Criteria andNickNameNotIn(List<String> values) {
            addCriterion("nick_name not in", values, "nickName");
            return (Criteria) this;
        }

        public Criteria andNickNameBetween(String value1, String value2) {
            addCriterion("nick_name between", value1, value2, "nickName");
            return (Criteria) this;
        }

        public Criteria andNickNameNotBetween(String value1, String value2) {
            addCriterion("nick_name not between", value1, value2, "nickName");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNull() {
            addCriterion("group_id is null");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNotNull() {
            addCriterion("group_id is not null");
            return (Criteria) this;
        }

        public Criteria andGroupIdEqualTo(Integer value) {
            addCriterion("group_id =", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotEqualTo(Integer value) {
            addCriterion("group_id <>", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThan(Integer value) {
            addCriterion("group_id >", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("group_id >=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThan(Integer value) {
            addCriterion("group_id <", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThanOrEqualTo(Integer value) {
            addCriterion("group_id <=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdIn(List<Integer> values) {
            addCriterion("group_id in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotIn(List<Integer> values) {
            addCriterion("group_id not in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdBetween(Integer value1, Integer value2) {
            addCriterion("group_id between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotBetween(Integer value1, Integer value2) {
            addCriterion("group_id not between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andAreaIdIsNull() {
            addCriterion("area_id is null");
            return (Criteria) this;
        }

        public Criteria andAreaIdIsNotNull() {
            addCriterion("area_id is not null");
            return (Criteria) this;
        }

        public Criteria andAreaIdEqualTo(Integer value) {
            addCriterion("area_id =", value, "areaId");
            return (Criteria) this;
        }

        public Criteria andAreaIdNotEqualTo(Integer value) {
            addCriterion("area_id <>", value, "areaId");
            return (Criteria) this;
        }

        public Criteria andAreaIdGreaterThan(Integer value) {
            addCriterion("area_id >", value, "areaId");
            return (Criteria) this;
        }

        public Criteria andAreaIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("area_id >=", value, "areaId");
            return (Criteria) this;
        }

        public Criteria andAreaIdLessThan(Integer value) {
            addCriterion("area_id <", value, "areaId");
            return (Criteria) this;
        }

        public Criteria andAreaIdLessThanOrEqualTo(Integer value) {
            addCriterion("area_id <=", value, "areaId");
            return (Criteria) this;
        }

        public Criteria andAreaIdIn(List<Integer> values) {
            addCriterion("area_id in", values, "areaId");
            return (Criteria) this;
        }

        public Criteria andAreaIdNotIn(List<Integer> values) {
            addCriterion("area_id not in", values, "areaId");
            return (Criteria) this;
        }

        public Criteria andAreaIdBetween(Integer value1, Integer value2) {
            addCriterion("area_id between", value1, value2, "areaId");
            return (Criteria) this;
        }

        public Criteria andAreaIdNotBetween(Integer value1, Integer value2) {
            addCriterion("area_id not between", value1, value2, "areaId");
            return (Criteria) this;
        }

        public Criteria andWebsiteNameIsNull() {
            addCriterion("website_name is null");
            return (Criteria) this;
        }

        public Criteria andWebsiteNameIsNotNull() {
            addCriterion("website_name is not null");
            return (Criteria) this;
        }

        public Criteria andWebsiteNameEqualTo(String value) {
            addCriterion("website_name =", value, "websiteName");
            return (Criteria) this;
        }

        public Criteria andWebsiteNameNotEqualTo(String value) {
            addCriterion("website_name <>", value, "websiteName");
            return (Criteria) this;
        }

        public Criteria andWebsiteNameGreaterThan(String value) {
            addCriterion("website_name >", value, "websiteName");
            return (Criteria) this;
        }

        public Criteria andWebsiteNameGreaterThanOrEqualTo(String value) {
            addCriterion("website_name >=", value, "websiteName");
            return (Criteria) this;
        }

        public Criteria andWebsiteNameLessThan(String value) {
            addCriterion("website_name <", value, "websiteName");
            return (Criteria) this;
        }

        public Criteria andWebsiteNameLessThanOrEqualTo(String value) {
            addCriterion("website_name <=", value, "websiteName");
            return (Criteria) this;
        }

        public Criteria andWebsiteNameLike(String value) {
            addCriterion("website_name like", value, "websiteName");
            return (Criteria) this;
        }

        public Criteria andWebsiteNameNotLike(String value) {
            addCriterion("website_name not like", value, "websiteName");
            return (Criteria) this;
        }

        public Criteria andWebsiteNameIn(List<String> values) {
            addCriterion("website_name in", values, "websiteName");
            return (Criteria) this;
        }

        public Criteria andWebsiteNameNotIn(List<String> values) {
            addCriterion("website_name not in", values, "websiteName");
            return (Criteria) this;
        }

        public Criteria andWebsiteNameBetween(String value1, String value2) {
            addCriterion("website_name between", value1, value2, "websiteName");
            return (Criteria) this;
        }

        public Criteria andWebsiteNameNotBetween(String value1, String value2) {
            addCriterion("website_name not between", value1, value2, "websiteName");
            return (Criteria) this;
        }

        public Criteria andWeiboIsNull() {
            addCriterion("weibo is null");
            return (Criteria) this;
        }

        public Criteria andWeiboIsNotNull() {
            addCriterion("weibo is not null");
            return (Criteria) this;
        }

        public Criteria andWeiboEqualTo(String value) {
            addCriterion("weibo =", value, "weibo");
            return (Criteria) this;
        }

        public Criteria andWeiboNotEqualTo(String value) {
            addCriterion("weibo <>", value, "weibo");
            return (Criteria) this;
        }

        public Criteria andWeiboGreaterThan(String value) {
            addCriterion("weibo >", value, "weibo");
            return (Criteria) this;
        }

        public Criteria andWeiboGreaterThanOrEqualTo(String value) {
            addCriterion("weibo >=", value, "weibo");
            return (Criteria) this;
        }

        public Criteria andWeiboLessThan(String value) {
            addCriterion("weibo <", value, "weibo");
            return (Criteria) this;
        }

        public Criteria andWeiboLessThanOrEqualTo(String value) {
            addCriterion("weibo <=", value, "weibo");
            return (Criteria) this;
        }

        public Criteria andWeiboLike(String value) {
            addCriterion("weibo like", value, "weibo");
            return (Criteria) this;
        }

        public Criteria andWeiboNotLike(String value) {
            addCriterion("weibo not like", value, "weibo");
            return (Criteria) this;
        }

        public Criteria andWeiboIn(List<String> values) {
            addCriterion("weibo in", values, "weibo");
            return (Criteria) this;
        }

        public Criteria andWeiboNotIn(List<String> values) {
            addCriterion("weibo not in", values, "weibo");
            return (Criteria) this;
        }

        public Criteria andWeiboBetween(String value1, String value2) {
            addCriterion("weibo between", value1, value2, "weibo");
            return (Criteria) this;
        }

        public Criteria andWeiboNotBetween(String value1, String value2) {
            addCriterion("weibo not between", value1, value2, "weibo");
            return (Criteria) this;
        }

        public Criteria andInternalLinkmanIsNull() {
            addCriterion("internal_linkman is null");
            return (Criteria) this;
        }

        public Criteria andInternalLinkmanIsNotNull() {
            addCriterion("internal_linkman is not null");
            return (Criteria) this;
        }

        public Criteria andInternalLinkmanEqualTo(String value) {
            addCriterion("internal_linkman =", value, "internalLinkman");
            return (Criteria) this;
        }

        public Criteria andInternalLinkmanNotEqualTo(String value) {
            addCriterion("internal_linkman <>", value, "internalLinkman");
            return (Criteria) this;
        }

        public Criteria andInternalLinkmanGreaterThan(String value) {
            addCriterion("internal_linkman >", value, "internalLinkman");
            return (Criteria) this;
        }

        public Criteria andInternalLinkmanGreaterThanOrEqualTo(String value) {
            addCriterion("internal_linkman >=", value, "internalLinkman");
            return (Criteria) this;
        }

        public Criteria andInternalLinkmanLessThan(String value) {
            addCriterion("internal_linkman <", value, "internalLinkman");
            return (Criteria) this;
        }

        public Criteria andInternalLinkmanLessThanOrEqualTo(String value) {
            addCriterion("internal_linkman <=", value, "internalLinkman");
            return (Criteria) this;
        }

        public Criteria andInternalLinkmanLike(String value) {
            addCriterion("internal_linkman like", value, "internalLinkman");
            return (Criteria) this;
        }

        public Criteria andInternalLinkmanNotLike(String value) {
            addCriterion("internal_linkman not like", value, "internalLinkman");
            return (Criteria) this;
        }

        public Criteria andInternalLinkmanIn(List<String> values) {
            addCriterion("internal_linkman in", values, "internalLinkman");
            return (Criteria) this;
        }

        public Criteria andInternalLinkmanNotIn(List<String> values) {
            addCriterion("internal_linkman not in", values, "internalLinkman");
            return (Criteria) this;
        }

        public Criteria andInternalLinkmanBetween(String value1, String value2) {
            addCriterion("internal_linkman between", value1, value2, "internalLinkman");
            return (Criteria) this;
        }

        public Criteria andInternalLinkmanNotBetween(String value1, String value2) {
            addCriterion("internal_linkman not between", value1, value2, "internalLinkman");
            return (Criteria) this;
        }

        public Criteria andLinkmanPhoneIsNull() {
            addCriterion("linkman_phone is null");
            return (Criteria) this;
        }

        public Criteria andLinkmanPhoneIsNotNull() {
            addCriterion("linkman_phone is not null");
            return (Criteria) this;
        }

        public Criteria andLinkmanPhoneEqualTo(String value) {
            addCriterion("linkman_phone =", value, "linkmanPhone");
            return (Criteria) this;
        }

        public Criteria andLinkmanPhoneNotEqualTo(String value) {
            addCriterion("linkman_phone <>", value, "linkmanPhone");
            return (Criteria) this;
        }

        public Criteria andLinkmanPhoneGreaterThan(String value) {
            addCriterion("linkman_phone >", value, "linkmanPhone");
            return (Criteria) this;
        }

        public Criteria andLinkmanPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("linkman_phone >=", value, "linkmanPhone");
            return (Criteria) this;
        }

        public Criteria andLinkmanPhoneLessThan(String value) {
            addCriterion("linkman_phone <", value, "linkmanPhone");
            return (Criteria) this;
        }

        public Criteria andLinkmanPhoneLessThanOrEqualTo(String value) {
            addCriterion("linkman_phone <=", value, "linkmanPhone");
            return (Criteria) this;
        }

        public Criteria andLinkmanPhoneLike(String value) {
            addCriterion("linkman_phone like", value, "linkmanPhone");
            return (Criteria) this;
        }

        public Criteria andLinkmanPhoneNotLike(String value) {
            addCriterion("linkman_phone not like", value, "linkmanPhone");
            return (Criteria) this;
        }

        public Criteria andLinkmanPhoneIn(List<String> values) {
            addCriterion("linkman_phone in", values, "linkmanPhone");
            return (Criteria) this;
        }

        public Criteria andLinkmanPhoneNotIn(List<String> values) {
            addCriterion("linkman_phone not in", values, "linkmanPhone");
            return (Criteria) this;
        }

        public Criteria andLinkmanPhoneBetween(String value1, String value2) {
            addCriterion("linkman_phone between", value1, value2, "linkmanPhone");
            return (Criteria) this;
        }

        public Criteria andLinkmanPhoneNotBetween(String value1, String value2) {
            addCriterion("linkman_phone not between", value1, value2, "linkmanPhone");
            return (Criteria) this;
        }

        public Criteria andLinkmanEmailIsNull() {
            addCriterion("linkman_email is null");
            return (Criteria) this;
        }

        public Criteria andLinkmanEmailIsNotNull() {
            addCriterion("linkman_email is not null");
            return (Criteria) this;
        }

        public Criteria andLinkmanEmailEqualTo(String value) {
            addCriterion("linkman_email =", value, "linkmanEmail");
            return (Criteria) this;
        }

        public Criteria andLinkmanEmailNotEqualTo(String value) {
            addCriterion("linkman_email <>", value, "linkmanEmail");
            return (Criteria) this;
        }

        public Criteria andLinkmanEmailGreaterThan(String value) {
            addCriterion("linkman_email >", value, "linkmanEmail");
            return (Criteria) this;
        }

        public Criteria andLinkmanEmailGreaterThanOrEqualTo(String value) {
            addCriterion("linkman_email >=", value, "linkmanEmail");
            return (Criteria) this;
        }

        public Criteria andLinkmanEmailLessThan(String value) {
            addCriterion("linkman_email <", value, "linkmanEmail");
            return (Criteria) this;
        }

        public Criteria andLinkmanEmailLessThanOrEqualTo(String value) {
            addCriterion("linkman_email <=", value, "linkmanEmail");
            return (Criteria) this;
        }

        public Criteria andLinkmanEmailLike(String value) {
            addCriterion("linkman_email like", value, "linkmanEmail");
            return (Criteria) this;
        }

        public Criteria andLinkmanEmailNotLike(String value) {
            addCriterion("linkman_email not like", value, "linkmanEmail");
            return (Criteria) this;
        }

        public Criteria andLinkmanEmailIn(List<String> values) {
            addCriterion("linkman_email in", values, "linkmanEmail");
            return (Criteria) this;
        }

        public Criteria andLinkmanEmailNotIn(List<String> values) {
            addCriterion("linkman_email not in", values, "linkmanEmail");
            return (Criteria) this;
        }

        public Criteria andLinkmanEmailBetween(String value1, String value2) {
            addCriterion("linkman_email between", value1, value2, "linkmanEmail");
            return (Criteria) this;
        }

        public Criteria andLinkmanEmailNotBetween(String value1, String value2) {
            addCriterion("linkman_email not between", value1, value2, "linkmanEmail");
            return (Criteria) this;
        }

        public Criteria andLinkmanAddressIsNull() {
            addCriterion("linkman_address is null");
            return (Criteria) this;
        }

        public Criteria andLinkmanAddressIsNotNull() {
            addCriterion("linkman_address is not null");
            return (Criteria) this;
        }

        public Criteria andLinkmanAddressEqualTo(String value) {
            addCriterion("linkman_address =", value, "linkmanAddress");
            return (Criteria) this;
        }

        public Criteria andLinkmanAddressNotEqualTo(String value) {
            addCriterion("linkman_address <>", value, "linkmanAddress");
            return (Criteria) this;
        }

        public Criteria andLinkmanAddressGreaterThan(String value) {
            addCriterion("linkman_address >", value, "linkmanAddress");
            return (Criteria) this;
        }

        public Criteria andLinkmanAddressGreaterThanOrEqualTo(String value) {
            addCriterion("linkman_address >=", value, "linkmanAddress");
            return (Criteria) this;
        }

        public Criteria andLinkmanAddressLessThan(String value) {
            addCriterion("linkman_address <", value, "linkmanAddress");
            return (Criteria) this;
        }

        public Criteria andLinkmanAddressLessThanOrEqualTo(String value) {
            addCriterion("linkman_address <=", value, "linkmanAddress");
            return (Criteria) this;
        }

        public Criteria andLinkmanAddressLike(String value) {
            addCriterion("linkman_address like", value, "linkmanAddress");
            return (Criteria) this;
        }

        public Criteria andLinkmanAddressNotLike(String value) {
            addCriterion("linkman_address not like", value, "linkmanAddress");
            return (Criteria) this;
        }

        public Criteria andLinkmanAddressIn(List<String> values) {
            addCriterion("linkman_address in", values, "linkmanAddress");
            return (Criteria) this;
        }

        public Criteria andLinkmanAddressNotIn(List<String> values) {
            addCriterion("linkman_address not in", values, "linkmanAddress");
            return (Criteria) this;
        }

        public Criteria andLinkmanAddressBetween(String value1, String value2) {
            addCriterion("linkman_address between", value1, value2, "linkmanAddress");
            return (Criteria) this;
        }

        public Criteria andLinkmanAddressNotBetween(String value1, String value2) {
            addCriterion("linkman_address not between", value1, value2, "linkmanAddress");
            return (Criteria) this;
        }

        public Criteria andBankIsNull() {
            addCriterion("bank is null");
            return (Criteria) this;
        }

        public Criteria andBankIsNotNull() {
            addCriterion("bank is not null");
            return (Criteria) this;
        }

        public Criteria andBankEqualTo(String value) {
            addCriterion("bank =", value, "bank");
            return (Criteria) this;
        }

        public Criteria andBankNotEqualTo(String value) {
            addCriterion("bank <>", value, "bank");
            return (Criteria) this;
        }

        public Criteria andBankGreaterThan(String value) {
            addCriterion("bank >", value, "bank");
            return (Criteria) this;
        }

        public Criteria andBankGreaterThanOrEqualTo(String value) {
            addCriterion("bank >=", value, "bank");
            return (Criteria) this;
        }

        public Criteria andBankLessThan(String value) {
            addCriterion("bank <", value, "bank");
            return (Criteria) this;
        }

        public Criteria andBankLessThanOrEqualTo(String value) {
            addCriterion("bank <=", value, "bank");
            return (Criteria) this;
        }

        public Criteria andBankLike(String value) {
            addCriterion("bank like", value, "bank");
            return (Criteria) this;
        }

        public Criteria andBankNotLike(String value) {
            addCriterion("bank not like", value, "bank");
            return (Criteria) this;
        }

        public Criteria andBankIn(List<String> values) {
            addCriterion("bank in", values, "bank");
            return (Criteria) this;
        }

        public Criteria andBankNotIn(List<String> values) {
            addCriterion("bank not in", values, "bank");
            return (Criteria) this;
        }

        public Criteria andBankBetween(String value1, String value2) {
            addCriterion("bank between", value1, value2, "bank");
            return (Criteria) this;
        }

        public Criteria andBankNotBetween(String value1, String value2) {
            addCriterion("bank not between", value1, value2, "bank");
            return (Criteria) this;
        }

        public Criteria andCategoryFirstIdIsNull() {
            addCriterion("category_first_id is null");
            return (Criteria) this;
        }

        public Criteria andCategoryFirstIdIsNotNull() {
            addCriterion("category_first_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryFirstIdEqualTo(Integer value) {
            addCriterion("category_first_id =", value, "categoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCategoryFirstIdNotEqualTo(Integer value) {
            addCriterion("category_first_id <>", value, "categoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCategoryFirstIdGreaterThan(Integer value) {
            addCriterion("category_first_id >", value, "categoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCategoryFirstIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("category_first_id >=", value, "categoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCategoryFirstIdLessThan(Integer value) {
            addCriterion("category_first_id <", value, "categoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCategoryFirstIdLessThanOrEqualTo(Integer value) {
            addCriterion("category_first_id <=", value, "categoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCategoryFirstIdIn(List<Integer> values) {
            addCriterion("category_first_id in", values, "categoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCategoryFirstIdNotIn(List<Integer> values) {
            addCriterion("category_first_id not in", values, "categoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCategoryFirstIdBetween(Integer value1, Integer value2) {
            addCriterion("category_first_id between", value1, value2, "categoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCategoryFirstIdNotBetween(Integer value1, Integer value2) {
            addCriterion("category_first_id not between", value1, value2, "categoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCategorySecondIdIsNull() {
            addCriterion("category_second_id is null");
            return (Criteria) this;
        }

        public Criteria andCategorySecondIdIsNotNull() {
            addCriterion("category_second_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategorySecondIdEqualTo(Integer value) {
            addCriterion("category_second_id =", value, "categorySecondId");
            return (Criteria) this;
        }

        public Criteria andCategorySecondIdNotEqualTo(Integer value) {
            addCriterion("category_second_id <>", value, "categorySecondId");
            return (Criteria) this;
        }

        public Criteria andCategorySecondIdGreaterThan(Integer value) {
            addCriterion("category_second_id >", value, "categorySecondId");
            return (Criteria) this;
        }

        public Criteria andCategorySecondIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("category_second_id >=", value, "categorySecondId");
            return (Criteria) this;
        }

        public Criteria andCategorySecondIdLessThan(Integer value) {
            addCriterion("category_second_id <", value, "categorySecondId");
            return (Criteria) this;
        }

        public Criteria andCategorySecondIdLessThanOrEqualTo(Integer value) {
            addCriterion("category_second_id <=", value, "categorySecondId");
            return (Criteria) this;
        }

        public Criteria andCategorySecondIdIn(List<Integer> values) {
            addCriterion("category_second_id in", values, "categorySecondId");
            return (Criteria) this;
        }

        public Criteria andCategorySecondIdNotIn(List<Integer> values) {
            addCriterion("category_second_id not in", values, "categorySecondId");
            return (Criteria) this;
        }

        public Criteria andCategorySecondIdBetween(Integer value1, Integer value2) {
            addCriterion("category_second_id between", value1, value2, "categorySecondId");
            return (Criteria) this;
        }

        public Criteria andCategorySecondIdNotBetween(Integer value1, Integer value2) {
            addCriterion("category_second_id not between", value1, value2, "categorySecondId");
            return (Criteria) this;
        }

        public Criteria andPersonalPhoneIsNull() {
            addCriterion("personal_phone is null");
            return (Criteria) this;
        }

        public Criteria andPersonalPhoneIsNotNull() {
            addCriterion("personal_phone is not null");
            return (Criteria) this;
        }

        public Criteria andPersonalPhoneEqualTo(String value) {
            addCriterion("personal_phone =", value, "personalPhone");
            return (Criteria) this;
        }

        public Criteria andPersonalPhoneNotEqualTo(String value) {
            addCriterion("personal_phone <>", value, "personalPhone");
            return (Criteria) this;
        }

        public Criteria andPersonalPhoneGreaterThan(String value) {
            addCriterion("personal_phone >", value, "personalPhone");
            return (Criteria) this;
        }

        public Criteria andPersonalPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("personal_phone >=", value, "personalPhone");
            return (Criteria) this;
        }

        public Criteria andPersonalPhoneLessThan(String value) {
            addCriterion("personal_phone <", value, "personalPhone");
            return (Criteria) this;
        }

        public Criteria andPersonalPhoneLessThanOrEqualTo(String value) {
            addCriterion("personal_phone <=", value, "personalPhone");
            return (Criteria) this;
        }

        public Criteria andPersonalPhoneLike(String value) {
            addCriterion("personal_phone like", value, "personalPhone");
            return (Criteria) this;
        }

        public Criteria andPersonalPhoneNotLike(String value) {
            addCriterion("personal_phone not like", value, "personalPhone");
            return (Criteria) this;
        }

        public Criteria andPersonalPhoneIn(List<String> values) {
            addCriterion("personal_phone in", values, "personalPhone");
            return (Criteria) this;
        }

        public Criteria andPersonalPhoneNotIn(List<String> values) {
            addCriterion("personal_phone not in", values, "personalPhone");
            return (Criteria) this;
        }

        public Criteria andPersonalPhoneBetween(String value1, String value2) {
            addCriterion("personal_phone between", value1, value2, "personalPhone");
            return (Criteria) this;
        }

        public Criteria andPersonalPhoneNotBetween(String value1, String value2) {
            addCriterion("personal_phone not between", value1, value2, "personalPhone");
            return (Criteria) this;
        }

        public Criteria andPersonalAddressIsNull() {
            addCriterion("personal_address is null");
            return (Criteria) this;
        }

        public Criteria andPersonalAddressIsNotNull() {
            addCriterion("personal_address is not null");
            return (Criteria) this;
        }

        public Criteria andPersonalAddressEqualTo(String value) {
            addCriterion("personal_address =", value, "personalAddress");
            return (Criteria) this;
        }

        public Criteria andPersonalAddressNotEqualTo(String value) {
            addCriterion("personal_address <>", value, "personalAddress");
            return (Criteria) this;
        }

        public Criteria andPersonalAddressGreaterThan(String value) {
            addCriterion("personal_address >", value, "personalAddress");
            return (Criteria) this;
        }

        public Criteria andPersonalAddressGreaterThanOrEqualTo(String value) {
            addCriterion("personal_address >=", value, "personalAddress");
            return (Criteria) this;
        }

        public Criteria andPersonalAddressLessThan(String value) {
            addCriterion("personal_address <", value, "personalAddress");
            return (Criteria) this;
        }

        public Criteria andPersonalAddressLessThanOrEqualTo(String value) {
            addCriterion("personal_address <=", value, "personalAddress");
            return (Criteria) this;
        }

        public Criteria andPersonalAddressLike(String value) {
            addCriterion("personal_address like", value, "personalAddress");
            return (Criteria) this;
        }

        public Criteria andPersonalAddressNotLike(String value) {
            addCriterion("personal_address not like", value, "personalAddress");
            return (Criteria) this;
        }

        public Criteria andPersonalAddressIn(List<String> values) {
            addCriterion("personal_address in", values, "personalAddress");
            return (Criteria) this;
        }

        public Criteria andPersonalAddressNotIn(List<String> values) {
            addCriterion("personal_address not in", values, "personalAddress");
            return (Criteria) this;
        }

        public Criteria andPersonalAddressBetween(String value1, String value2) {
            addCriterion("personal_address between", value1, value2, "personalAddress");
            return (Criteria) this;
        }

        public Criteria andPersonalAddressNotBetween(String value1, String value2) {
            addCriterion("personal_address not between", value1, value2, "personalAddress");
            return (Criteria) this;
        }

        public Criteria andDomainIsNull() {
            addCriterion("domain is null");
            return (Criteria) this;
        }

        public Criteria andDomainIsNotNull() {
            addCriterion("domain is not null");
            return (Criteria) this;
        }

        public Criteria andDomainEqualTo(String value) {
            addCriterion("domain =", value, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainNotEqualTo(String value) {
            addCriterion("domain <>", value, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainGreaterThan(String value) {
            addCriterion("domain >", value, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainGreaterThanOrEqualTo(String value) {
            addCriterion("domain >=", value, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainLessThan(String value) {
            addCriterion("domain <", value, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainLessThanOrEqualTo(String value) {
            addCriterion("domain <=", value, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainLike(String value) {
            addCriterion("domain like", value, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainNotLike(String value) {
            addCriterion("domain not like", value, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainIn(List<String> values) {
            addCriterion("domain in", values, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainNotIn(List<String> values) {
            addCriterion("domain not in", values, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainBetween(String value1, String value2) {
            addCriterion("domain between", value1, value2, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainNotBetween(String value1, String value2) {
            addCriterion("domain not between", value1, value2, "domain");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusIsNull() {
            addCriterion("customer_status is null");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusIsNotNull() {
            addCriterion("customer_status is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusEqualTo(Integer value) {
            addCriterion("customer_status =", value, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusNotEqualTo(Integer value) {
            addCriterion("customer_status <>", value, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusGreaterThan(Integer value) {
            addCriterion("customer_status >", value, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_status >=", value, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusLessThan(Integer value) {
            addCriterion("customer_status <", value, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusLessThanOrEqualTo(Integer value) {
            addCriterion("customer_status <=", value, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusIn(List<Integer> values) {
            addCriterion("customer_status in", values, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusNotIn(List<Integer> values) {
            addCriterion("customer_status not in", values, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusBetween(Integer value1, Integer value2) {
            addCriterion("customer_status between", value1, value2, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_status not between", value1, value2, "customerStatus");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(String value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(String value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(String value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(String value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(String value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(String value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLike(String value) {
            addCriterion("creator like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotLike(String value) {
            addCriterion("creator not like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<String> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<String> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(String value1, String value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(String value1, String value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andCommerceCategoryFirstIdIsNull() {
            addCriterion("commerce_category_first_id is null");
            return (Criteria) this;
        }

        public Criteria andCommerceCategoryFirstIdIsNotNull() {
            addCriterion("commerce_category_first_id is not null");
            return (Criteria) this;
        }

        public Criteria andCommerceCategoryFirstIdEqualTo(Integer value) {
            addCriterion("commerce_category_first_id =", value, "commerceCategoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCommerceCategoryFirstIdNotEqualTo(Integer value) {
            addCriterion("commerce_category_first_id <>", value, "commerceCategoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCommerceCategoryFirstIdGreaterThan(Integer value) {
            addCriterion("commerce_category_first_id >", value, "commerceCategoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCommerceCategoryFirstIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("commerce_category_first_id >=", value, "commerceCategoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCommerceCategoryFirstIdLessThan(Integer value) {
            addCriterion("commerce_category_first_id <", value, "commerceCategoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCommerceCategoryFirstIdLessThanOrEqualTo(Integer value) {
            addCriterion("commerce_category_first_id <=", value, "commerceCategoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCommerceCategoryFirstIdIn(List<Integer> values) {
            addCriterion("commerce_category_first_id in", values, "commerceCategoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCommerceCategoryFirstIdNotIn(List<Integer> values) {
            addCriterion("commerce_category_first_id not in", values, "commerceCategoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCommerceCategoryFirstIdBetween(Integer value1, Integer value2) {
            addCriterion("commerce_category_first_id between", value1, value2, "commerceCategoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCommerceCategoryFirstIdNotBetween(Integer value1, Integer value2) {
            addCriterion("commerce_category_first_id not between", value1, value2, "commerceCategoryFirstId");
            return (Criteria) this;
        }

        public Criteria andCommerceCategorySecondIdIsNull() {
            addCriterion("commerce_category_second_id is null");
            return (Criteria) this;
        }

        public Criteria andCommerceCategorySecondIdIsNotNull() {
            addCriterion("commerce_category_second_id is not null");
            return (Criteria) this;
        }

        public Criteria andCommerceCategorySecondIdEqualTo(Integer value) {
            addCriterion("commerce_category_second_id =", value, "commerceCategorySecondId");
            return (Criteria) this;
        }

        public Criteria andCommerceCategorySecondIdNotEqualTo(Integer value) {
            addCriterion("commerce_category_second_id <>", value, "commerceCategorySecondId");
            return (Criteria) this;
        }

        public Criteria andCommerceCategorySecondIdGreaterThan(Integer value) {
            addCriterion("commerce_category_second_id >", value, "commerceCategorySecondId");
            return (Criteria) this;
        }

        public Criteria andCommerceCategorySecondIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("commerce_category_second_id >=", value, "commerceCategorySecondId");
            return (Criteria) this;
        }

        public Criteria andCommerceCategorySecondIdLessThan(Integer value) {
            addCriterion("commerce_category_second_id <", value, "commerceCategorySecondId");
            return (Criteria) this;
        }

        public Criteria andCommerceCategorySecondIdLessThanOrEqualTo(Integer value) {
            addCriterion("commerce_category_second_id <=", value, "commerceCategorySecondId");
            return (Criteria) this;
        }

        public Criteria andCommerceCategorySecondIdIn(List<Integer> values) {
            addCriterion("commerce_category_second_id in", values, "commerceCategorySecondId");
            return (Criteria) this;
        }

        public Criteria andCommerceCategorySecondIdNotIn(List<Integer> values) {
            addCriterion("commerce_category_second_id not in", values, "commerceCategorySecondId");
            return (Criteria) this;
        }

        public Criteria andCommerceCategorySecondIdBetween(Integer value1, Integer value2) {
            addCriterion("commerce_category_second_id between", value1, value2, "commerceCategorySecondId");
            return (Criteria) this;
        }

        public Criteria andCommerceCategorySecondIdNotBetween(Integer value1, Integer value2) {
            addCriterion("commerce_category_second_id not between", value1, value2, "commerceCategorySecondId");
            return (Criteria) this;
        }

        public Criteria andStoreAddressIsNull() {
            addCriterion("store_address is null");
            return (Criteria) this;
        }

        public Criteria andStoreAddressIsNotNull() {
            addCriterion("store_address is not null");
            return (Criteria) this;
        }

        public Criteria andStoreAddressEqualTo(String value) {
            addCriterion("store_address =", value, "storeAddress");
            return (Criteria) this;
        }

        public Criteria andStoreAddressNotEqualTo(String value) {
            addCriterion("store_address <>", value, "storeAddress");
            return (Criteria) this;
        }

        public Criteria andStoreAddressGreaterThan(String value) {
            addCriterion("store_address >", value, "storeAddress");
            return (Criteria) this;
        }

        public Criteria andStoreAddressGreaterThanOrEqualTo(String value) {
            addCriterion("store_address >=", value, "storeAddress");
            return (Criteria) this;
        }

        public Criteria andStoreAddressLessThan(String value) {
            addCriterion("store_address <", value, "storeAddress");
            return (Criteria) this;
        }

        public Criteria andStoreAddressLessThanOrEqualTo(String value) {
            addCriterion("store_address <=", value, "storeAddress");
            return (Criteria) this;
        }

        public Criteria andStoreAddressLike(String value) {
            addCriterion("store_address like", value, "storeAddress");
            return (Criteria) this;
        }

        public Criteria andStoreAddressNotLike(String value) {
            addCriterion("store_address not like", value, "storeAddress");
            return (Criteria) this;
        }

        public Criteria andStoreAddressIn(List<String> values) {
            addCriterion("store_address in", values, "storeAddress");
            return (Criteria) this;
        }

        public Criteria andStoreAddressNotIn(List<String> values) {
            addCriterion("store_address not in", values, "storeAddress");
            return (Criteria) this;
        }

        public Criteria andStoreAddressBetween(String value1, String value2) {
            addCriterion("store_address between", value1, value2, "storeAddress");
            return (Criteria) this;
        }

        public Criteria andStoreAddressNotBetween(String value1, String value2) {
            addCriterion("store_address not between", value1, value2, "storeAddress");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}