package com.bilibili.mgk.platform.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bapis.ad.adp.app_package.AppPackageServiceGrpc;
import com.bapis.ad.adp.app_package.CopyAppItem;
import com.bapis.ad.adp.app_package.CopyAppReplay;
import com.bapis.ad.adp.app_package.CopyAppRequest;
import com.bapis.ad.adp.mini_game.CopyMiniGameItem;
import com.bapis.ad.adp.mini_game.CopyMiniGameReply;
import com.bapis.ad.adp.mini_game.CopyMiniGameRequest;
import com.bapis.ad.adp.mini_game.MiniGameServiceGrpc;
import com.bapis.datacenter.service.oneservice.*;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.AppPackageStatus;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.*;
import com.bilibili.adp.passport.api.dto.ArchiveDetail;
import com.bilibili.adp.passport.biz.manager.ArchiveManager;
import com.bilibili.adp.resource.api.app_package.dto.AppPackageDto;
import com.bilibili.adp.resource.api.app_package.dto.QueryAppPackageDto;
import com.bilibili.adp.resource.api.soa.ISoaAppPackageService;
import com.bilibili.crm.platform.soa.ISoaAccountLabelService;
import com.bilibili.mgk.platform.api.account.IMgkAccountService;
import com.bilibili.mgk.platform.api.audit.dto.MgkAuditPageActDto;
import com.bilibili.mgk.platform.api.audit.service.IMgkAuditPageService;
import com.bilibili.mgk.platform.api.landing_page.dto.*;
import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageService;
import com.bilibili.mgk.platform.api.landing_page_group.dto.mapping.LandingPageGroupMappingDto;
import com.bilibili.mgk.platform.api.landing_page_group.dto.mapping.QueryLandingPageGroupMappingDto;
import com.bilibili.mgk.platform.api.landing_page_group.service.ILandingPageGroupService;
import com.bilibili.mgk.platform.api.landing_page_group.service.mapping.ILandingPageGroupMappingService;
import com.bilibili.mgk.platform.api.log.dto.MgkPageAuditRecordInfoDto;
import com.bilibili.mgk.platform.api.log.dto.MgkPageOperateLogDto;
import com.bilibili.mgk.platform.api.log.dto.NewLogOperationDto;
import com.bilibili.mgk.platform.api.log.service.IMgkLogService;
import com.bilibili.mgk.platform.api.log.service.IMgkPageOperateLogService;
import com.bilibili.mgk.platform.api.message_component.CreateMessageComponentDto;
import com.bilibili.mgk.platform.api.shadow.IMgkShadowLandingPageService;
import com.bilibili.mgk.platform.api.wechat.dto.WechatAccountQueryDto;
import com.bilibili.mgk.platform.api.wechat.dto.WechatPackageCreateDto;
import com.bilibili.mgk.platform.api.wechat.service.IMgkWechatPackageService;
import com.bilibili.mgk.platform.biz.dao.*;
import com.bilibili.mgk.platform.biz.grpc.MgkGrpcManager;
import com.bilibili.mgk.platform.biz.po.*;
import com.bilibili.mgk.platform.biz.service.page.LandingPageUrlProc;
import com.bilibili.mgk.platform.biz.service.wechat.delegate.MgkWechatPackageServiceDelegate;
import com.bilibili.mgk.platform.biz.utils.DateUtils;
import com.bilibili.mgk.platform.biz.utils.MgkCatUtils;
import com.bilibili.mgk.platform.common.*;
import com.bilibili.mgk.platform.common.page_bean.MgkLandingPageBean;
import com.bilibili.mgk.platform.common.page_bean.PageConfig;
import com.bilibili.mgk.platform.common.page_group.PageGroupSourceEnum;
import com.bilibili.mgk.platform.common.utils.ExceptionUtils;
import com.bilibili.mgk.platform.common.utils.TimeUtil;
import com.dianping.cat.Cat;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Timestamp;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.alibaba.fastjson.JSON.toJSON;

/**
 * <AUTHOR>
 * @date 2018/1/18
 **/
@Service
@Slf4j
public class MgkLandingPageServiceImpl implements IMgkLandingPageService {

    @Autowired
    private MgkBaseService mgkBaseService;
    @Autowired
    private LandingPageServiceDelegate landingPageServiceDelegate;
    @Autowired
    private IMgkShadowLandingPageService mgkShadowLandingPageService;
    @Autowired
    private IMgkAuditPageService mgkAuditPageService;
    @Autowired
    private ISoaAppPackageService soaAppPackageService;
    @Autowired
    private ArchiveManager archiveManager;
    @Autowired
    private MgkGrpcManager mgkGrpcManager;

    @Autowired
    private LandingPageWithMacroParamQuerier landingPageWithMacroParamQuerier;

    @Autowired
    private LandingPageUrlProc landingPageUrlProc;
    @Autowired
    private IMgkPageOperateLogService mgkPageOperateLogService;

    @Autowired
    private MgkLandingPageConfigDao mgkLandingPageConfigDao;

    @Autowired
    private IMgkLogService mgkLogService;

    @Autowired
    private ILandingPageGroupService landingPageGroupService;

    @Autowired
    private ILandingPageGroupMappingService landingPageGroupMappingService;

    @Autowired
    private MgkLandingPageDao landingPageDao;

    @Resource
    private MgkFormDao formDao;
    @Resource
    private MgkFormItemDao formItemDao;
    @Resource
    private MgkWechatPackageDao mgkWechatPackageDao;
    @Resource
    private MgkPageWechatPackageMappingDao mgkPageWechatPackageMappingDao;
    @Resource
    private ISoaAccountLabelService accountLabelService;
    @Resource
    @Lazy
    private IMgkWechatPackageService mgkWechatPackageService;
    @Resource
    @Lazy
    private MgkWechatPackageServiceDelegate mgkWechatPackageServiceDelegate;
    @Resource
    private SnowflakeIdWorker snowflakeIdWorker;
    @Resource
    private MiniGameServiceGrpc.MiniGameServiceBlockingStub miniGameServiceBlockingStub;
    @Resource
    private AppPackageServiceGrpc.AppPackageServiceBlockingStub appPackageServiceBlockingStub;
    @Resource
    private IMgkAccountService iMgkAccountService;
    @Resource
    private MgkWechatAccountDao mgkWechatAccountDao;
    @Resource
    private LbsShopDao lbsShopDao;
    @Resource(name = "cacheTaskExecutor")
    private ThreadPoolTaskExecutor cacheTaskExecutor;
    @Autowired
    private OneServiceOpenApiManagerGrpc.OneServiceOpenApiManagerBlockingStub oneServiceOpenApiManagerBlockingStub;

    private final static OsHeader api2940Header = OsHeader.newBuilder()
            .setAppKey("496b1dd505103413f42117ff1d5840ca")
            .setSecret("tL4vGnTz03chln2R6iiMEQi8ZC/WLKS+NsLSbkP0AAs=")
            .setApiId("api_2940")
            .build();

    //新版我的落地页查询限制的最长时间
    @Value("${mgk.new.my.landing.page.query.limit.days:31}")
    private Integer myNewLandingPageQueryLimitDays;

    private static final Integer REFRESH_PAGE_LIMIT = 1000;

    LoadingCache<Long, MgkLandingPageDto> cache = CacheBuilder.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(35, TimeUnit.SECONDS)
            .refreshAfterWrite(30, TimeUnit.SECONDS)
            .build(new CacheLoader<Long, MgkLandingPageDto>() {
                @Override
                public MgkLandingPageDto load(Long key) throws Exception {
                    return getLandingPageDtoByPageId(key);
                }

                @Override
                public ListenableFuture<MgkLandingPageDto> reload(Long key, MgkLandingPageDto oldValue) throws Exception {
                    ListenableFutureTask<MgkLandingPageDto> task = ListenableFutureTask.create(() -> load(key));
                    cacheTaskExecutor.submit(task);
                    return task;
                }
            });

    @Override
    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public long create(Operator operator, NewLandingPageDto newLandingPageDto) {
        // 创建落地页
        long pageId = landingPageServiceDelegate.create(operator, newLandingPageDto);
        // 创建落地页模板
        landingPageServiceDelegate.createMgkLandingPageTemplatePage(operator, pageId, newLandingPageDto);
        // 创建无视频图文替换模板
        landingPageServiceDelegate.createLandingPageTemplateWithoutProperVideoComponent(operator, pageId, newLandingPageDto);

        return pageId;
    }

    @Override
    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public long createWithShadow(Operator operator, NewLandingPageDto newLandingPageDto) {
        long pageId = create(operator, newLandingPageDto);
        mgkShadowLandingPageService.createShadowPage(operator, pageId, newLandingPageDto);
        return pageId;
    }

    @Override
    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public long createSingle(Operator operator, NewLandingPageDto newLandingPageDto) {
        // 创建落地页
        return landingPageServiceDelegate.create(operator, newLandingPageDto);
    }

    @Override
    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void update(Operator operator, UpdateLandingPageDto updateLandingPageDto) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notNull(updateLandingPageDto, "落地页信息不可为空");
        Assert.notNull(updateLandingPageDto.getPageId(), "落地页页面ID不可为空");

        RLock lock = mgkBaseService.getLock(updateLandingPageDto.getPageId(), MgkConstants.PAGE_LOCK_SUFFIX);
        try {
            landingPageServiceDelegate.update(operator, updateLandingPageDto, true);
            landingPageServiceDelegate.updateMgkLandingPageTemplatePage(operator, updateLandingPageDto);
            landingPageServiceDelegate.updateLandingPageTemplateWithoutProperVideoComponent(operator, updateLandingPageDto);
        } finally {
            log.info(System.currentTimeMillis() + "---update landingPage unLock----");
            lock.unlock();
        }
    }

    @Override
    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void manualUpdate(Operator operator, UpdateLandingPageDto updateLandingPageDto) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notNull(updateLandingPageDto, "落地页信息不可为空");
        Assert.notNull(updateLandingPageDto.getPageId(), "落地页页面ID不可为空");

        RLock lock = mgkBaseService.getLock(updateLandingPageDto.getPageId(), MgkConstants.PAGE_LOCK_SUFFIX);
        try {
            Long originPageId = updateLandingPageDto.getPageId();
            MgkLandingPageDto originPageDto =
                    this.getLandingPageDtoByPageId(originPageId);
            Long shadowPageId = mgkShadowLandingPageService.getShadowPageIdByPageId(originPageId);
            // 已发布落地页或者执行发布操作 更新影子落地页来准备推审 禁止更改线上版本
            if (LandingPageStatusEnum.PUBLISHED.getCode().equals(originPageDto.getStatus())) {
                updateLandingPageDto.setIsShadowOriginPublished(WhetherEnum.YES.getCode());
                mgkShadowLandingPageService.updateShadowPage(operator, updateLandingPageDto);
            } else if (Utils.isPositive(updateLandingPageDto.getIsForPublish())) {
                mgkShadowLandingPageService.updateShadowPage(operator, updateLandingPageDto);
            }
            // 非已发布落地页 不存在线上版本 更新原始落地页作为编辑版本
            if (!LandingPageStatusEnum.PUBLISHED.getCode().equals(originPageDto.getStatus())) {
                landingPageServiceDelegate.update(operator, updateLandingPageDto, true);
                if (Utils.isPositive(shadowPageId)) {
                    landingPageServiceDelegate.resetUnpublished(operator, shadowPageId);
                }
            }
        } finally {
            log.info(System.currentTimeMillis() + "---manualUpdate landingPage unLock----");
            lock.unlock();
        }
    }

    @Override
    public void updateName(UpdateLandingPageNameDto updateLandingPageNameDto) {
        Assert.isTrue(Utils.isPositive(updateLandingPageNameDto.getAccountId()), "账户id不可为空");
        Assert.isTrue(Utils.isPositive(updateLandingPageNameDto.getPageId()), "落地页id不可为空");
        Assert.isTrue(!StringUtils.isEmpty(updateLandingPageNameDto.getPageName()), "落地页名称不可为空");
        RLock lock = mgkBaseService.getLock(updateLandingPageNameDto.getPageId(), MgkConstants.PAGE_LOCK_SUFFIX);
        try {
            landingPageServiceDelegate.updateLandingPageName(updateLandingPageNameDto);
        } finally {
            log.info(System.currentTimeMillis() + "---updateName landingPage unLock----");
            lock.unlock();
        }
    }

    /**
     * 更新单个落地页 目前嵌套在别的更新行为中进行 不需要重新获取分布式锁
     */
    @Override
    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void updateSingle(Operator operator, UpdateLandingPageDto updateLandingPageDto) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notNull(updateLandingPageDto, "落地页信息不可为空");
        Assert.notNull(updateLandingPageDto.getPageId(), "落地页页面ID不可为空");
        landingPageServiceDelegate.update(operator, updateLandingPageDto, true);
    }

    @Override
    public long copy(Operator operator, NewLandingPageDto newLandingPageDto) {
        return landingPageServiceDelegate.copy(operator, newLandingPageDto);
    }

    @Override
    @Deprecated
    public void publish(Operator operator, Long pageId) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notNull(pageId, "落地页页面ID不可为空");
        RLock lock = mgkBaseService.getLock(pageId, MgkConstants.PAGE_LOCK_SUFFIX);
        try {
            landingPageServiceDelegate.publish(pageId);
        } finally {
            log.info(System.currentTimeMillis() + "---publish unLock----");
            lock.unlock();
        }
    }

    @Override
    public void manualPublish(Operator operator, Long pageId) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notNull(pageId, "落地页页面ID不可为空");
        RLock lock = mgkBaseService.getLock(pageId, MgkConstants.PAGE_LOCK_SUFFIX);
        try {
            landingPageServiceDelegate.manualPublish(operator, pageId);
        } finally {
            log.info(System.currentTimeMillis() + "---manual publish unLock----");
            lock.unlock();
        }
    }

    @Override
    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void sendAudit(Operator operator, Long pageId) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notNull(pageId, "落地页页面ID不可为空");
        RLock lock = mgkBaseService.getLock(pageId, MgkConstants.PAGE_LOCK_SUFFIX);
        MgkLandingPageDto landingPageDto = this.getLandingPageDtoByPageId(pageId);
        Assert.notNull(landingPageDto, "原始落地页不存在，送审失败");
        try {
            Long shadowPageId = mgkShadowLandingPageService.getShadowPageIdByPageId(pageId);
            landingPageServiceDelegate.sendAudit(operator, pageId, shadowPageId);
            mgkAuditPageService.sendAudit(pageId, operator.getOperatorId());
        } finally {
            log.info(System.currentTimeMillis() + "---sendAudit unLock----");
            lock.unlock();
        }
    }

    public void sendAuditByRefresh(List<Long> pageIdList) {
        for (Long pageId : pageIdList) {

            Operator operator = Operator.SYSTEM;
            Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
            Assert.notNull(pageId, "落地页页面ID不可为空");
            RLock lock = mgkBaseService.getLock(pageId, MgkConstants.PAGE_LOCK_SUFFIX);
            MgkLandingPageDto landingPageDto = this.getLandingPageDtoByPageId(pageId);
            Assert.notNull(landingPageDto, "原始落地页不存在，送审失败");
            try {
                Long shadowPageId = mgkShadowLandingPageService.getShadowPageIdByPageId(pageId);
                landingPageServiceDelegate.sendAudit(operator, pageId, shadowPageId);
                mgkAuditPageService.sendAudit(pageId, operator.getOperatorId());
            } finally {
                log.info(System.currentTimeMillis() + "---sendAudit unLock----");
                lock.unlock();
            }
        }
    }

    @Override
    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void auditReject(MgkAuditPageActDto auditDto) {
        Assert.isTrue(Utils.isPositive(auditDto.getPageId()), "落地页id不可为空");
        Assert.isTrue(!StringUtils.isEmpty(auditDto.getAuditorName()), "审核人名称不可为空");
        Assert.isTrue(!StringUtils.isEmpty(auditDto.getReason()), "审核拒绝理由不可为空");
        Long pageId = auditDto.getPageId();
        MgkLandingPageDto landingPageDto = this.getLandingPageDtoByPageId(pageId);
        Assert.notNull(landingPageDto, "落地页不存在,审核拒绝失败");
        Operator operator = Operator.SYSTEM;
        operator.setOperatorId(landingPageDto.getAccountId());
        Long shadowPageId = mgkShadowLandingPageService.getShadowPageIdByPageId(pageId);

        RLock lock = mgkBaseService.getLock(pageId, MgkConstants.PAGE_LOCK_SUFFIX);
        try {
            landingPageServiceDelegate.validateShadowVersion(shadowPageId, auditDto.getShadowVersion());
            mgkAuditPageService.auditReject(auditDto);
            // 原始落地页已发布 -> 拒审时 影子落地页要回写
            if (LandingPageStatusEnum.PUBLISHED.getCode().equals(landingPageDto.getStatus())) {
                landingPageServiceDelegate.shadowPageWriteBack(pageId, shadowPageId, false);
            }
            landingPageServiceDelegate.auditReject(operator, pageId, auditDto.getReason());
            QueryLandingPageGroupMappingDto queryPageGroupMappingDto = QueryLandingPageGroupMappingDto.builder()
                    .pageIdList(Lists.newArrayList(pageId))
                    .groupSource(PageGroupSourceEnum.MGK_SOURCE.getCode())
                    .build();
            List<LandingPageGroupMappingDto> mappingDtoList =
                    landingPageGroupMappingService.queryPageGroupMappingDto(queryPageGroupMappingDto);
            landingPageGroupService.refreshLandingPageGroupHasVideoPageAndTemplatePage(mappingDtoList);
            MgkPageAuditRecordInfoDto auditRecordInfoDto =
                    landingPageServiceDelegate.generatePageAuditRecordInfoDto(pageId, shadowPageId);
            MgkPageOperateLogDto logDto = MgkPageOperateLogDto.builder()
                    .pageId(landingPageDto.getPageId())
                    .operatorName(auditDto.getAuditorName())
                    .operatorType(OperatorType.OPERATING_PERSONNEL.getCode())
                    .operateType(MgkOperationType.AUDIT_REJECT.getCode())
                    .operatorId(landingPageDto.getAccountId())
                    .originStatusDesc(LandingPageStatusEnum.getByCode(landingPageDto.getStatus()).getDesc())
                    .newStatusDesc(LandingPageStatusEnum.AUDIT_REJECT.getDesc())
                    .reason(auditDto.getReason())
                    .build();
            mgkPageOperateLogService.addAuditLog(logDto, auditRecordInfoDto);
            // 最后拒审
            landingPageGroupService.updateLandingPageGroupStatusByUnavailableMapping(mappingDtoList, operator);
        } finally {
            log.info(System.currentTimeMillis() + "---auditReject unLock----");
            lock.unlock();
        }
    }

    @Override
    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void auditPass(MgkAuditPageActDto auditDto) {
        log.info("auditPass auditDto:{}", auditDto);
        Assert.isTrue(Utils.isPositive(auditDto.getPageId()), "落地页id不可为空");
        Assert.isTrue(!StringUtils.isEmpty(auditDto.getAuditorName()), "审核人名称不可为空");
        Long pageId = auditDto.getPageId();
        Cat.logEvent("mgk auditPass", auditDto.getPageId().toString());
        MgkLandingPageDto landingPageDto = this.getLandingPageDtoByPageId(pageId);
        Assert.notNull(landingPageDto, "落地页不存在,审核通过失败");
        Long shadowPageId = mgkShadowLandingPageService.getShadowPageIdByPageId(pageId);
        RLock lock = mgkBaseService.getLock(pageId, MgkConstants.PAGE_LOCK_SUFFIX);
        try {
            landingPageServiceDelegate.validateShadowVersion(shadowPageId, auditDto.getShadowVersion());
            mgkAuditPageService.auditPass(auditDto);
            landingPageServiceDelegate.auditPass(pageId, shadowPageId);
            MgkPageAuditRecordInfoDto auditRecordInfoDto =
                    landingPageServiceDelegate.generatePageAuditRecordInfoDto(pageId, shadowPageId);
            MgkPageOperateLogDto logDto = MgkPageOperateLogDto.builder()
                    .pageId(landingPageDto.getPageId())
                    .operatorName(auditDto.getAuditorName())
                    .operatorType(OperatorType.OPERATING_PERSONNEL.getCode())
                    .operateType(MgkOperationType.AUDIT_PASS.getCode())
                    .operatorId(landingPageDto.getAccountId())
                    .originStatusDesc(LandingPageStatusEnum.getByCode(landingPageDto.getStatus()).getDesc())
                    .newStatusDesc(LandingPageStatusEnum.PUBLISHED.getDesc())
                    .build();
            mgkPageOperateLogService.addAuditLog(logDto, auditRecordInfoDto);
            QueryLandingPageGroupMappingDto queryPageGroupMappingDto = QueryLandingPageGroupMappingDto.builder()
                    .pageIdList(Lists.newArrayList(pageId))
                    .groupSource(PageGroupSourceEnum.MGK_SOURCE.getCode())
                    .build();
            List<LandingPageGroupMappingDto> mappingDtoList =
                    landingPageGroupMappingService.queryPageGroupMappingDto(queryPageGroupMappingDto);
            landingPageGroupService.refreshLandingPageGroupHasVideoPageAndTemplatePage(mappingDtoList);
            //更新/新建私信链接
            if (LandingPageTypeEnum.BUSINESS_TOOL.getCode().equals(landingPageDto.getType())) {
                createUpdateMessageComponent(landingPageDto, shadowPageId);
            }
            // 非已发布落地页过审 通知投放端
            if (!LandingPageStatusEnum.PUBLISHED.getCode().equals(landingPageDto.getStatus())) {
                mgkGrpcManager.mgkPagePassCreativeAudit(pageId);
            }
        } finally {
            log.info(System.currentTimeMillis() + "---auditPass unLock----");
            lock.unlock();
        }
    }

    public void auditPassByRefresh(List<Long> pageIdList) {
        for (Long pageId : pageIdList) {
            MgkAuditPageActDto auditDto = new MgkAuditPageActDto();
            auditDto.setPageId(pageId);
            auditDto.setAuditorName("system");

            log.info("auditPass auditDto:{}", auditDto);
            Assert.isTrue(Utils.isPositive(auditDto.getPageId()), "落地页id不可为空");
            Assert.isTrue(!StringUtils.isEmpty(auditDto.getAuditorName()), "审核人名称不可为空");

            Cat.logEvent("mgk auditPass", auditDto.getPageId().toString());
            MgkLandingPageDto landingPageDto = this.getLandingPageDtoByPageId(pageId);
            Assert.notNull(landingPageDto, "落地页不存在,审核通过失败");
            Long shadowPageId = mgkShadowLandingPageService.getShadowPageIdByPageId(pageId);
            RLock lock = mgkBaseService.getLock(pageId, MgkConstants.PAGE_LOCK_SUFFIX);
            try {

                mgkAuditPageService.auditPass(auditDto);
                landingPageServiceDelegate.auditPass(pageId, shadowPageId);
                MgkPageAuditRecordInfoDto auditRecordInfoDto =
                        landingPageServiceDelegate.generatePageAuditRecordInfoDto(pageId, shadowPageId);
                MgkPageOperateLogDto logDto = MgkPageOperateLogDto.builder()
                        .pageId(landingPageDto.getPageId())
                        .operatorName(auditDto.getAuditorName())
                        .operatorType(OperatorType.OPERATING_PERSONNEL.getCode())
                        .operateType(MgkOperationType.AUDIT_PASS.getCode())
                        .operatorId(landingPageDto.getAccountId())
                        .originStatusDesc(LandingPageStatusEnum.getByCode(landingPageDto.getStatus()).getDesc())
                        .newStatusDesc(LandingPageStatusEnum.PUBLISHED.getDesc())
                        .build();
                mgkPageOperateLogService.addAuditLog(logDto, auditRecordInfoDto);
                QueryLandingPageGroupMappingDto queryPageGroupMappingDto = QueryLandingPageGroupMappingDto.builder()
                        .pageIdList(Lists.newArrayList(pageId))
                        .groupSource(PageGroupSourceEnum.MGK_SOURCE.getCode())
                        .build();
                List<LandingPageGroupMappingDto> mappingDtoList =
                        landingPageGroupMappingService.queryPageGroupMappingDto(queryPageGroupMappingDto);
                landingPageGroupService.refreshLandingPageGroupHasVideoPageAndTemplatePage(mappingDtoList);
                //更新/新建私信链接
                if (LandingPageTypeEnum.BUSINESS_TOOL.getCode().equals(landingPageDto.getType())) {
                    createUpdateMessageComponent(landingPageDto, shadowPageId);
                }
                // 非已发布落地页过审 通知投放端
                if (!LandingPageStatusEnum.PUBLISHED.getCode().equals(landingPageDto.getStatus())) {
                    mgkGrpcManager.mgkPagePassCreativeAudit(pageId);
                }
            } finally {
                log.info(System.currentTimeMillis() + "---auditPass unLock----");
                lock.unlock();
            }
        }
    }

    private void createUpdateMessageComponent(MgkLandingPageDto landingPageDto, Long shadowPageId) {
        log.info("landingPageDto:{}", landingPageDto);
        Integer messageComponentNum = mgkGrpcManager.queryMessageComponentNum(landingPageDto.getAccountId(), landingPageDto.getPageId());
        log.info("messageComponentNum:{}", messageComponentNum);
        if (messageComponentNum > 0) {
            updateMessageComponent(landingPageDto, shadowPageId);
        } else {
            createMessageComponent(landingPageDto);
            createMessageComponent(landingPageDto, (byte) 1);
        }
    }

    private void createMessageComponent(MgkLandingPageDto landingPageDto, Byte messageType) {
        CreateMessageComponentDto createMessageComponentDto = new CreateMessageComponentDto();
        createMessageComponentDto.setAccountId(landingPageDto.getAccountId().longValue());
        createMessageComponentDto.setMessageType(messageType);
        createMessageComponentDto.setName(landingPageDto.getName());
        createMessageComponentDto.setContent(landingPageDto.getTitle());
        createMessageComponentDto.setImageUrl(landingPageDto.getPageCover().contains("@") ? landingPageDto.getPageCover().split("@")[0] : landingPageDto.getPageCover());
        createMessageComponentDto.setRelatePageId(landingPageDto.getPageId());
        createMessageComponentDto.setIconUrl("https://i0.hdslb.com/bfs/mall/workorder-b/26/7d/267d78ee05926f1b621e38c77f2483d7.png");
        createMessageComponentDto.setJumpUrl(this.getLaunchUrl(WhetherEnum.NO.getCode(), landingPageDto));
        log.info("createMessageComponentDto:{}", createMessageComponentDto);
        mgkGrpcManager.createMessageComponent(createMessageComponentDto);
    }

    private void createMessageComponent(MgkLandingPageDto landingPageDto) {
        CreateMessageComponentDto createMessageComponentDto = new CreateMessageComponentDto();
        createMessageComponentDto.setAccountId(landingPageDto.getAccountId().longValue());
        createMessageComponentDto.setMessageType((byte) 3);
        createMessageComponentDto.setName(landingPageDto.getName());
        createMessageComponentDto.setContent(landingPageDto.getTitle());
        createMessageComponentDto.setImageUrl(landingPageDto.getPageCover().contains("@") ? landingPageDto.getPageCover().split("@")[0] : landingPageDto.getPageCover());
        createMessageComponentDto.setRelatePageId(landingPageDto.getPageId());
        createMessageComponentDto.setIconUrl("https://i0.hdslb.com/bfs/mall/workorder-b/26/7d/267d78ee05926f1b621e38c77f2483d7.png");
        createMessageComponentDto.setJumpUrl(this.getLaunchUrl(WhetherEnum.NO.getCode(), landingPageDto));
        log.info("createMessageComponentDto:{}", createMessageComponentDto);
        mgkGrpcManager.createMessageComponent(createMessageComponentDto);
    }

    private void updateMessageComponent(MgkLandingPageDto landingPageDto, Long shadowPageId) {
        MgkLandingPageDto shadowPage = this.getLandingPageDtoByPageId(shadowPageId);
        mgkGrpcManager.updateMessageComponentTitle(landingPageDto.getPageId(), shadowPage.getTitle());
    }

    @Override
    public void resetUnpublished(Operator operator, Long pageId) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notNull(pageId, "落地页页面ID不可为空");
        RLock lock = mgkBaseService.getLock(pageId, MgkConstants.PAGE_LOCK_SUFFIX);
        try {
            landingPageServiceDelegate.resetUnpublished(operator, pageId);
        } finally {
            log.info(System.currentTimeMillis() + "---resetUnpublished unLock----");
            lock.unlock();
        }
    }

    @Override
    public void batchDisable(Operator operator, List<Long> pageIds) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notEmpty(pageIds, "落地页页面ID不可为空");
        Assert.isTrue(pageIds.size() <= 10, "单次批量最多删除十个落地页");
        Set<RLock> allLock = pageIds.stream().map(pid -> mgkBaseService.getLock(pid, MgkConstants.PAGE_LOCK_SUFFIX)).collect(Collectors.toSet());
        try {
            landingPageServiceDelegate.batchDisable(operator, pageIds);
        } finally {
            allLock.forEach(RLock::unlock);
        }
    }

    @Override
    public void refreshMgkPageStatusByAppPackageId() {
        int start = 0;
        int limit = 50;
        Map<Integer, List<Long>> appPackageId2MgkPageIdMap = this.getAppPackageId2MgkPageIdMap(start, limit);
        while (!CollectionUtils.isEmpty(appPackageId2MgkPageIdMap)) {
            List<AppPackageDto> appPackageDtos = soaAppPackageService.query(QueryAppPackageDto.builder()
                    .ids(Lists.newArrayList(appPackageId2MgkPageIdMap.keySet()))
                    .status(AppPackageStatus.VALID.getCode())
                    .platformStatus(AppPackageStatus.VALID.getCode())
                    .build());
            List<Integer> appPackageIds = CollectionUtils.isEmpty(appPackageDtos) ?
                    Collections.emptyList() : appPackageDtos.stream().map(AppPackageDto::getId).collect(Collectors.toList());
            Map<Integer, List<Long>> finalAppPackageId2MgkPageIdMap = appPackageId2MgkPageIdMap;
            appPackageId2MgkPageIdMap.keySet().stream().filter(appId -> !appPackageIds.contains(appId)
                            && !CollectionUtils.isEmpty(finalAppPackageId2MgkPageIdMap.get(appId)))
                    .forEach(appId -> {
                        try {
                            List<Long> pageIds = finalAppPackageId2MgkPageIdMap.get(appId);
                            Set<RLock> allLock = pageIds.stream().map(pid -> mgkBaseService.getLock(pid,
                                    MgkConstants.PAGE_LOCK_SUFFIX)).collect(Collectors.toSet());
                            try {
                                landingPageServiceDelegate.downlineForAppPackageDel(Operator.SYSTEM, pageIds);
                            } finally {
                                allLock.forEach(RLock::unlock);
                            }
                        } catch (Exception e) {
                            log.error("downlineForAppPackageDel error " + ExceptionUtils.getSubStringMsg(e));
                        }
                    });
            start += limit;
            appPackageId2MgkPageIdMap = this.getAppPackageId2MgkPageIdMap(start, limit);
        }
    }


    public void refreshErrorPageConfigForApk() throws IOException {
        int start = 3977;
        int limit = 500;
        BufferedWriter bf = new BufferedWriter(new FileWriter("/data/logs/aa.txt"));
        Map<Integer, List<Long>> appPackageId2MgkPageIdMap = this.getAppPackageId2MgkPageIdMap(start, limit);
        MgkLandingPageConfigPoExample configPoExample = new MgkLandingPageConfigPoExample();
        while (!CollectionUtils.isEmpty(appPackageId2MgkPageIdMap)) {
            List<AppPackageDto> appPackageDtos = soaAppPackageService.query(QueryAppPackageDto.builder()
                    .ids(Lists.newArrayList(appPackageId2MgkPageIdMap.keySet()))
                    .status(AppPackageStatus.VALID.getCode())
                    .platformStatus(AppPackageStatus.VALID.getCode())
                    .build());
            Map<Integer, AppPackageDto> appPackageMap = CollectionUtils.isEmpty(appPackageDtos) ?
                    new HashMap<>() : appPackageDtos.stream().collect(Collectors.toMap(AppPackageDto::getId, Function.identity()));

//            // 编写正则表达式
//            String regFileName = "\"android\":\\{\"id\":"+id+",\"url\":\".*\",\"scheme\"";
//            // 匹配当前正则表达式
//            Matcher matcher = Pattern.compile(regFileName).matcher(url);

            Set<Integer> appIds = new HashSet<>();
            appPackageId2MgkPageIdMap.forEach((appPackageId, pageIdList) -> {
                if (appIds.contains(appPackageId)) {
                    return;
                }
                appIds.add(appPackageId);
                AppPackageDto packageDto = appPackageMap.get(appPackageId);
                if (packageDto == null || packageDto.getPlatform() != 2) {
                    return;
                }
                String apkUrl = packageDto.getUrl();
                configPoExample.clear();
                configPoExample.or().andPageIdIn(pageIdList);
                List<MgkLandingPageConfigPo> configPos = mgkLandingPageConfigDao.selectByExampleWithBLOBs(configPoExample);
                List<Long> excluede = Lists.newArrayList(611251895454146560L, 704000183731478528L);
                configPos.forEach(configPo -> {
                    if (excluede.contains(configPo.getPageId())) {
                        return;
                    }
                    String config = configPo.getConfig();
                    Long pageId = configPo.getPageId();
                    if (!StringUtils.isEmpty(config) && !config.contains(apkUrl)) {
                        try {
                            // 编写正则表达式
                            String regFileName = "\"android\":\\{\"id\":" + packageDto.getId() + ",\"url\":\".*(.apk\",\"scheme\")+?";
                            // 匹配当前正则表达式
                            Matcher matcher = Pattern.compile(regFileName).matcher(config);
                            if (matcher.find()) {
                                String res = matcher.group();
                                String begin = "\"android\":{\"id\":" + packageDto.getId() + ",\"url\":\"";
                                String end = "\",\"scheme\"";
                                String[] a = res.split(end);
                                String item = a[0].replace(begin, "").replace(end, "");
                                String newApkUrl = apkUrl.trim();
                                MgkLandingPagePo pagePo = landingPageServiceDelegate.getByPageId(pageId);
                                updatePageConfig(Operator.builder().operatorId(pagePo.getAccountId())
                                                .operatorType(OperatorType.SYSTEM).operatorName("system").build(),
                                        pageId, config.replace(item, newApkUrl));
                                bf.write(configPo.getPageId() + "," + packageDto.getId() + "," + apkUrl + "," + item + "\n");
                            }
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                });
            });
            start += limit;
            appPackageId2MgkPageIdMap = this.getAppPackageId2MgkPageIdMap(start, limit);
        }
        bf.close();
    }

//    public static void main(String[] args) {
//        String url=
//            "{\"name\":\"校花老婆她又软又甜-品牌空间链接\",\"title\":\"书名：校花老婆她又软又甜\",\"header\":1,\"page_bg_color\":\"\",\"page_bg_url\":\"\",\"blocks\":[{\"id\":\"2d157db1-92a7-41d3-b957-46d9217718c3\",\"name\":\"fixed-block\",\"active\":false,\"components\":[{\"id\":\"324211db-d1d4-4ec5-933c-e6fd3cec6b80\",\"name\":\"button-download-large-fixed-bottom\",\"active\":false,\"data\":{\"content\":{\"text\":\"立即下载 继续阅读全文\",\"link\":{\"type\":\"downloadEvent\",\"behavior\":{\"url\":\"\",\"scheme\":\"\",\"formId\":\"\",\"form\":{\"useTitle\":1,\"title\":\"\",\"successMessage\":\"提交成功！\"},\"ios\":{\"id\":\"\",\"url\":\"\",\"scheme\":\"\"},\"android\":{\"id\":31316,\"url\":\"\\t http://media9.tadu.com/downloads/android/mianfeixiaoshuoyuedu_803778.apk\",\"scheme\":\"\"}}}},\"style\":{\"color\":\"#ffffff\",\"fontSize\":16,\"backgroundColor\":\"rgba(230, 57, 57, 1)\",\"borderColor\":\"rgba(230, 57, 57, 1)\",\"borderWidth\":1,\"borderRadius\":4,\"animation\":\"breath\"},\"layout\":{\"resizeWidth\":1,\"resizeHeight\":1,\"aspectRatio\":0,\"left\":15,\"top\":13,\"width\":345,\"height\":44,\"rotate\":0}},\"fixed\":\"bottom\",\"position\":false,\"hasError\":false}],\"data\":{\"bg\":\"#ffffff\",\"height\":200},\"hasError\":false},{\"id\":\"239a1f13-1f3a-4be6-ad09-8b8710566da9\",\"name\":\"block\",\"active\":false,\"components\":[{\"id\":\"05a323d0-579a-42cc-adad-f14a548b4807\",\"name\":\"plain-text-normal\",\"active\":false,\"data\":{\"content\":{\"content\":\"第1章 开局和漂亮校花学妹结婚！\"},\"style\":{\"fontName\":\"normal\",\"color\":\"#333\",\"fontSize\":20,\"letterSpacing\":0,\"lineHeight\":28,\"fontWeight\":\"bolder\",\"fontStyle\":\"normal\",\"textDecoration\":\"none\",\"textAlign\":\"center\"},\"layout\":{\"resizeWidth\":1,\"resizeHeight\":1,\"aspectRatio\":0,\"left\":0,\"top\":235,\"width\":375,\"height\":28,\"rotate\":0}},\"fixed\":\"\",\"position\":true,\"hasError\":false},{\"id\":\"6565d74a-a32c-4069-8f4b-d06b4baf4057\",\"name\":\"image-normal\",\"active\":false,\"data\":{\"content\":{\"image\":\"https://i0.hdslb.com/bfs/sycp/mgk/img/202107/d0587cf44ea6727ee1839ebfed4af4ea.gif@750w_300h.webp\",\"link\":{\"type\":\"linkEvent\",\"behavior\":{\"url\":\"\",\"scheme\":\"\",\"formId\":\"\",\"form\":{\"useTitle\":1,\"title\":\"\",\"successMessage\":\"提交成功！\"},\"ios\":{\"id\":\"\",\"url\":\"\",\"scheme\":\"\"},\"android\":{\"id\":\"\",\"url\":\"\",\"scheme\":\"\"}}}},\"layout\":{\"resizeWidth\":1,\"resizeHeight\":1,\"aspectRatio\":1,\"left\":0,\"top\":2489,\"width\":372,\"height\":148,\"rotate\":0}},\"fixed\":\"\",\"position\":true,\"hasError\":false},{\"id\":\"ead8c5f6-d761-496b-8470-fc60f4a5b81e\",\"name\":\"button-download-large\",\"active\":false,\"data\":{\"content\":{\"text\":\"点击立即下载免费看全篇\",\"link\":{\"type\":\"downloadEvent\",\"behavior\":{\"url\":\"\",\"scheme\":\"\",\"formId\":\"\",\"form\":{\"useTitle\":1,\"title\":\"\",\"successMessage\":\"提交成功！\"},\"ios\":{\"id\":\"\",\"url\":\"\",\"scheme\":\"\"},\"android\":{\"id\":31316,\"url\":\"\\t http://media9.tadu.com/downloads/android/mianfeixiaoshuoyuedu_803778.apk\",\"scheme\":\"\"}}}},\"style\":{\"color\":\"#ffffff\",\"fontSize\":16,\"backgroundColor\":\"rgba(230, 57, 57, 1)\",\"borderColor\":\"rgba(230, 57, 57, 1)\",\"borderWidth\":1,\"borderRadius\":4,\"animation\":\"jump\"},\"layout\":{\"resizeWidth\":1,\"resizeHeight\":1,\"aspectRatio\":0,\"left\":15,\"top\":2603,\"width\":345,\"height\":43,\"rotate\":0}},\"fixed\":\"\",\"position\":true,\"hasError\":false},{\"id\":\"ac95927d-da10-48d8-b01f-e78b7e54a8de\",\"name\":\"button-download-image\",\"active\":false,\"data\":{\"content\":{\"image\":\"https://i0.hdslb.com/bfs/sycp/mgk/img/202107/04bd1b9f753fa324d89571f6100d96f9.jpg@640w_400h.webp\",\"link\":{\"type\":\"downloadEvent\",\"behavior\":{\"url\":\"\",\"scheme\":\"\",\"formId\":\"\",\"form\":{\"useTitle\":1,\"title\":\"\",\"successMessage\":\"提交成功！\"},\"ios\":{\"id\":\"\",\"url\":\"\",\"scheme\":\"\"},\"android\":{\"id\":31316,\"url\":\"\\t http://media9.tadu.com/downloads/android/mianfeixiaoshuoyuedu_803778.apk\",\"scheme\":\"\"}}}},\"layout\":{\"resizeWidth\":1,\"resizeHeight\":1,\"aspectRatio\":1,\"left\":0,\"top\":0,\"width\":375,\"height\":235,\"rotate\":0}},\"fixed\":\"\",\"position\":true,\"hasError\":false},{\"id\":\"1d1efd86-38c9-4c8d-87f9-d493ef0b6b6b\",\"name\":\"button-download-image\",\"active\":false,\"data\":{\"content\":{\"image\":\"https://i0.hdslb.com/bfs/sycp/mgk/img/202107/459cbea77d7bdc71f7c5c94a040ce339.jpg@749w_4433h.webp\",\"link\":{\"type\":\"downloadEvent\",\"behavior\":{\"url\":\"\",\"scheme\":\"\",\"formId\":\"\",\"form\":{\"useTitle\":1,\"title\":\"\",\"successMessage\":\"提交成功！\"},\"ios\":{\"id\":\"\",\"url\":\"\",\"scheme\":\"\"},\"android\":{\"id\":31316,\"url\":\"\\t http://media9.tadu.com/downloads/android/mianfeixiaoshuoyuedu_803778.apk\",\"scheme\":\"\"}}}},\"layout\":{\"resizeWidth\":1,\"resizeHeight\":1,\"aspectRatio\":1,\"left\":0,\"top\":269,\"width\":375,\"height\":2220,\"rotate\":0}},\"fixed\":\"\",\"position\":true,\"hasError\":false}],\"data\":{\"bg\":\"rgba(250, 244, 225, 1)\",\"height\":2646},\"hasError\":false}],\"page_id\":null}";
//                   // 编写正则表达式
//        String regFileName = "(\"android\":\\{\"id\":"+31316+",\"url\":\".*(.apk\",\"scheme\")+?)+?";
//        // 匹配当前正则表达式
//        Matcher matcher = Pattern.compile(regFileName).matcher(url);
//        if(matcher.find()){
//            String res = matcher.group();
//            String begin = "\"android\":{\"id\":"+31316+",\"url\":\"";
//            String end = "\",\"scheme\"";
//            String[] a = res.split(end);
//            res = a[0].replace(begin, "").replace(end, "");
//            System.out.println(res);
//        }
//
//    }

    @Override
    public void downlineMgkPageByAppPackageId(Operator operator, Integer appPackageId) {
        Assert.notNull(appPackageId, "APPID不可为空");
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不能为空");

        Map<Integer, List<Long>> map = landingPageServiceDelegate.getAppPackageId2MgkPageIdMapInAppIds(Lists.newArrayList(appPackageId));
        if (!CollectionUtils.isEmpty(map)) {
            List<Long> pageIds = map.getOrDefault(appPackageId, Collections.emptyList());
            if (!CollectionUtils.isEmpty(pageIds)) {
                Set<RLock> allLock = pageIds.stream().map(pid -> mgkBaseService.getLock(pid, MgkConstants.PAGE_LOCK_SUFFIX)).collect(Collectors.toSet());
                try {
                    landingPageServiceDelegate.downlineForAppPackageDel(operator, pageIds);
                } finally {
                    allLock.forEach(RLock::unlock);
                }
            }
        }

    }

    @Override
    public void manualRejectMgkCreative(List<Long> mgkPageIds) {
        landingPageServiceDelegate.manualRejectMgkCreative(mgkPageIds);
    }

    @Override
    public Map<Integer, List<Long>> getAppPackageId2MgkPageIdMap(int start, int limit) {
        return landingPageServiceDelegate.getValidBindedAppPackageId2MgkPageIdMap(start, limit);
    }

    @Override
    public LandingPageConfigDto getLandingPageEditConfigDtoByOriginPageId(Long pageId) {
        return landingPageServiceDelegate.getLandingPageEditConfigDtoByOriginPageId(pageId);
    }

    @Override
    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void downline(Operator operator, long pageId) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notNull(pageId, "落地页页面ID不可为空");
        RLock lock = mgkBaseService.getLock(pageId, MgkConstants.PAGE_LOCK_SUFFIX);
        try {
            // 取消原落地页审核任务 这里是原始落地页pageId
            mgkAuditPageService.cancelAudit(pageId, "落地页下线");
            landingPageServiceDelegate.downline(operator, pageId);
        } finally {
            log.info(System.currentTimeMillis() + "---downline unLock----");
            lock.unlock();
        }
    }

    @Override
    public List<Long> batchDownlineByMiniGameId(Operator operator, Integer miniGameId) {
        List<Long> pageIds = landingPageServiceDelegate.getPageIdsByMiniGameId(miniGameId);
        if (!CollectionUtils.isEmpty(pageIds)) {
            MgkLandingPageServiceImpl serviceProxy = (MgkLandingPageServiceImpl) AopContext.currentProxy();
            return serviceProxy.batchDownlineWithReason(operator, pageIds, "微信小游戏下线");
        }
        return Collections.emptyList();
    }

    @Override
    public List<Long> batchDownline(Operator operator, List<Long> pageIds) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notEmpty(pageIds, "落地页页面ID不可为空");
        MgkLandingPageServiceImpl serviceProxy = (MgkLandingPageServiceImpl) AopContext.currentProxy();
        return serviceProxy.batchDownlineWithReason(operator, pageIds, "落地页下线");
    }

    @Override
    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public List<Long> batchDownlineWithReason(Operator operator, List<Long> pageIds, String reason) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notEmpty(pageIds, "落地页页面ID不可为空");
        List<Long> result;
        Set<RLock> allLock = pageIds.stream().map(pid -> mgkBaseService.getLock(pid, MgkConstants.PAGE_LOCK_SUFFIX)).collect(Collectors.toSet());
        try {
            mgkAuditPageService.cancelAudit(pageIds, reason);
            result = landingPageServiceDelegate.batchDownline(operator, pageIds, reason);
        } finally {
            allLock.forEach(RLock::unlock);
        }
        return result;
    }


    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public List<Long> batchDownlineWithReasonBackdoor(List<Long> pageIds, String reason) {
        Operator operator = Operator.SYSTEM;
        operator.setOperatorName("系统");
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notEmpty(pageIds, "落地页页面ID不可为空");
        List<Long> result = new ArrayList<>();
        Set<RLock> allLock = pageIds.stream().map(pid -> mgkBaseService.getLock(pid, MgkConstants.PAGE_LOCK_SUFFIX)).collect(Collectors.toSet());
        try {
            mgkAuditPageService.cancelAudit(pageIds, reason);
            result = landingPageServiceDelegate.batchDownline(operator, pageIds, reason);
        } finally {
            allLock.forEach(RLock::unlock);
        }
        return result;
    }

    @Override
    public void refreshCDN(Operator operator, long pageId) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        landingPageServiceDelegate.refreshCDN(pageId);
    }

    @Override
    public void refreshLandingPageInRedis() {
        int startId = 0;
        int max = getMgkLandingPageTableMaxId();
        // 账号唤起白名单
        Map<Integer, List<String>> accountAwakenWhiteListMap = landingPageServiceDelegate
                .getAllAwakenWhiteListGroupByAccountId();
        log.info("refreshLandingPageInRedis begin");

        while (startId <= max) {
            try {
                List<MgkLandingPagePo> pagePos = landingPageServiceDelegate
                        .getRefreshLandingPagePosByPage(startId, REFRESH_PAGE_LIMIT);

                landingPageServiceDelegate.refreshLandingPageInRedis(pagePos, accountAwakenWhiteListMap);
            } catch (Exception e) {
                log.error("refreshLandingPageInRedis error [{}]", ExceptionUtils.getSubStringMsg(e));
            }
            startId += REFRESH_PAGE_LIMIT;
        }
    }

    @Override
    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void reject(Operator operator, long pageId, String reason) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        RLock lock = mgkBaseService.getLock(pageId, MgkConstants.PAGE_LOCK_SUFFIX);
        try {
            mgkAuditPageService.cancelAudit(Lists.newArrayList(pageId), "管理员驳回");
            landingPageServiceDelegate.reject(operator, pageId, reason);
        } finally {
            log.info(System.currentTimeMillis() + "---reject unLock----");
            lock.unlock();
        }
    }

    @Override
    public LandingPageConfigDto getLandingPageConfigDtoByPageId(Long pageId) {
        return landingPageServiceDelegate.getLandingPageConfigDtoByPageId(pageId);
    }

    @Override
    public PageConfig getLandingPageConfigForMobByPageId(Long pageId) {
        return landingPageServiceDelegate.getLandingPageConfigForMobByPageId(pageId);
    }

    @Override
    public PageConfig getLandingPageConfigForPreviewMobByPageId(Long pageId, Long shadowPageId, Integer previewMode) {
        return landingPageServiceDelegate.getLandingPageConfigForPreviewMobByPageId(pageId, shadowPageId, previewMode);
    }

    @Override
    public Integer getAccountIdByPageId(Long pageId) {
        return landingPageServiceDelegate.getAccountIdByPageId(pageId);
    }

    @Override
    public MgkLandingPageDto getLandingPageDtoByPageId(Long pageId) {
        return landingPageServiceDelegate.getLandingPageDtoByPageId(pageId);
    }

    @Override
    public MgkLandingPageDto getLandingPageDtoByPageIdWithCache(Long pageId) {
        try {
            MgkLandingPageDto mgkLandingPageDto = cache.get(pageId);
            if (null != mgkLandingPageDto) {
                return mgkLandingPageDto;
            }
        } catch (ExecutionException e) {
            log.error("cache load error ", e);
        }

        return landingPageServiceDelegate.getLandingPageDtoByPageId(pageId);
    }

    /**
     * 包括待审页面
     */
    @Override
    public MgkLandingPageBean validatePageIdAndGetPage(Long pageId) {
        MgkLandingPageDto landingPageDto = this.getLandingPageDtoByPageId(pageId);
        Assert.isTrue(LandingPageStatusEnum.LAUNCH_AVAILABLE_STATUS_LIST.contains(landingPageDto.getStatus()),
                "非发布或待审核状态的页面不可投放");
        Integer adVersionControllId = this.getAdVersionControlIdByPageId(pageId);
        String launchUrl = this.getLaunchUrl(WhetherEnum.NO.getCode(), landingPageDto);
        String launchUrlSecondary = this.getLaunchUrl(WhetherEnum.YES.getCode(), landingPageDto);
        return MgkLandingPageBean.builder()
                .mgkPageId(landingPageDto.getPageId())
                .launchUrl(launchUrl)
                .launchUrlSecondary(launchUrlSecondary)
                .adVersionControllId(adVersionControllId)
                .pageStatus(landingPageDto.getStatus())
                .templateStyle(landingPageDto.getTemplateStyle())
                .pageType(landingPageDto.getType())
                .build();
    }

    @Override
    public List<MgkLandingPageBean> validateAndGetPageList(List<Long> pageIdList) {
        if (CollectionUtils.isEmpty(pageIdList)) {
            return Collections.emptyList();
        }

        List<MgkLandingPageBean> pageBeanList = landingPageServiceDelegate.getPageBaseInfoList(pageIdList);
        Assert.isTrue(pageBeanList.stream()
                        .allMatch(pageBean -> LandingPageStatusEnum.LAUNCH_AVAILABLE_STATUS_LIST.contains(pageBean.getPageStatus())),
                "非发布或待审核状态的页面不可投放");
        return pageBeanList;
    }


    @Override
    public List<Long> getPublishedPageIds(List<Long> pageIds) {
        return landingPageServiceDelegate.getPublishedPageIds(pageIds);
    }

    @Override
    public List<Long> getLaunchAblePageIds(List<Long> pageIds) {
        return landingPageServiceDelegate.getLaunchAblePageIds(pageIds);
    }

    @Override
    public List<MgkLandingPageDto> getLandingPageDtosInPageIds(List<Long> pageIds) {
        return landingPageServiceDelegate.getLandingPageDtosInPageIds(pageIds);
    }

    @Override
    public Map<Long, MgkLandingPageDto> getLandingPageMapInPageIds(List<Long> pageIds) {
        return landingPageServiceDelegate.getLandingPageMapInPageIds(pageIds);
    }

    @Override
    public List<MgkLandingPageDto> getLandingPageDtos(QueryLandingPageParamDto queryLandingPageParamDto) {
        return landingPageServiceDelegate.getLandingPageDtos(queryLandingPageParamDto);
    }

    @Override
    public List<MgkLandingPageStatusBaseDto> getLandingPageStatusBaseDtoList(QueryLandingPageParamDto queryLandingPageParamDto) {
        return landingPageServiceDelegate.getLandingPageStatusBaseDtoList(queryLandingPageParamDto);
    }

    @Override
    public PageResult<MgkLandingPageDto> getLandingPageDtos(QueryLandingPageParamDto queryLandingPageParamDto, Page page) {
        return landingPageServiceDelegate.getLandingPageDtos(queryLandingPageParamDto, page);
    }

    @Override
    public PageResult<MgkLandingPageDto> getPreviewLandingPageDtos(QueryLandingPageParamDto queryLandingPageParamDto, Page page) {
        return landingPageServiceDelegate.getPreviewLandingPageDtos(queryLandingPageParamDto, page);
    }

    @Override
    public List<MgkLandingPageDto> getLaunchLandingPageDtos(QueryLandingPageParamDto queryLandingPageParamDto) {
        return landingPageServiceDelegate.getLaunchLandingPageDtos(queryLandingPageParamDto);
    }

    @Override
    public PageResult<MgkLandingPageDto> getLaunchLandingPageDtoByPage(QueryLandingPageParamDto queryLandingPageParamDto, Page page) {
        return landingPageServiceDelegate.getLaunchLandingPageDtoByPage(queryLandingPageParamDto, page);
    }

    @Override
    public PageResult<MgkLandingPageWithFormDto> queryLandingPageWithForm(QueryLandingPageWithFormParamDto queryDto, Page page) {
        return MgkCatUtils.newTransactionAndReturn("查询有表单落地页列表==", "查询有表单落地页列表",
                transaction -> landingPageWithMacroParamQuerier.queryLandingPageWithForm(queryDto, page));
    }

    @Override
    public PageResult<MgkLandingPageWithMacroParamDto> queryLandingPageWithMacroParam(QueryMgkLandingPageWithMacroParamDto queryDto, Page page) {
        return MgkCatUtils.newTransactionAndReturn("查询带转化参数的落地页列表==", "查询带转化参数的落地页列表",
                transaction -> landingPageWithMacroParamQuerier.queryLandingPageWithMacroParam(queryDto, page));
    }

    @Override
    public Map<Long, String> getLandingPageId2NameMap(List<Long> pageIds) {
        List<MgkLandingPageDto> landingPageDtos = landingPageServiceDelegate.getLandingPageDtosInPageIds(pageIds);
        return landingPageDtos.stream().collect(Collectors.toMap(MgkLandingPageDto::getPageId, MgkLandingPageDto::getName));
    }

    @Override
    public long getAssociatePageCountByFormId(Long formId) {
        return landingPageServiceDelegate.getAssociatePageCountByFormId(formId);
    }

    @Override
    public long getAssociateCustomNativePageCountByFormId(Long formId) {
        return landingPageServiceDelegate.getAssociateCustomNativePageCountByFormId(formId);
    }

    @Override
    public void refreshMgkPageStatusByAvId() {
        Map<Long, List<Long>> videoId2MgkPageIdMap = landingPageServiceDelegate.getVideoId2MgkPageIdMap();
        if (CollectionUtils.isEmpty(videoId2MgkPageIdMap)) {
            return;
        }
        CollectionHelper.processInBatches(videoId2MgkPageIdMap.keySet(), 50, vids -> {
            try {
                Map<Long, ArchiveDetail> videoId2ArchiveMap = archiveManager.getArchivesByAids(Lists.newArrayList(vids));

                List<Long> needOfflinePageIds = vids.stream().filter(vid -> vid > 0).filter(vid -> {
                    if (videoId2ArchiveMap.containsKey(vid)) {
                        ArchiveDetail archiveDetail = videoId2ArchiveMap.get(vid);
                        return archiveDetail == null
                                || archiveDetail.getArchive() == null
                                || archiveDetail.getArchive().getState() == null
                                || !ArchiveStateEnum.validVideoStatus.contains(archiveDetail.getArchive().getState());
                    }
                    return true;
                }).map(videoId2MgkPageIdMap::get).flatMap(Collection::stream).distinct().collect(Collectors.toList());

                log.info("possibleNeedOfflinePageIds size: [{}]", needOfflinePageIds.size());

                CollectionHelper.processInBatches(needOfflinePageIds, 50, pids -> {
                    Set<RLock> allLock = pids.stream().map(pid -> mgkBaseService.getLock(pid, MgkConstants.PAGE_LOCK_SUFFIX)).collect(Collectors.toSet());
                    try {
                        MgkLandingPageServiceImpl serviceProxy = (MgkLandingPageServiceImpl) AopContext.currentProxy();
                        List<Long> canDownlinePageIds = serviceProxy.batchDownlineWithReason(Operator.SYSTEM, pids, "落地页中视频不可播放");
                        log.info("refreshMgkPageStatusByAvId alreadyDownloadPageIds：[{}]", canDownlinePageIds);
                    } finally {
                        allLock.forEach(RLock::unlock);
                    }
                });
            } catch (Exception e) {
                log.error("refreshMgkPageStatusByAvId videoId: [{}], failed", vids, e);
            }
        });

    }

    @Override
    public String getWeChatSign() {
        return landingPageServiceDelegate.getWeChatSign();
    }

    @Override
    public Map<Long, Integer> getTemplateStyleMapByPageId(List<Long> pageIds) {
        return landingPageServiceDelegate.getTemplateStyleMapByPageId(pageIds);
    }

    @Override
    public List<MgkLandingPageDto> getLandingPageDtoByPageIds(List<Long> pageIds) {
        return landingPageServiceDelegate.getLandingPageDtoByPageIds(pageIds);
    }

    @Override
    public Integer getAdVersionControlIdByPageId(Long pageId) {
        return landingPageServiceDelegate.getAdVersionControlIdByPageId(pageId);
    }

    @Override
    public String getNativeLaunchUrlByPageId(Long pageId) {
        return landingPageServiceDelegate.getNativeLaunchUrlByPageId(pageId);
    }

    @Override
    public List<AppInfoDto> getAppInfo(List<Integer> appIds) {
        return landingPageServiceDelegate.getAppInfo(appIds);
    }

    @Override
    public Map<Long, String> getJumpUrlByPageIds(List<Long> pageIdList) {
        if (CollectionUtils.isEmpty(pageIdList)) {
            return Collections.emptyMap();
        }

        List<MgkLandingPageDto> pageDtoList = landingPageServiceDelegate.getValidLandingPageByIds(pageIdList);
        if (CollectionUtils.isEmpty(pageDtoList)) {
            return Collections.emptyMap();
        }

        return pageDtoList.stream().filter(pageDto -> LandingPageTypeEnum.H5.getCode().equals(pageDto.getType())
                        || LandingPageTypeEnum.NATIVE.getCode().equals(pageDto.getType()))
                .collect(Collectors.toMap(MgkLandingPageDto::getPageId,
                        pageDto -> LandingPageTypeEnum.H5.getLaunchUrl(pageDto.getPageId(), null)));
    }

    @Override
    public String getLaunchUrl(Integer isPc, MgkLandingPageDto landingPageDto) {
        return landingPageUrlProc.getLaunchUrl(isPc, landingPageDto, false);
    }

    @Override
    public String getLaunchUrl(Integer isPc, LaunchUrlPageDto pageDto, boolean forceProdEnv) {
        return landingPageUrlProc.getLaunchUrl(isPc, pageDto, forceProdEnv);
    }

    @Override
    public Map<Integer, String> getPlatformLaunchUrlWithParams(Long pageId) {
        return landingPageUrlProc.getPlatformLaunchUrlWithParams(pageId);
    }

    @Override
    public LandingPageConfigDto transPage(Integer accountId, String pageId, Integer strict) throws ServiceException {
        return landingPageServiceDelegate.transPage(accountId, pageId, strict);
    }

    @Override
    public List<MgkLandingPageDto> getPageSLimit(int start, int limit) {
        return landingPageServiceDelegate.getPageLimit(start, limit);
    }

    @Override
    public List<Long> getFormIdsByPageIds(List<Long> pageIds) {
        return landingPageServiceDelegate.getFormIdsByPageIds(pageIds);
    }

    @Override
    public PageResult<MgkHotLandingPageDto> getHotLandingPageList(QueryHotPageListDto queryHotPageListDto) {
        Assert.isTrue(Utils.isPositive(queryHotPageListDto.getOrderBy()), "排序字段不可为空");
        MgkHotPageOrderByEnum.getByCode(queryHotPageListDto.getOrderBy());
        Assert.isTrue(Utils.isPositive(queryHotPageListDto.getDataPeriod()), "日期筛选不可为空");
        MgkHotPageDatePeriodEnum.getByCode(queryHotPageListDto.getDataPeriod());
        Assert.isTrue(!Objects.isNull(queryHotPageListDto.getPage()), "分页参数不可为空");
        Assert.isTrue(Utils.isPositive(queryHotPageListDto.getPage().getPage()), "页码不可为空");
        Assert.isTrue(Utils.isPositive(queryHotPageListDto.getPage().getPageSize()), "分页大小不可为空");
        return landingPageServiceDelegate.getHotLandingPageList(queryHotPageListDto);
    }

    @Override
    public List<String> getHotLandingPagesCategories() {
        return landingPageServiceDelegate.getHotLandingPagesCategories();
    }

    @Override
    public List<MgkHotLandingPageDto> getHotLandingPageDetail(QueryHotPageListDto queryHotPageListDto) {
        Assert.isTrue(Utils.isPositive(queryHotPageListDto.getDataPeriod()), "日期筛选不可为空");
        MgkHotPageDatePeriodEnum.getByCode(queryHotPageListDto.getDataPeriod());
        Assert.isTrue(!CollectionUtils.isEmpty(queryHotPageListDto.getPageIds()), "落地页id不可为空");
        return landingPageServiceDelegate.getHotLandingPageDetail(queryHotPageListDto);
    }

    @Override
    public TemplatePageDto getMgkTemplatePage(Operator operator, Long pageId, Integer gameBaseId, Integer packageId, String url) {
        return landingPageServiceDelegate.getMgkTemplatePage(operator, pageId, gameBaseId, packageId, url);
    }

    @Override
    public List<SanlianMgkTemplatePageDto> getMgkTemplatePageList(QuerySanlianTemplatePageDto queryDto) {
        return landingPageServiceDelegate.getMgkTemplatePageList(queryDto);
    }

    @Override
    public List<TemplatePageDto> getMgkOnlineTemplatePage(List<Long> templatePageIds) {
        return landingPageServiceDelegate.getMgkOnlineTemplatePage(templatePageIds);
    }

    @Override
    public Map<Long, MgkLandingPageDto> getMgkAppletTemplatePageMapByPageIds(List<Long> pageIds) {
        return landingPageServiceDelegate.getMgkAppletTemplatePageMapByPageIds(pageIds);
    }

    @Override
    public Map<Long, List<Integer>> getAppPackageIdMapInPageIds(List<Long> pageIds) {
        return landingPageServiceDelegate.getAppPackageIdMapInPageIds(pageIds);
    }

    @Override
    public void refreshArchiveLandingPageByAccountIds(List<Integer> accountIds) {
        landingPageServiceDelegate.refreshArchiveLandingPageByAccountIds(accountIds);
    }

    @Override
    public void refreshArchiveLandingPageByPageIds(List<Long> pageIds) {
        landingPageServiceDelegate.refreshArchiveLandingPageByPageIds(pageIds);
    }

    @Override
    public void rollbackArchiveLandingPageByAccountIds(List<Integer> accountIds) {
        landingPageServiceDelegate.rollbackArchiveLandingPageByAccountIds(accountIds);
    }

    @Override
    public void rollbackArchiveLandingPageByPageIds(List<Long> pageIds) {
        landingPageServiceDelegate.rollbackArchiveLandingPageByPageIds(pageIds);
    }

    @Override
    public Map<Long, Long> getMgkLandingTemplatePageIdMap(List<Long> templatePageIds) {
        return landingPageServiceDelegate.getMgkLandingTemplatePageIdMap(templatePageIds);
    }

    @Override
    public Map<Long, Long> getMgkLandingPageTemplateWithoutArcIdMap(List<Long> templatePageIds) {
        return landingPageServiceDelegate.getMgkLandingPageTemplateWithoutArcIdMap(templatePageIds);
    }

    @Override
    public void refreshTemplateWithoutArcByAccountIds(List<Integer> accountIds) {
        landingPageServiceDelegate.refreshAllTemplatePageIdsWithoutArcs(accountIds);
    }

    @Override
    public void refreshLandingPageDownloadComponentLimit(List<Integer> accountIds) {
        landingPageServiceDelegate.refreshLandingPageDownloadComponentLimit(accountIds);
    }

    @Override
    public String getColumn(String json, String col) {
        JSONArray array = JSON.parseArray(json);
        if (Objects.isNull(array)) {
            return "";
        }
        try {
            StringBuilder sb = new StringBuilder();
            for (Object item : array) {
                JSONObject block = item instanceof JSONObject ? (JSONObject) item : (JSONObject) toJSON(item);
                sb.append(block.getString(col)).append("\n");
            }
            return sb.toString();
        } catch (Exception e) {
            return "";
        }
    }

    @Override
    public PageResult<MgkLandingPageDto> getLandingPagePVCtrForNewVersion(QueryLandingPageParamDto paramDto, Page page) throws ServiceException {
        Assert.notNull(paramDto.getLaunchBeginDate(), "开始时间不能为空");
        Assert.notNull(paramDto.getLaunchEndDate(), "结束时间不能为空");
        Assert.isTrue(Utils.getDateSpace(Objects.requireNonNull(TimeUtil.isoStrToTimestamp(paramDto.getLaunchBeginDate())),
                        Objects.requireNonNull(TimeUtil.isoStrToTimestamp(paramDto.getLaunchEndDate()))) <= myNewLandingPageQueryLimitDays,
                "查询的时间范围不能超过" + myNewLandingPageQueryLimitDays + "天");

        return landingPageServiceDelegate.getLandingPagePVCtrForNewVersion(paramDto, page);
    }

    @Override
    public List<MgkLandingPageDto> getPagePvCtrAndLimitItems(QueryLandingPageParamDto paramDto) {
        return landingPageServiceDelegate.getPagePvCtrAndLimitItems(paramDto);
    }

    @Override
    public void updatePageConfig(Operator operator, Long pageId, String config) {
        log.info("updatePageConfig1 pageId [{}], config [{}]", pageId, config);
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.isTrue(Utils.isPositive(pageId), "落地页信息不可为空");

        RLock lock = mgkBaseService.getLock(pageId, MgkConstants.PAGE_LOCK_SUFFIX);
        try {
            MgkLandingPageConfigPoExample configPoExample = new MgkLandingPageConfigPoExample();
            configPoExample.or().andPageIdEqualTo(pageId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            List<MgkLandingPageConfigPo> configPos = mgkLandingPageConfigDao.selectByExample(configPoExample);
            if (CollectionUtils.isEmpty(configPos)) {
                return;
            }
            MgkLandingPageConfigPo configPo = configPos.get(0);
            String oldConfig = configPo.getConfig();
            configPo.setConfig(config);
            configPo.setMtime(new Timestamp(System.currentTimeMillis()));
            log.info("updatePageConfig configPo [{}]", configPo);
            mgkLandingPageConfigDao.updateByPrimaryKeyWithBLOBs(configPo);

            mgkLogService.insertLog(NewLogOperationDto.builder()
                    .accountId(operator.getOperatorId())
                    .objId(pageId)
                    .objFlag(LogObjFlagEnum.LANDING_PAGE.getCode())
                    .operateType(LogOperateTypeEnum.LANDING_PAGE_MODIFY.getCode())
                    .operatorUsername(operator.getOperatorName())
                    .operatorType(operator.getOperatorType().getCode())
                    .oldValue("oldConfig:" + oldConfig)
                    .newValue(configPo)
                    .build());

            landingPageServiceDelegate.refreshPageConfigAndFormItemToRedis(pageId);
        } finally {
            log.info(System.currentTimeMillis() + "---updatePageConfig unLock----");
            lock.unlock();
        }
    }

    @Override
    public Map<Long, MgkLandingPageDto> getLandingPageId2DtoMap(List<Long> pageIds) {
        List<MgkLandingPageDto> landingPageDtos = landingPageServiceDelegate.getLandingPageDtosInPageIds(pageIds);
        return landingPageDtos.stream().collect(Collectors.toMap(MgkLandingPageDto::getPageId, t -> t));
    }

    @Override
    public Map<Long, MgkLandingPageDto> getAllLandingPageId2DtoMapForInner(List<Long> pageIds) {
        List<MgkLandingPageDto> landingPageDtos = getLandingPageDtos(QueryLandingPageParamDto.builder()
                .isModelList(IsModelEnum.ALL_MODE).pageIdList(pageIds).build());
        return landingPageDtos.stream().collect(Collectors.toMap(MgkLandingPageDto::getPageId, t -> t));
    }

    @Override
    public int getMgkLandingPageTableMaxId() {
        MgkLandingPagePoExample example = new MgkLandingPagePoExample();
        example.or();
        example.setOrderByClause("id desc");
        example.setLimit(1);
        List<MgkLandingPagePo> landingPagePos = landingPageDao.selectByExample(example);
        return CollectionUtils.isEmpty(landingPagePos) ? 0 : landingPagePos.get(0).getId();
    }

    @Override
    public List<MgkLandingPageWithMacroParamDto> queryLimitLandingPageWithExtraParam(QueryPageWithExtraParamDto param) {
        return landingPageWithMacroParamQuerier.queryLimitLandingPageWithExtraParam(param);
    }

    @Override
    public void refreshMessageComponent(List<Integer> accountIds) {
        QueryLandingPageParamDto queryLandingPageParamDto = new QueryLandingPageParamDto();
        queryLandingPageParamDto.setAccountIdList(accountIds);
        queryLandingPageParamDto.setTypeList(Lists.newArrayList(LandingPageTypeEnum.BUSINESS_TOOL.getCode()));
        queryLandingPageParamDto.setStatusList(Lists.newArrayList(LandingPageStatusEnum.PUBLISHED.getCode()));
        List<MgkLandingPageDto> mgkLandingPageDtoList = this.getLandingPageDtos(queryLandingPageParamDto);
        for (MgkLandingPageDto mgkLandingPageDto : mgkLandingPageDtoList) {
            log.info("landingPageDto:{}", mgkLandingPageDto);
            Integer messageComponentNum = mgkGrpcManager.queryMessageComponentNum(mgkLandingPageDto.getAccountId(), mgkLandingPageDto.getPageId(), 1);
            log.info("messageComponentNum:{}", messageComponentNum);
            if (messageComponentNum <= 0) {
                createMessageComponent(mgkLandingPageDto, (byte) 1);
            }
        }
    }

    @Override
    public void copyToOtherAcc(Operator operator, List<Long> copySourcePageIds, List<Integer> toAccIds) {
        copyCheck(operator, toAccIds);
        for (Long sourcePageId : copySourcePageIds) {
            for (Integer toAccId : toAccIds) {
                copyToSingleAcc(operator, sourcePageId, toAccId);
            }
        }
    }

    @Override
    public List<LandingPageHotPointDto> landingPageHotPoint(LandingPageHotPointQueryDto queryDto) {

        QueryReq.Builder builder = QueryReq.newBuilder()
                .setOsHeader(api2940Header)
                .addResps("page_y")
                .addResps("page_x")
                .addResps("sum_click_cnt")
                .addResps("page_id")
                .addResps("account_id")
                .addResps("page_type")
                .addReqs(OperatorVo.newBuilder().setField("account_id").setOperator("=").addAllValues(Lists.newArrayList(String.valueOf(queryDto.getAccountId()))))
                .addReqs(OperatorVo.newBuilder().setField("page_id").setOperator("=").addAllValues(Lists.newArrayList(String.valueOf(queryDto.getPageId()))))
                .addReqs(OperatorVo.newBuilder().setField("log_date").setOperator(">=")
                        .addAllValues(Lists.newArrayList(DateTimeFormatter.ofPattern("yyyyMMdd").format(queryDto.getBeginTime()))))
                .addReqs(OperatorVo.newBuilder().setField("log_date").setOperator("<=")
                        .addAllValues(Lists.newArrayList(DateTimeFormatter.ofPattern("yyyyMMdd").format(queryDto.getEndTime()))))
                .addAllOrders(Lists.newArrayList("page_y desc"))
                .setPageReq(PageReq.newBuilder().setPage(1).setPageSize(50000).build());

        if (null != queryDto.getPageType()) {
            builder.addReqs(OperatorVo.newBuilder().setField("page_type").setOperator("=").addAllValues(Lists.newArrayList(String.valueOf(queryDto.getPageType()))));
        }

        QueryReq openApiReq = builder
                .build();

        QueryResp queryResp = oneServiceOpenApiManagerBlockingStub.query(openApiReq);

        return queryResp.getRowsList().stream().map(r -> LandingPageHotPointDto.builder()
                .accountId(Integer.parseInt(r.getValueOrDefault("account_id", "0")))
                .pageX(Double.parseDouble(r.getValueOrDefault("page_x", "0")))
                .pageY(Double.parseDouble(r.getValueOrDefault("page_y", "0")))
                .clickCnt(Long.parseLong(r.getValueOrDefault("sum_click_cnt", "0")))
                .pageType(Integer.parseInt(r.getValueOrDefault("page_type", "0")))
                .build()).collect(Collectors.toList());
    }

    public void copyToOtherAccByRefresh(List<Long> copySourcePageIds, List<Integer> toAccIds) {
        Operator operator = Operator.SYSTEM;
        copyCheck(operator, toAccIds);
        for (Long sourcePageId : copySourcePageIds) {
            for (Integer toAccId : toAccIds) {
                copyToSingleAcc(operator, sourcePageId, toAccId);
            }
        }
    }

    private void copyToSingleAcc(Operator operator, Long sourcePageId, Integer toAccId) {
        LandingPageConfigDto sourceLandingPageConfigDto = landingPageServiceDelegate.getLandingPageConfigDtoByPageId(sourcePageId);
        //落地页状态校验
        Assert.isTrue(LandingPageTypeEnum.APPLETS.getCode().equals(sourceLandingPageConfigDto.getType())
                || Objects.equals(LandingPageTypeEnum.BUSINESS_TOOL.getCode(), sourceLandingPageConfigDto.getType()), "不支持复制此类型落地页");
        //有获客链接
        Assert.isTrue(CollectionUtils.isEmpty(sourceLandingPageConfigDto.getCustomerAcquisitionLinkIds()), "获客链接组件暂不支持落地页推送");
        //游戏下载组件
        Assert.isTrue(CollectionUtils.isEmpty(sourceLandingPageConfigDto.getGames()), "游戏下载组件暂不支持落地页推送");

        //生成 insert config
        Operator toOperator = Operator.builder()
                .operatorId(toAccId)
                .operatorName(OperatorType.SYSTEM.getName())
                .operatorType(OperatorType.SYSTEM)
                .build();
        NewLandingPageDto newLandingPageDto = NewLandingPageDto.builder().build();
        newLandingPageDto.setAccountId(toAccId);
        newLandingPageDto.setCopySourceAccountId(operator.getOperatorId());
        newLandingPageDto.setConfig(sourceLandingPageConfigDto.getConfig());

        //查询 app  mgk_landing_page_app_package 没有的话 复制 复制后的新id生成config 替换 查三联接口
        appPackageCopy(operator, Lists.newArrayList(toAccId), sourceLandingPageConfigDto, newLandingPageDto);

        //查询 小游戏 mgk_page_mini_game_mapping  没有的话 复制  复制后的新id生成config 替换  查三联接口
        miniGameCopy(operator, Lists.newArrayList(toAccId), sourceLandingPageConfigDto, newLandingPageDto);

        //表单 mgk_form , mgk_form_item  表单item复制
        formCopy(toAccId, sourceLandingPageConfigDto, newLandingPageDto);
        //查询 微信小程序 mgk_page_wechat_package_mapping (mapping 应该是是自动写)   mgk_wechat_package 没有的话 复制 复制后的新id生成config 替换
        weChatPackageCopy(toAccId, sourceLandingPageConfigDto, newLandingPageDto);

        // mgk_landing_page_game  清除参数中的pageId
        pageDtoBuild(sourceLandingPageConfigDto, newLandingPageDto);

        //dto 替换
        //保存
        //原生 品牌不用生成shadow
        boolean noNeedAudit = LandingPageTypeEnum.NATIVE.getCode().equals(sourceLandingPageConfigDto.getType())
                || mgkGrpcManager.checkBrandAccount(toAccId);

        if (noNeedAudit) {
            long newPageId = create(toOperator, newLandingPageDto);
            log.info("newPageId {}", newPageId);
        } else {
            long withShadowNewPageId = createWithShadow(toOperator, newLandingPageDto);
            log.info("withShadowNewPageId {}", withShadowNewPageId);
        }
    }

    private void pageDtoBuild(LandingPageConfigDto sourceLandingPageConfigDto, NewLandingPageDto newLandingPageDto) {
        newLandingPageDto.setGames(sourceLandingPageConfigDto.getGames());
        // showUrls 、
        newLandingPageDto.setShowUrls(sourceLandingPageConfigDto.getShowUrls());
        // BizId 、
        newLandingPageDto.setBizIds(sourceLandingPageConfigDto.getBizIds());
        // landingPageAvids   清除参数中的pageId
        newLandingPageDto.setAvIds(sourceLandingPageConfigDto.getAvIds());
        // 获客链接（mgk_landing_page_customer_acquisition） mgk_work_wechat_customer_acquisition_mapping  清除参数中的pageId
        newLandingPageDto.setCustomerAcquisitionLinkIds(sourceLandingPageConfigDto.getCustomerAcquisitionLinkIds());
        // mgk_page_download_component_height   清除参数中的pageId

        newLandingPageDto.setIsModel(sourceLandingPageConfigDto.getIsModel());
        newLandingPageDto.setHasTransition(sourceLandingPageConfigDto.getHasTransition());
        newLandingPageDto.setHeader(sourceLandingPageConfigDto.getHeader());
        newLandingPageDto.setTemplateStyle(sourceLandingPageConfigDto.getTemplateStyle());
        newLandingPageDto.setType(sourceLandingPageConfigDto.getType());
        newLandingPageDto.setModelId(sourceLandingPageConfigDto.getModelId());
        newLandingPageDto.setEffectiveEndTime(sourceLandingPageConfigDto.getEffectiveEndTime());
        newLandingPageDto.setEffectiveStartTime(sourceLandingPageConfigDto.getEffectiveStartTime());
        newLandingPageDto.setHasDpaGoods(sourceLandingPageConfigDto.getHasDpaGoods());
        newLandingPageDto.setIsFormScroll(sourceLandingPageConfigDto.getIsFormScroll());
        newLandingPageDto.setIsGameFormReserve(sourceLandingPageConfigDto.getIsGameFormReserve());
        newLandingPageDto.setIsPc(sourceLandingPageConfigDto.getIsPc());
        newLandingPageDto.setMaxDownloadComponentSize(sourceLandingPageConfigDto.getMaxDownloadComponentSize());
        newLandingPageDto.setName(sourceLandingPageConfigDto.getName());
        newLandingPageDto.setPageCover(sourceLandingPageConfigDto.getPageCover());
        newLandingPageDto.setTitle(sourceLandingPageConfigDto.getTitle());
        newLandingPageDto.setPageVersion(sourceLandingPageConfigDto.getPageVersion());
        newLandingPageDto.setTotalBlockSize(sourceLandingPageConfigDto.getTotalBlockSize());
        newLandingPageDto.setTotalDownloadComponentSize(sourceLandingPageConfigDto.getTotalDownloadComponentSize());
        newLandingPageDto.setTotalFirstScreenDownloadComponentSize(sourceLandingPageConfigDto.getTotalFirstScreenDownloadComponentSize());
        newLandingPageDto.setIsVideoPage(sourceLandingPageConfigDto.getIsVideoPage());
    }

    private String appPackageCopy(Operator operator, List<Integer> toAccIds, LandingPageConfigDto sourceLandingPageConfigDto, NewLandingPageDto newLandingPageDto) {
        String pageDtoConfig = newLandingPageDto.getConfig();
        List<Integer> appPackageIds = sourceLandingPageConfigDto.getAppPackageIds();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(appPackageIds)) {
            CopyAppReplay copyAppReplay = appPackageServiceBlockingStub.withDeadlineAfter(3L, TimeUnit.SECONDS)
                    .copyAppPackage(CopyAppRequest.newBuilder()
                            .setSourceAccId(operator.getOperatorId())
                            .addAllIds(appPackageIds)
                            .addAllToAccountIds(toAccIds)
                            .build());
            //替换apppackage config
            log.info("copyAppReplay {}", copyAppReplay.getItemsList());
            List<Integer> copyAppPackageIds = copyAppReplay.getItemsList().stream().map(CopyAppItem::getId).distinct().collect(Collectors.toList());
            newLandingPageDto.setAppPackageIds(copyAppPackageIds);

            Assert.isTrue(org.apache.commons.collections4.CollectionUtils.isNotEmpty(copyAppPackageIds), "app package 复制出错!");

//            pageDtoConfig = pageDtoConfig.replace("\\\"download_whitelist\\\"\\:\\[(.*?)\\]", copyAppPackageIds.toString().replaceAll("\\[|]", ""));
            for (CopyAppItem copyAppItem : copyAppReplay.getItemsList()) {
                //todo
                pageDtoConfig = pageDtoConfig.replace("id\":" + copyAppItem.getSourceId(), "id\":" + copyAppItem.getId());
            }
        }
        newLandingPageDto.setConfig(pageDtoConfig);
        return pageDtoConfig;
    }

    private String miniGameCopy(Operator operator, List<Integer> toAccIds, LandingPageConfigDto sourceLandingPageConfigDto, NewLandingPageDto newLandingPageDto) {
        String pageDtoConfig = newLandingPageDto.getConfig();
        List<Integer> miniGameIds = sourceLandingPageConfigDto.getMiniGameIds();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(miniGameIds)) {
            CopyMiniGameReply copyMiniGameReply = miniGameServiceBlockingStub.withDeadlineAfter(3L, TimeUnit.SECONDS)
                    .copyMiniGame(CopyMiniGameRequest.newBuilder()
                            .setSourceAccId(operator.getOperatorId())
                            .addAllIds(miniGameIds)
                            .addAllToAccountIds(toAccIds)
                            .build());
            //miniGame config
            log.info("copyMiniGameReply {}", copyMiniGameReply.getItemsList());
            List<Integer> copyMiniGameIds = copyMiniGameReply.getItemsList().stream().map(CopyMiniGameItem::getId).distinct().collect(Collectors.toList());
            newLandingPageDto.setMiniGameIds(copyMiniGameIds);
            for (CopyMiniGameItem copyMiniGameItem : copyMiniGameReply.getItemsList()) {
//                Matcher matcher = Pattern.compile("wxData\":{(.*?),\"id\":(" + copyMiniGameItem.getSourceId() + ")},").matcher(pageDtoConfig);
//                if (matcher.group().length() > 0) {
//                    pageDtoConfig = pageDtoConfig.replace(matcher.group(), matcher.group().replace(String.valueOf(copyMiniGameItem.getSourceId()), String.valueOf(copyMiniGameItem.getId())));
//                }
                //todo
                pageDtoConfig = pageDtoConfig.replace("id\":" + copyMiniGameItem.getSourceId(), "id\":" + copyMiniGameItem.getId());
            }
        }
        newLandingPageDto.setConfig(pageDtoConfig);
        return pageDtoConfig;
    }

    private void weChatPackageCopy(Integer toAccId, LandingPageConfigDto sourceLandingPageConfigDto, NewLandingPageDto newLandingPageDto) {
        String pageDtoConfig = newLandingPageDto.getConfig();
        Integer sourceWechatPackageId = sourceLandingPageConfigDto.getWechatPackageId();
        if (!Utils.isPositive(sourceWechatPackageId)) {
            newLandingPageDto.setWechatPackageId(sourceWechatPackageId);
            return;
        }
        MgkWechatPackagePo sourceMgkWechatPackagePo = mgkWechatPackageDao.selectByPrimaryKey(sourceWechatPackageId);
        List<MgkWechatPackageAccountMappingPo> sourceAccountMappingByPackagePos = mgkWechatPackageServiceDelegate.getMgkWechatPackageAccountMappingByPackageIds(Lists.newArrayList(sourceWechatPackageId));

        List<Integer> sourceWeChatAccIds = sourceAccountMappingByPackagePos.stream().map(MgkWechatPackageAccountMappingPo::getWechatAccountId).distinct().collect(Collectors.toList());

        List<MgkWechatAccountPo> sourceWechatAccountListPos = queryWeChatAccList(WechatAccountQueryDto.builder()
                .ids(sourceWeChatAccIds)
                .build());
        List<Integer> toAccWeChatIds = new ArrayList<>();
        for (MgkWechatAccountPo sourceWechatAccountListPo : sourceWechatAccountListPos) {
            List<MgkWechatAccountPo> wechatAccountListPos = queryWeChatAccList(WechatAccountQueryDto.builder()
                    .wechatAccountEqual(sourceWechatAccountListPo.getWechatAccount())
                    .accountId(toAccId)
                    .build());
            Integer newWeChatAccId = 0;
            if (CollectionUtils.isEmpty(wechatAccountListPos)) {
                //create
                MgkWechatAccountPo mgkWechatAccountPo = MgkWechatAccountPo.builder()
                        .accountId(toAccId)
                        .type(sourceWechatAccountListPo.getType())
                        .wechatAccount(sourceWechatAccountListPo.getWechatAccount())
                        .wechatName(sourceWechatAccountListPo.getWechatName())
                        .info(sourceWechatAccountListPo.getInfo())
                        .build();
                mgkWechatAccountDao.insertSelective(mgkWechatAccountPo);
                newWeChatAccId = mgkWechatAccountPo.getId();
                toAccWeChatIds.add(newWeChatAccId);
            } else {
                toAccWeChatIds.add(wechatAccountListPos.get(0).getId());
            }
        }

        MgkWechatPackagePoExample toAccMgkWechatPackagePoExample = new MgkWechatPackagePoExample();
        toAccMgkWechatPackagePoExample.createCriteria()
                .andAccountIdEqualTo(toAccId)
                .andNameEqualTo(sourceMgkWechatPackagePo.getName())
                .andTypeEqualTo(sourceMgkWechatPackagePo.getType())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MgkWechatPackagePo> toAccMgkWechatPackagePos = mgkWechatPackageDao.selectByExample(toAccMgkWechatPackagePoExample);
        Integer newWeChatPackageId = null;
        if (CollectionUtils.isEmpty(toAccMgkWechatPackagePos)) {
            WechatPackageCreateDto wechatPackageCreateDto = new WechatPackageCreateDto();
            wechatPackageCreateDto.setName(sourceMgkWechatPackagePo.getName());
            wechatPackageCreateDto.setAccountId(toAccId);
            wechatPackageCreateDto.setType(sourceMgkWechatPackagePo.getType());
            wechatPackageCreateDto.setWechatAccountIds(toAccWeChatIds);
            newWeChatPackageId = mgkWechatPackageService.create(wechatPackageCreateDto);
            newLandingPageDto.setWechatPackageId(newWeChatPackageId);
        } else {
            newWeChatPackageId = toAccMgkWechatPackagePos.get(0).getId();
            newLandingPageDto.setWechatPackageId(toAccMgkWechatPackagePos.get(0).getId());
        }
        //微信包
        pageDtoConfig = pageDtoConfig.replace("packageId\":" + sourceMgkWechatPackagePo.getId(), "packageId\":" + newWeChatPackageId);
        newLandingPageDto.setConfig(pageDtoConfig);
    }

    public List<MgkWechatAccountPo> queryWeChatAccList(WechatAccountQueryDto queryDto) {
        MgkWechatAccountPoExample exm = this.buildExampleByQueryDto(queryDto);
        return mgkWechatAccountDao.selectByExample(exm);
    }

    public MgkWechatAccountPoExample buildExampleByQueryDto(WechatAccountQueryDto queryDto) {
        MgkWechatAccountPoExample exm = new MgkWechatAccountPoExample();
        MgkWechatAccountPoExample.Criteria criteria = exm.or();
        ObjectUtils.setObject(queryDto::getIds, criteria::andIdIn);
        ObjectUtils.setList(queryDto::getExcludeIds, criteria::andIdNotIn);
        ObjectUtils.setObject(queryDto::getId, criteria::andIdEqualTo);
        ObjectUtils.setObject(queryDto::getType, criteria::andTypeEqualTo);
        ObjectUtils.setObject(queryDto::getStartTime, criteria::andCtimeGreaterThanOrEqualTo);
        ObjectUtils.setObject(queryDto::getEndTime, criteria::andCtimeLessThanOrEqualTo);
        ObjectUtils.setObject(queryDto::getAccountId, criteria::andAccountIdEqualTo);
        ObjectUtils.setObject(queryDto::getWechatAccountEqual, criteria::andWechatAccountEqualTo);
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        ObjectUtils.setLikeString(queryDto::getWechatAccount, criteria::andWechatAccountLike);
        ObjectUtils.setLikeString(queryDto::getWechatName, criteria::andWechatNameLike);
        if (Objects.nonNull(queryDto.getPage())) {
            exm.setLimit(queryDto.getPage().getLimit());
            exm.setOffset(queryDto.getPage().getOffset());
        }
        exm.setOrderByClause("mtime desc");
        return exm;
    }

    private String formCopy(Integer toAccId, LandingPageConfigDto sourceLandingPageConfigDto, NewLandingPageDto newLandingPageDto) {
        String pageDtoConfig = newLandingPageDto.getConfig();
        List<Long> sourceformIds = sourceLandingPageConfigDto.getFormIds();
        newLandingPageDto.setFormId(sourceLandingPageConfigDto.getFormId());
        if (!CollectionUtils.isEmpty(sourceformIds)) {
            MgkFormPoExample mgkFormPoExample = new MgkFormPoExample();
            mgkFormPoExample.createCriteria()
                    .andFormIdIn(sourceformIds)
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            List<MgkFormPo> sourceMgkFormPos = formDao.selectByExample(mgkFormPoExample);
            List<MgkFormPo> copyMgkFormPos = new ArrayList<>(sourceMgkFormPos);

            MgkFormItemPoExample mgkFormItemPoExample = new MgkFormItemPoExample();
            mgkFormItemPoExample.createCriteria()
                    .andFormIdIn(sourceformIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            List<MgkFormItemPo> sourceMgkFormItemPos = formItemDao.selectByExampleWithBLOBs(mgkFormItemPoExample);
            List<MgkFormItemPo> copyMgkFormItemPos = new ArrayList<>(sourceMgkFormItemPos);
            Map<Long, List<MgkFormItemPo>> copyMgkFormItemPosMaps = copyMgkFormItemPos.stream().collect(Collectors.groupingBy(MgkFormItemPo::getFormId));


            for (MgkFormPo copyMgkFormPo : copyMgkFormPos) {
                Long sourceFormId = copyMgkFormPo.getFormId();
                Long newFormId = snowflakeIdWorker.nextId();
                pageDtoConfig = pageDtoConfig.replace(sourceFormId.toString(), newFormId.toString());

                copyMgkFormPo.setFormId(newFormId);
                copyMgkFormPo.setAccountId(toAccId);
                copyMgkFormPo.setId(null);
                copyMgkFormPo.setMtime(Utils.getNow());
                copyMgkFormPo.setCtime(Utils.getNow());
                for (MgkFormItemPo r : copyMgkFormItemPosMaps.get(sourceFormId)) {
                    r.setFormId(newFormId);
                    r.setMtime(Utils.getNow());
                    r.setCtime(Utils.getNow());
                    r.setId(null);

                    Long newFormItemId = snowflakeIdWorker.nextId();

                    //item lbs copy
                    LbsShopPoExample lbsShopPoExample = new LbsShopPoExample();
                    lbsShopPoExample.createCriteria()
                            .andFormItemIdEqualTo(r.getFormItemId())
                            .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
                    List<LbsShopPo> oldLbsShopPos = lbsShopDao.selectByExample(lbsShopPoExample);
                    if (!CollectionUtils.isEmpty(oldLbsShopPos)) {
                        oldLbsShopPos.forEach(lbsShopPo -> {
                            lbsShopPo.setCtime(Utils.getNow());
                            lbsShopPo.setMtime(Utils.getNow());
                            lbsShopPo.setId(null);
                            lbsShopPo.setFormItemId(newFormItemId);
                        });
                        lbsShopDao.insertBatch(oldLbsShopPos);
                    }

                    pageDtoConfig = pageDtoConfig.replace(r.getFormItemId().toString(), newFormItemId.toString());
                    r.setFormItemId(newFormItemId);
                }
            }
            formDao.insertBatch(copyMgkFormPos);
            copyMgkFormItemPos = copyMgkFormItemPosMaps.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
            formItemDao.insertBatch(copyMgkFormItemPos);

            log.info("form copy {}, {}", copyMgkFormPos, copyMgkFormItemPos);
            List<Long> copyFormIds = copyMgkFormPos.stream().map(MgkFormPo::getFormId).collect(Collectors.toList());
            newLandingPageDto.setFormIds(copyFormIds);
            newLandingPageDto.setFormId(0L);
            pageDtoConfig = pageDtoConfig.replace("\\\"form_ids\\\"\\:\\[(.*?)\\]", copyFormIds.toString().replaceAll("\\[|]", ""));
        }
        newLandingPageDto.setConfig(pageDtoConfig);
        return pageDtoConfig;
    }

    private void copyCheck(Operator operator, List<Integer> toAccIds) {
        for (Integer toAccId : toAccIds) {
            Map<Integer, List<Integer>> sourceAccountLabelBindingMap = accountLabelService.getAccountLabelBindingMap(Lists.newArrayList(operator.getOperatorId()));
            List<Integer> sourceAccLabels = sourceAccountLabelBindingMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());

            Map<Integer, List<Integer>> toAccountLabelBindingMap = accountLabelService.getAccountLabelBindingMap(Lists.newArrayList(toAccId));
            List<Integer> toAccLabels = toAccountLabelBindingMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());

            //推送账户“没有”【405标签】（个人号黑名单） ，被推送账户“有”，推送失败，提示账户id 。
            if (toAccLabels.contains(405) && !sourceAccLabels.contains(405)) {
                throw new RuntimeException("账户" + toAccId + "因没有个人号权限落地页推送失败");
            }
            //2、推送账户“有”【413标签】（非o单元支持上报微信复制数白名单） ，被推送账户“没有”，推送失败，提示账户id 。
            copyCheckLabel(sourceAccLabels, toAccLabels, 431, "账户" + toAccId + "因没有微信复制数据权限落地页推送失败");
            //3、推送账户“有”【755标签】（跳外链权限-带货） ，被推送账户“没有”，推送失败，提示账户id 。
            copyCheckLabel(sourceAccLabels, toAccLabels, 755, "账户" + toAccId + "因没有带货账号跳转网页权限落地页推送失败");
            //4、推送账户“有”【756标签】（跳外链权限-花火） ，被推送账户“没有”，推送失败，提示账户id 。
            copyCheckLabel(sourceAccLabels, toAccLabels, 756, "账户" + toAccId + "因没有花火账号跳转网页权限落地页推送失败");
            //5、推送账户“有”【757标签】（跳外链权限-内容起飞） ，被推送账户“没有”，推送失败，提示账户id 。
            copyCheckLabel(sourceAccLabels, toAccLabels, 757, "账户" + toAccId + "因没有内容起飞账号跳转网页权限落地页推送失败");

            copyCheckLabel(sourceAccLabels, toAccLabels, 725, "账户" + toAccId + "因没有获客链接权限落地页推送失败");
        }
    }

    void copyCheckLabel(List<Integer> sourceLabels, List<Integer> toAccLabels, Integer labelId, String msg) {
        if (sourceLabels.contains(labelId)) {
            Assert.isTrue(toAccLabels.contains(labelId), msg);
        }
    }

    public static void main(String[] args) {
        String test = "{\"name\":\"小程序\",\"title\":\"页面标题\",\"header\":1,\"page_bg_color\":\"rgba(255,255,255,1)\",\"page_bg_url\":\"\",\"blocks\":[{\"id\":\"b3fdf6c2-e4a2-46b4-b542-74791c6ee1c2\",\"name\":\"fixed-block\",\"active\":false,\"components\":[],\"data\":{\"bg\":\"#ffffff\",\"height\":200},\"hasError\":false},{\"id\":\"99027327-9999-4015-8167-e8db56c4d8ef\",\"name\":\"block\",\"active\":false,\"components\":[{\"id\":\"e6db4fef-6065-430c-8606-06b04e6b8713\",\"name\":\"button-large\",\"active\":true,\"data\":{\"content\":{\"text\":\"立即提交\",\"link\":{\"type\":\"wxEvent\",\"behavior\":{\"url\":\"\",\"scheme\":\"\",\"formId\":\"\",\"formMode\":1,\"form\":{\"useTitle\":true,\"title\":\"\",\"successMessage\":\"\"},\"wxData\":{\"orgId\":\"大陆魔法\",\"name\":\"大陆魔法\",\"path\":\"index.html?referer=bili_zxanmf01&putin_media=bzwxminigame&trackid=__TRACKID__\",\"url\":\"https://niubi.com\",\"id\":24},\"floatFormData\":{\"advanceSetting\":{\"count\":{\"status\":0,\"order\":\"asc\",\"middle\":100,\"position\":\"top\",\"prefix\":\"目前已有\",\"suffix\":\"人参与活动\",\"style\":0,\"color\":\"#FE5656\"},\"link\":{\"status\":0,\"type\":\"linkEvent\",\"behavior\":{\"url\":\"\",\"scheme\":\"\",\"ios\":{\"id\":\"\",\"url\":\"\"},\"android\":{\"id\":\"\",\"url\":\"\"}}},\"recentSubmit\":{\"status\":0,\"style\":\"scrollWall\",\"styleColor\":\"default\",\"useTimeDesc\":true,\"desc\":\"xx分钟前\"}},\"style\":{\"inputStyle\":1,\"background\":\"#FFFFFF\",\"themeColor\":\"#0A8AFA\",\"color\":{\"inputBg\":\"#FFFFFF\",\"inputBorder\":\"#F0F0F0\",\"inputPlaceholder\":\"#cccccc\",\"text\":\"#333333\"}},\"buttonStyle\":{\"color\":\"#FFFFFF\",\"fontSize\":16,\"backgroundColor\":\"#0A8AFA\",\"borderColor\":\"#0A8AFA\",\"borderWidth\":1,\"borderStyle\":\"solid\",\"borderRadius\":50,\"animation\":\"none\"}},\"ios\":{\"id\":\"\",\"name\":\"\"},\"android\":{\"id\":\"\",\"name\":\"\"},\"downloadGame\":{\"id\":\"\"}}}},\"style\":{\"color\":\"#ffffff\",\"fontSize\":16,\"backgroundColor\":\"#0A8AFA\",\"borderColor\":\"#0A8AFA\",\"borderWidth\":1,\"borderRadius\":4,\"animation\":\"none\"},\"layout\":{\"resizeWidth\":1,\"resizeHeight\":1,\"aspectRatio\":0,\"left\":15,\"top\":78,\"width\":345,\"height\":44,\"rotate\":0}},\"fixed\":\"\",\"position\":true,\"hasError\":false}],\"data\":{\"bg\":\"#ffffff\",\"height\":200},\"hasError\":false}]}";
        String reg = "wxData\":(.*?),\"id\":24";
        Matcher matcher = Pattern.compile(reg).matcher(test);
        if (matcher.find()) {
            matcher.group();
        }
    }
}
