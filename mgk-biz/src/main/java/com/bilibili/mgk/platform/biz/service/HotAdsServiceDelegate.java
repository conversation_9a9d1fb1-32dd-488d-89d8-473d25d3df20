package com.bilibili.mgk.platform.biz.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.mgk.platform.api.hot_ads.dto.HotAdsDto;
import com.bilibili.mgk.platform.api.hot_ads.dto.QueryHotAdsDto;
import com.bilibili.mgk.platform.api.hot_video.dto.HotVideoCollectDto;
import com.bilibili.mgk.platform.api.hot_video.dto.QueryHotVideoCollectDto;
import com.bilibili.mgk.platform.api.hot_video.service.IHotVideoCollectService;
import com.bilibili.mgk.platform.biz.config.ClickHouseJDBCClient;
import com.bilibili.mgk.platform.biz.utils.DateUtils;
import com.bilibili.mgk.platform.common.*;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.Serializable;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.jooq.impl.DSL.*;

/**
 * @file: HotAdsServiceDelegate
 * @author: gaoming
 * @date: 2021/01/07
 * @version: 1.0
 * @description:
 **/

@Service
@Slf4j
public class HotAdsServiceDelegate {

    @Autowired
    private IHotVideoCollectService hotVideoCollectService;

    @Autowired
    private ClickHouseJDBCClient clickHouseJDBCClient;

    @Resource
    private RedisTemplate<String, String> stringRedisTemplate;

    // clickHouse表名
    @Value("${hot_ads_table_name:sycpb_2.app_hot_ad_creative}")
    private String HOT_ADS_TABLE_NAME;

    @Value("${mgk.environment:1}")
    private Integer mgkEnvironment;

    // 单元名称
    private final String COL_UNIT_NAME = "unit_name";
    // 广告类型 cpc cpm gd
    private final String COL_AD_TYPE = "ad_type";
    // 创意ID
    private final String COL_CREATIVE_ID = "creative_id";
    // 创意标题
    private final String COL_CREATIVE_TITLE = "creative_title";
    // 创意形态 1-静态图文 2-动态图文 3-静态视频 4-广告位播放视频
    private final String COL_STYLE_ABILITY = "style_ability";
    // 图片gif类型为图片链接，视频类型为封面图
    private final String COL_IMAGE_URL = "image_url";
    // 视频链接
    private final String COL_VIDEO_URL = "video_url";
    // 账户ID
    private final String COL_ACCOUNT_ID = "account_id";
    // 行业
    private final String COL_FIRST_INDUSTRY = "first_industry";
    // bvid
    private final String COL_BVID = "bvid";
    // 曝光
    private final String COL_PV = "pv";
    // 点击
    private final String COL_CLICK = "click";
    // 转化数
    private final String COL_CONV_NUM = "conv_num";
    // ctr
    private final String COL_CTR = "ctr";
    // cvr
    private final String COL_CVR = "cvr";
    // 曝光等级 S A B C
    private final String COL_PV_RANK = "pv_rank";
    // ctr等级
    private final String COL_CTR_RANK = "ctr_rank";
    // cvr等级
    private final String COL_CVR_RANK = "cvr_rank";
    // 创意创建时间
    private final String COL_CREATIVE_CTIME = "creative_ctime";
    // 同步日期
    private final String COL_LOG_DATE = "log_date";
    // 统计日期类型,7d:最近7天，30d:最近30天
    private final String COL_DAY_TYPE = "day_type";

    // 数量
    private final String COL_COUNT = "creative_id_count";

    // 资源位类型
    private final String COL_PLATFORM_CATEGORY = "platform_category";

    // 联合主键
    private final String COL_AD_TYPE_CREATIVE_ID = "ad_type_creative_id";

    // 是否是竖屏，1:是 0:否
    private final String COL_IS_VERTICAL_SCREEN = "is_vertical_screen";

    private final ArrayList<? extends Field<? extends Serializable>> fields = Lists.newArrayList(
            field(concat(field(COL_AD_TYPE).concat(inline("_")), field(COL_CREATIVE_ID))).coerce(String.class).as(COL_AD_TYPE_CREATIVE_ID),
            field(COL_UNIT_NAME).coerce(String.class).as(COL_UNIT_NAME),
            field(COL_AD_TYPE).coerce(String.class).as(COL_AD_TYPE),
            field(COL_CREATIVE_ID).coerce(String.class).as(COL_CREATIVE_ID),
            field(COL_CREATIVE_TITLE).coerce(String.class).as(COL_CREATIVE_TITLE),
            field(COL_STYLE_ABILITY).coerce(Integer.class).as(COL_STYLE_ABILITY),
            field(COL_IMAGE_URL).coerce(String.class).as(COL_IMAGE_URL),
            field(COL_VIDEO_URL).coerce(String.class).as(COL_VIDEO_URL),
            field(COL_ACCOUNT_ID).coerce(Long.class).as(COL_ACCOUNT_ID),
            field(COL_FIRST_INDUSTRY).coerce(String.class).as(COL_FIRST_INDUSTRY),
            field(COL_BVID).coerce(String.class).as(COL_BVID),
            field(COL_PV).coerce(Long.class).as(COL_PV),
            field(COL_CLICK).coerce(Long.class).as(COL_CLICK),
            field(COL_CONV_NUM).coerce(Long.class).as(COL_CONV_NUM),
            field(COL_CTR).coerce(Double.class).as(COL_CTR),
            field(COL_CVR).coerce(Double.class).as(COL_CVR),
            field(COL_PV_RANK).coerce(String.class).as(COL_PV_RANK),
            field(COL_CTR_RANK).coerce(String.class).as(COL_CTR_RANK),
            field(COL_CVR_RANK).coerce(String.class).as(COL_CVR_RANK),
            field(COL_CREATIVE_CTIME).coerce(String.class).as(COL_CREATIVE_CTIME),
            field(COL_LOG_DATE).coerce(String.class).as(COL_LOG_DATE),
            field(COL_DAY_TYPE).coerce(String.class).as(COL_DAY_TYPE),
            field(COL_IS_VERTICAL_SCREEN).coerce(Integer.class).as(COL_IS_VERTICAL_SCREEN)
    );

    public PageResult<HotAdsDto> getHotAdsDtos(Operator operator, QueryHotAdsDto queryHotAdsDto) {
        String logDate = getLogDate();
//        String logDate = "2022-08-29";

        // 查询 count
        SelectConditionStep<?> countSqlSelect = getCountSqlSelect(logDate);
        countSqlSelect = setQueryConditions(countSqlSelect, queryHotAdsDto);
        String countSql = countSqlSelect.getSQL().replace("select * from ", "");
        log.info(countSql);
        List<Map<String, Object>> maps = clickHouseJDBCClient.submitQuery(countSql);
        if(CollectionUtils.isEmpty(maps)){
            return PageResult.EMPTY_PAGE_RESULT;
        }
        Long count = maps.stream().map(m -> Long.parseLong(m.getOrDefault(COL_COUNT, "0").toString())).collect(Collectors.toList()).get(0);
        if (count == 0) {
            return PageResult.EMPTY_PAGE_RESULT;
        }

        // 查询 list
        SelectConditionStep<?> sqlSelect = getBaseSqlSelect(logDate);
        sqlSelect = setQueryConditions(sqlSelect, queryHotAdsDto);
        SelectSeekStep1<?, Object> seekStepSql = setOrderBy(sqlSelect, queryHotAdsDto);
        SelectForUpdateStep<?> selectForUpdateStep = setPage(seekStepSql, queryHotAdsDto);
        String sql = selectForUpdateStep.getSQL();
        log.info(sql);
        List<Map<String, Object>> dbData = clickHouseJDBCClient.submitQuery(sql);
        List<HotAdsDto> hotAdsDtos = dbData2Dto(dbData);
        if (operator != null) {
            setIsCollect(operator, hotAdsDtos);
        }

        return PageResult.<HotAdsDto>builder().total(count.intValue()).records(hotAdsDtos).build();
    }

    private void setIsCollect(Operator operator, List<HotAdsDto> hotAdsDtos) {
        List<HotVideoCollectDto> collectDtos = hotVideoCollectService.getCollects(QueryHotVideoCollectDto.builder()
                .isDeleted(IsDeleted.VALID.getCode())
                .collectTypes(Lists.newArrayList(MgkHotVideoCollectTypeEnum.HOT_ADS.getCode()))
                .accountIds(Lists.newArrayList(operator.getOperatorId()))
                .build());
        List<String> collects = collectDtos.stream().map(HotVideoCollectDto::getAdTypeCreativeId).collect(Collectors.toList());
        // 查询列表的广告标识为是否收藏
        hotAdsDtos.forEach(hotAdsDto -> {
            if (collects.contains(hotAdsDto.getAdTypeCreativeId())) {
                hotAdsDto.setIsCollect(MgkHotVideoIsCollectEnum.COLLECT_ENUM.getCode());
            }
        });
    }

    private List<HotAdsDto> dbData2Dto(List<Map<String, Object>> dbData) {
        return dbData.stream().filter((m) -> !Objects.isNull(m.get(COL_CREATIVE_ID))).map((m) -> {
            return HotAdsDto.builder()
                    .unitName(m.getOrDefault(COL_UNIT_NAME, "").toString())
                    .adTypeCreativeId(m.getOrDefault(COL_AD_TYPE_CREATIVE_ID, "").toString())
                    .adType(m.getOrDefault(COL_AD_TYPE, "").toString())
                    .creativeId(m.getOrDefault(COL_CREATIVE_ID, "").toString())
                    .creativeTitle(m.getOrDefault(COL_CREATIVE_TITLE, "").toString())
                    .styleAbility(Integer.parseInt(m.getOrDefault(COL_STYLE_ABILITY, "0").toString()))
                    .imageUrl(m.getOrDefault(COL_IMAGE_URL, "").toString())
                    .videoUrl(m.getOrDefault(COL_VIDEO_URL, "").toString())
                    .accountId(Long.parseLong(m.getOrDefault(COL_ACCOUNT_ID, "0").toString()))
                    .firstIndustry(m.getOrDefault(COL_FIRST_INDUSTRY, "").toString())
                    .bvid(m.getOrDefault(COL_BVID, "").toString())
                    .pv(Long.parseLong(m.getOrDefault(COL_PV, "0").toString()))
                    .click(Long.parseLong(m.getOrDefault(COL_CLICK, "0").toString()))
                    .convNum(Long.parseLong(m.getOrDefault(COL_CONV_NUM, "0").toString()))
                    .ctr(Double.parseDouble(m.getOrDefault(COL_CTR, "0").toString()))
                    .cvr(Double.parseDouble(m.getOrDefault(COL_CVR, "0").toString()))
                    .pvRank(m.getOrDefault(COL_PV_RANK, "").toString())
                    .ctrRank(m.getOrDefault(COL_CTR_RANK, "").toString())
                    .cvrRank(m.getOrDefault(COL_CVR_RANK, "").toString())
                    .creativeCreateTime(m.getOrDefault(COL_CREATIVE_CTIME, "").toString())
                    .logDate(m.getOrDefault(COL_LOG_DATE, "").toString())
                    .dayType(m.getOrDefault(COL_DAY_TYPE, "").toString())
                    .isVerticalScreen(Integer.parseInt(m.getOrDefault(COL_IS_VERTICAL_SCREEN, "0").toString()))
                    .build();
        }).collect(Collectors.toList());
    }

    private SelectForUpdateStep<?> setPage(SelectSeekStep1<?, Object> seekStepSql, QueryHotAdsDto queryHotAdsDto) {
        Page page = queryHotAdsDto.getPage();
        return seekStepSql.limit(inline(page.getLimit())).offset(inline(page.getOffset()));
    }

    private SelectSeekStep1<?, Object> setOrderBy(SelectConditionStep<?> sqlSelect, QueryHotAdsDto queryHotAdsDto) {
        MgkHotAdsOrderByEnum orderByEnum = MgkHotAdsOrderByEnum.getByCode(queryHotAdsDto.getOrderType());
        return sqlSelect.orderBy(field(orderByEnum.getOrderBy()).desc());
    }

    private SelectConditionStep<?> setQueryConditions(SelectConditionStep<?> sqlSelect, QueryHotAdsDto queryHotAdsDto) {

        Condition titleLike = DSL.falseCondition();
        if (!CollectionUtils.isEmpty(queryHotAdsDto.getCreativeTitles())) {
            for (String title : queryHotAdsDto.getCreativeTitles()) {
                titleLike = titleLike.or(field(COL_CREATIVE_TITLE).like(inline("%" + title + "%")));
            }
            sqlSelect.and(titleLike);
        }

        if (!Strings.isNullOrEmpty(queryHotAdsDto.getAdType())) {
            sqlSelect.and(field(COL_AD_TYPE).eq(inline(queryHotAdsDto.getAdType())));
        }

        if (!Strings.isNullOrEmpty(queryHotAdsDto.getCreativeId())) {
            sqlSelect.and(field(COL_CREATIVE_ID).eq(inline(queryHotAdsDto.getCreativeId())));
        }

        if (!Objects.isNull(queryHotAdsDto.getStyleAbility())) {
            sqlSelect.and(field(COL_STYLE_ABILITY).eq(inline(queryHotAdsDto.getStyleAbility())));
        }

        if (!CollectionUtils.isEmpty(queryHotAdsDto.getStyleAbilities())) {
            sqlSelect.and(field(COL_STYLE_ABILITY).in(queryHotAdsDto.getStyleAbilities().stream().map(DSL::inline).collect(Collectors.toList())));
        }

//        sqlSelect.and(field(COL_STYLE_ABILITY).notEqual(inline(MgkHotAdsCreativeTypeEnum.STATIC_VIDEO.getCode())));

        if (!Objects.isNull(queryHotAdsDto.getDateType())) {
            sqlSelect.and(field(COL_DAY_TYPE).eq(inline(MgkHotAdsDateTypeEnum.getByCode(queryHotAdsDto.getDateType()).getValue())));
        }

        if (!CollectionUtils.isEmpty(queryHotAdsDto.getIndustryList())) {
            sqlSelect.and(field(COL_FIRST_INDUSTRY).in(queryHotAdsDto.getIndustryList().stream().map(DSL::inline).collect(Collectors.toList())));
        }

        if (queryHotAdsDto.getBlackStatus() != null) {
            if (MgkHotVideoIsBlackEnum.BLACK.getCode().equals(queryHotAdsDto.getBlackStatus())) {
                sqlSelect.and(field(COL_AD_TYPE_CREATIVE_ID).in(queryHotAdsDto.getBlackList().stream().map(DSL::inline).collect(Collectors.toList())));
            }
            if (MgkHotVideoIsBlackEnum.NORMAL.getCode().equals(queryHotAdsDto.getBlackStatus())) {
                sqlSelect.and(field(COL_AD_TYPE_CREATIVE_ID).notIn(queryHotAdsDto.getBlackList().stream().map(DSL::inline).collect(Collectors.toList())));
            }
        }

        if (!Objects.isNull(queryHotAdsDto.getPlatformCategory())) {
            sqlSelect.and(field(COL_PLATFORM_CATEGORY).eq(inline(queryHotAdsDto.getPlatformCategory())));
        }

        if (!Objects.isNull(queryHotAdsDto.getIsVerticalScreen())) {
            sqlSelect.and(field(COL_IS_VERTICAL_SCREEN).eq(inline(queryHotAdsDto.getIsVerticalScreen())));
        }

        return sqlSelect;
    }

    private SelectConditionStep<?> getBaseSqlSelect(String logDate) {

        DSLContext create = DSL.using(SQLDialect.MYSQL);
        return create.select(fields).from(HOT_ADS_TABLE_NAME).where(field(COL_LOG_DATE).eq(inline(logDate)));
    }

    private String getLogDate() {
        if(MgkEnvironmentEnum.TEST.getCode().equals(mgkEnvironment)
                || MgkEnvironmentEnum.UAT.getCode().equals(mgkEnvironment)){
            return "2023-01-01";
        }
        String hotAdsLogDateKey = MgkConstants.MGK_HOT_ADS_LOG_DATE_KEY;
        String hotAdsLogDate = stringRedisTemplate.opsForValue().get(hotAdsLogDateKey);
        if (!Strings.isNullOrEmpty(hotAdsLogDate)) {
            return hotAdsLogDate;
        }
        String lastLogDate = getLastLogDate();
        stringRedisTemplate.opsForValue().set(hotAdsLogDateKey, lastLogDate, MgkConstants.HOT_ADS_LOG_DATE_EXPIRE_TIME_IN_REDIS, TimeUnit.HOURS);
        return lastLogDate;
    }

    private String getBeforeDate() {
        String nowTime = DateUtils.getTimeNow("yyyy-MM-dd");
        String timeBefore = null;
        try {
            timeBefore = DateUtils.getTimeBefore(nowTime, -1, "yyyy-MM-dd");
        } catch (ParseException e) {
            Assert.isTrue(true, e.getMessage());
        }
        return timeBefore;
    }


    private SelectConditionStep<?> getCountSqlSelect(String logDate) {


        DSLContext create = DSL.using(SQLDialect.MYSQL);
        return create.selectFrom("select count(concat(concat(cast(" + COL_AD_TYPE + " as char), '_'), cast(" + COL_CREATIVE_ID
                + " as char)) as `" + COL_AD_TYPE_CREATIVE_ID + "`) as `" + COL_COUNT
                + "` from " + HOT_ADS_TABLE_NAME + "").where(field(COL_LOG_DATE).eq(inline(logDate)));
    }

    public List<HotAdsDto> getHotAdsDetail(String adTypeCreativeId, Integer dateType) {

        SelectConditionStep<?> sqlSelect = getBaseSqlSelect(adTypeCreativeId, dateType);
        SelectLimitPercentStep<?> limitPercentStep = sqlSelect.orderBy(field(COL_LOG_DATE).desc()).limit(inline(1));

        log.info(limitPercentStep.getSQL());
        List<Map<String, Object>> dbData = clickHouseJDBCClient.submitQuery(limitPercentStep.getSQL());

        List<HotAdsDto> hotAdsDtos = dbData2Dto(dbData);

        if (CollectionUtils.isEmpty(hotAdsDtos)) {
            SelectConditionStep<?> sqlSelectOther = getBaseSqlSelect(Lists.newArrayList(adTypeCreativeId));
            SelectLimitPercentStep<?> limitPercentStepOther = sqlSelectOther.orderBy(field(COL_LOG_DATE).desc()).limit(inline(1));
            log.info(limitPercentStepOther.getSQL());
            List<Map<String, Object>> dbDataOther = clickHouseJDBCClient.submitQuery(limitPercentStepOther.getSQL());

            return dbDataOther.stream().filter((m) -> !Objects.isNull(m.get(COL_CREATIVE_ID))).map((m) -> {
                return HotAdsDto.builder()
                        .unitName(m.getOrDefault(COL_UNIT_NAME, "").toString())
                        .adTypeCreativeId(m.getOrDefault(COL_AD_TYPE_CREATIVE_ID, "").toString())
                        .adType(m.getOrDefault(COL_AD_TYPE, "").toString())
                        .creativeId(m.getOrDefault(COL_CREATIVE_ID, "").toString())
                        .creativeTitle(m.getOrDefault(COL_CREATIVE_TITLE, "").toString())
                        .styleAbility(Integer.parseInt(m.getOrDefault(COL_STYLE_ABILITY, "0").toString()))
                        .imageUrl(m.getOrDefault(COL_IMAGE_URL, "").toString())
                        .videoUrl(m.getOrDefault(COL_VIDEO_URL, "").toString())
                        .accountId(Long.parseLong(m.getOrDefault(COL_ACCOUNT_ID, "0").toString()))
                        .firstIndustry(m.getOrDefault(COL_FIRST_INDUSTRY, "").toString())
                        .bvid(m.getOrDefault(COL_BVID, "").toString())
                        .logDate(m.getOrDefault(COL_LOG_DATE, "").toString())
                        .dayType(m.getOrDefault(COL_DAY_TYPE, "").toString())
                        .build();
            }).collect(Collectors.toList());
        }
        return hotAdsDtos;
    }

    private SelectConditionStep<?> getBaseSqlSelect(String adTypeCreativeId, Integer dateType) {
        DSLContext create = DSL.using(SQLDialect.MYSQL);
        return create.select(fields).from(HOT_ADS_TABLE_NAME).where(field(COL_AD_TYPE_CREATIVE_ID).eq(inline(adTypeCreativeId)))
                .and(field(COL_DAY_TYPE).eq(inline(MgkHotAdsDateTypeEnum.getByCode(dateType).getValue())));
    }

    public void doCollect(Operator operator, String adTypeCreativeId,
                          String creativeTitle, Integer collectType, String logDate, Integer dayType) {
        // 查询这个人已经收藏过的所有创意
        List<HotVideoCollectDto> hasCollectDtos = hotVideoCollectService.getCollects(QueryHotVideoCollectDto.builder()
                .accountIds(Lists.newArrayList(operator.getOperatorId()))
                .adTypeCreativeIds(Lists.newArrayList(adTypeCreativeId))
                .collectTypes(Lists.newArrayList(collectType))
                .build());
        // 如果存在记录则更新，否则插入
        if (!CollectionUtils.isEmpty(hasCollectDtos)) {
            // 更新
            hotVideoCollectService.updateCreativeStatus(operator, hasCollectDtos.get(0).getId(),
                    IsDeleted.VALID.getCode(), logDate, dayType);
        } else {
            // 新增记录
            hotVideoCollectService.insert(HotVideoCollectDto.builder()
                    .accountId(operator.getOperatorId())
                    .adTypeCreativeId(adTypeCreativeId)
                    .isDeleted(IsDeleted.VALID.getCode())
                    .title(creativeTitle)
                    .collectType(collectType)
                    .logDate(logDate)
                    .dayType(dayType)
                    .build());
        }
    }

    public void cancelCollect(Operator operator, String adTypeCreativeId, Integer collectType) {
        // 查询这个人已经收藏过的所有创意
        List<HotVideoCollectDto> hasCollectDtos = hotVideoCollectService.getCollects(QueryHotVideoCollectDto.builder()
                .accountIds(Lists.newArrayList(operator.getOperatorId()))
                .adTypeCreativeIds(Lists.newArrayList(adTypeCreativeId))
                .collectTypes(Lists.newArrayList(collectType))
                .build());
        Assert.isTrue(!CollectionUtils.isEmpty(hasCollectDtos), "未收藏");
        hotVideoCollectService.updateCreativeStatus(operator,
                hasCollectDtos.get(0).getId(),
                IsDeleted.DELETED.getCode(), null, null);
    }

    private SelectConditionStep<?> getBaseSqlSelect(List<String> adTypeCreativeIds) {
        DSLContext create = DSL.using(SQLDialect.MYSQL);
        return create.select(fields).from(HOT_ADS_TABLE_NAME).where(field(COL_AD_TYPE_CREATIVE_ID).in(adTypeCreativeIds.stream().map(DSL::inline).collect(Collectors.toList())));
    }

    public List<HotAdsDto> getHotAdsDtosByAdTypeCreativeIds(List<String> adTypeCreativeIds) {
        if (CollectionUtils.isEmpty(adTypeCreativeIds)) {
            return Collections.emptyList();
        }
        SelectConditionStep<?> baseSqlSelect = getBaseSqlSelect(adTypeCreativeIds);
        log.info(baseSqlSelect.getSQL());
        List<Map<String, Object>> dbData = clickHouseJDBCClient.submitQuery(baseSqlSelect.getSQL());
        List<HotAdsDto> hotAdsDtos = dbData2Dto(dbData);
        return hotAdsDtos;
    }

    public String getLastLogDate() {
        DSLContext create = DSL.using(SQLDialect.MYSQL);
        SelectLimitPercentStep<Record1<String>> sqlBuilder = create.select(field(COL_LOG_DATE).coerce(String.class).as(COL_LOG_DATE)).from(HOT_ADS_TABLE_NAME).orderBy(field(COL_LOG_DATE).desc()).limit(inline(1));
        log.info(sqlBuilder.getSQL());
        List<Map<String, Object>> maps = clickHouseJDBCClient.submitQuery(sqlBuilder.getSQL());
        return maps.stream().map(m -> m.getOrDefault(COL_LOG_DATE, getBeforeDate()).toString()).collect(Collectors.toList()).get(0);
    }
}
