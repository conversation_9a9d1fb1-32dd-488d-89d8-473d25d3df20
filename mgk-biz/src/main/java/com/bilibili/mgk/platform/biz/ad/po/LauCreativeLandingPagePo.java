package com.bilibili.mgk.platform.biz.ad.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LauCreativeLandingPagePo implements Serializable {
    /**
     * 自增id
     */
    private Long id;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * 软删除
     */
    private Integer isDeleted;

    /**
     * 账号id
     */
    private Integer accountId;

    /**
     * 计划id
     */
    private Integer campaignId;

    /**
     * 单元id
     */
    private Integer unitId;

    /**
     * 创意id
     */
    private Integer creativeId;

    /**
     * 容器落地页pageid
     */
    private Long containerPageId;

    /**
     * 容器落地页url
     */
    private String containerUrl;

    /**
     * 容器降级落地页url
     */
    private String containerSecondaryUrl;

    private static final long serialVersionUID = 1L;
}