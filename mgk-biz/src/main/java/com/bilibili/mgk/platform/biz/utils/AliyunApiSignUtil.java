package com.bilibili.mgk.platform.biz.utils;

import com.alibaba.cloudapi.sdk.constant.SdkConstant;
import com.alibaba.cloudapi.sdk.model.ApiRequest;
import com.alibaba.cloudapi.sdk.util.HttpCommonUtil;
import com.alibaba.cloudapi.sdk.util.SignUtil;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName AliyunApiSignUtil
 * <AUTHOR>
 * @Date 2022/5/6 6:57 下午
 * @Version 1.0
 **/
public class AliyunApiSignUtil {

    public static void make(ApiRequest request, String appKey, String appSecret) {
        request.setPath(combinePathParam(request.getPath(), request.getPathParams()));
        StringBuilder url = (new StringBuilder()).append(request.getScheme().getValue()).append(request.getHost()).append(request.getPath());
        if (null != request.getQuerys() && request.getQuerys().size() > 0) {
            url.append("?").append(HttpCommonUtil.buildParamString(request.getQuerys()));
        }

        request.setUrl(url.toString());
        Date current = request.getCurrentDate() == null ? new Date() : request.getCurrentDate();
        if (null == request.getFirstHeaderValue("date")) {
            request.addHeader("date", getHttpDateHeaderValue(current));
        }

        request.addHeader("x-ca-timestamp", String.valueOf(current.getTime()));
        if (request.isGenerateNonce() && null == request.getFirstHeaderValue("x-ca-nonce")) {
            request.addHeader("x-ca-nonce", UUID.randomUUID().toString());
        }

        request.addHeader("user-agent", "BILI_MGK");
        request.addHeader("host", request.getHost());
        if (request.isNeedSignature()) {
            request.addHeader("x-ca-key", appKey);
        }

        request.addHeader("CA_VERSION", "1.1.2");
        if (null == request.getFirstHeaderValue("content-type")) {
            request.addHeader("content-type", request.getMethod().getRequestContentType());
        }

        if (null == request.getFirstHeaderValue("accept")) {
            request.addHeader("accept", request.getMethod().getAcceptContentType());
        }

        if (request.isNeedSignature() && !HttpCommonUtil.isEmpty(request.getSignatureMethod())) {
            request.addHeader("X-Ca-Signature-Method", request.getSignatureMethod());
        }

        if (null != request.getBody() && request.isGenerateContentMd5() && request.getBody().length > 0 && null == request.getFirstHeaderValue("content-md5")) {
            request.addHeader("content-md5", SignUtil.base64AndMD5(request.getBody()));
        }

        if (request.isNeedSignature()) {
            String signature = SignUtil.sign(request, appSecret);
            request.addHeader("x-ca-signature", signature);
        }

        String key;
        List values;
        for(Iterator i$ = request.getHeaders().keySet().iterator(); i$.hasNext(); request.getHeaders().put(key, values)) {
            key = (String)i$.next();
            values = (List)request.getHeaders().get(key);
            if (null != values && values.size() > 0) {
                for(int i = 0; i < values.size(); ++i) {
                    byte[] temp = ((String)values.get(i)).getBytes(SdkConstant.CLOUDAPI_ENCODING);
                    values.set(i, new String(temp, SdkConstant.CLOUDAPI_HEADER_ENCODING));
                }
            }
        }

    }

    public static Request generateRequest(ApiRequest apiRequest) {
        RequestBody requestBody = null;
        if (null != apiRequest.getFormParams() && apiRequest.getFormParams().size() > 0) {
            requestBody = RequestBody.create(MediaType.parse(apiRequest.getFirstHeaderValue("content-type")), HttpCommonUtil.buildParamString(apiRequest.getFormParams()));
        } else if (null != apiRequest.getBody() && apiRequest.getBody().length > 0) {
            requestBody = RequestBody.create(MediaType.parse(apiRequest.getFirstHeaderValue("content-type")), apiRequest.getBody());
        }
        return (new Request.Builder()).method(apiRequest.getMethod().getValue(), requestBody).url(apiRequest.getUrl()).headers(getHeadersFromMap(apiRequest.getHeaders())).build();
    }

    private static Headers getHeadersFromMap(Map<String, List<String>> map) {
        List<String> nameAndValues = new ArrayList();
        Iterator iterator = map.entrySet().iterator();

        while(iterator.hasNext()) {
            Map.Entry<String, List<String>> entry = (Map.Entry)iterator.next();
            Iterator entryIterator = ((List)entry.getValue()).iterator();

            while(entryIterator.hasNext()) {
                String value = (String)entryIterator.next();
                nameAndValues.add(entry.getKey());
                nameAndValues.add(value);
            }
        }

        return Headers.of((String[])nameAndValues.toArray(new String[nameAndValues.size()]));
    }

    private static String combinePathParam(String path, Map<String, String> pathParams) {
        if (pathParams == null) {
            return path;
        } else {
            String key;
            for(Iterator i$ = pathParams.keySet().iterator(); i$.hasNext(); path = path.replace("[" + key + "]", (CharSequence)pathParams.get(key))) {
                key = (String)i$.next();
            }

            return path;
        }
    }

    private static String getHttpDateHeaderValue(Date date) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.US);
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        return dateFormat.format(date);
    }

}
