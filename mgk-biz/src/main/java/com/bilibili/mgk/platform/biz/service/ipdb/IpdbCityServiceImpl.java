package com.bilibili.mgk.platform.biz.service.ipdb;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.http.utils.OkHttpUtils;
import com.bilibili.mgk.platform.api.ipdb.IIpdbCityService;
import com.bilibili.mgk.platform.api.ipdb.dto.RegionAnalyzeDto;
import com.bilibili.mgk.platform.common.MgkEnvironmentEnum;
import lombok.extern.slf4j.Slf4j;
import net.ipip.ipdb.City;
import net.ipip.ipdb.CityInfo;
import org.modelmapper.internal.util.Assert;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.Objects;

/**
 * @ClassName IpdbCityUtil
 * <AUTHOR>
 * @Date 2022/8/30 3:53 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class IpdbCityServiceImpl implements IIpdbCityService {

    private City cityIpdb;

    @Value("${mgk.environment:1}")
    private Integer mgkEnvironment;

    @Value("${direct.download.ipdb.file.uat.url:http://upos-hz-uat.bilivideo.com/testbucket/stable_v4_flagship.ipdb}")
    private String directDownloadIpdbFileUatUrl;

    @Value("${download.ipdb.file.api.url:http://bvc-nerve.bilibili.co/ipip/checkipipnetversion}")
    private String downloadIpdbFileApiUrl;

    private static final String DOWNLOAD_IPDB_FILE_PATH = "/data/cityIp.ipdb";

    private static final String DOWNLOAD_IPDB_FILE_JSON_KEY = "ipv4_flagship_ipdb_stable_url";

    private static final String DOWNLOAD_IPDB_DATA_JSON_KEY = "data";

    private static final String DOWNLOAD_IPDB_LANGUAGE_CN = "CN";

    private static final String TW_PREFIX = "71";
    private static final String HK_PREFIX = "81";
    /**
     * 澳门 MACAO
     */
    private static final String MC_PREFIX = "82";

    private static final String CODE_PROVINCE_SUFFIX = "0000";

    @PostConstruct
    public void init() {
        boolean isRefreshSuccess = refreshIpdbDbInfo();
        log.info("refreshIpdbDbInfo result:{}", isRefreshSuccess);
    }

    private boolean loadIpdbInfoDb() {
        try {
            City newCityIpdb = new City(DOWNLOAD_IPDB_FILE_PATH);
            cityIpdb = newCityIpdb;
            return true;
        } catch (Exception e) {
            log.error("refreshIpdbDbInfo failed, e:{}", e);
            return false;
        }
    }

    @Override
    public boolean refreshIpdbDbInfo() throws RuntimeException {
        String downloadFileUrl = directDownloadIpdbFileUatUrl;
        if (MgkEnvironmentEnum.PROD.getCode().equals(mgkEnvironment) ||
                MgkEnvironmentEnum.PRE.getCode().equals(mgkEnvironment)) {
            downloadFileUrl = getIpdbDownloadFileUrl();
        }
        refreshIpdbFile(downloadFileUrl);
        try {
            cityIpdb = new City(DOWNLOAD_IPDB_FILE_PATH);
        } catch (Exception e) {
            log.error("refreshIpdbDbInfo failed, e:{}", e);
            throw new RuntimeException(e);
        }
        return true;
    }

    private void refreshIpdbFile(String downloadUrl) throws RuntimeException {
        try {
            Assert.isTrue(!StringUtils.isEmpty(downloadUrl), "ipdb文件下载url不能为空");
            InputStream inputStream = OkHttpUtils.get(downloadUrl).callForStream();
            int index;
            byte[] bytes = new byte[1024];
            File tempFile = new File(DOWNLOAD_IPDB_FILE_PATH);
            if (!tempFile.exists()) {
                tempFile.createNewFile();
            }
            FileOutputStream downloadFile = new FileOutputStream(tempFile);
            while ((index = inputStream.read(bytes)) > 0) {
                downloadFile.write(bytes, 0, index);
                downloadFile.flush();
            }
            inputStream.close();
            downloadFile.close();
        } catch (Exception e) {
            log.error("downloadIpdbFile failed, e:{}", e);
           throw new RuntimeException(e);
        }
    }

    private String getIpdbDownloadFileUrl() {
        JSONObject jsonObject = OkHttpUtils.get(downloadIpdbFileApiUrl).callForObject(JSONObject.class);
        if (Objects.isNull(jsonObject)) {
            return "";
        }
        JSONObject data = jsonObject.getObject(DOWNLOAD_IPDB_DATA_JSON_KEY, JSONObject.class);
        if (Objects.isNull(data)) {
            return "";
        }
        String url = data.getString(DOWNLOAD_IPDB_FILE_JSON_KEY);
        return url;
    }

    @Override
    public RegionAnalyzeDto analyzeIpCityInfo(String ip) {
        CityInfo result = new CityInfo(new String[0]);
        try {
            result = cityIpdb.findInfo(ip, DOWNLOAD_IPDB_LANGUAGE_CN);
        } catch (Exception e) {
            log.error("analyzeIpCityInfo failed, e:{}", e);
        }
        return convertCityInfo2Dto(result);
    }

    private RegionAnalyzeDto convertCityInfo2Dto(CityInfo result) {
        String chinaAdminCode = result.getChinaAdminCode();
        if (chinaAdminCode.startsWith(TW_PREFIX)
                || chinaAdminCode.startsWith(HK_PREFIX)
                || chinaAdminCode.startsWith(MC_PREFIX)) {
            chinaAdminCode = chinaAdminCode.substring(0,2) + CODE_PROVINCE_SUFFIX;
        }
        return RegionAnalyzeDto.builder()
                .cityName(result.getCityName())
                .chinaAdminCode(chinaAdminCode)
                .build();
    }

    public String getAdd(String ip){
        String apk = "l1mnHtGUdR7nS110PochueTmNrTAVZQX";
       return OkHttpUtils.get("https://api.map.baidu.com/location/ip").param("ip", ip)
                .param("ak", apk)
                .callForObject(String.class);
    }
}
