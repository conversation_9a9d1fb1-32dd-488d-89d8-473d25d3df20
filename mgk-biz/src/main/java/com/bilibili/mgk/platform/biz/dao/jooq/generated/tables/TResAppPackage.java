/*
 * This file is generated by jOOQ.
 */
package com.bilibili.mgk.platform.biz.dao.jooq.generated.tables;


import com.bilibili.mgk.platform.biz.dao.jooq.generated.DefaultSchema;
import com.bilibili.mgk.platform.biz.dao.jooq.generated.Indexes;
import com.bilibili.mgk.platform.biz.dao.jooq.generated.Keys;
import com.bilibili.mgk.platform.biz.dao.jooq.generated.tables.records.ResAppPackageRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.jooq.types.UByte;
import org.jooq.types.UInteger;


/**
 * app应用包信息表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TResAppPackage extends TableImpl<ResAppPackageRecord> {

    private static final long serialVersionUID = 893589226;

    /**
     * The reference instance of <code>res_app_package</code>
     */
    public static final TResAppPackage RES_APP_PACKAGE = new TResAppPackage();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ResAppPackageRecord> getRecordType() {
        return ResAppPackageRecord.class;
    }

    /**
     * The column <code>res_app_package.id</code>. 主键ID
     */
    public final TableField<ResAppPackageRecord, UInteger> ID = createField(DSL.name("id"), org.jooq.impl.SQLDataType.INTEGERUNSIGNED.nullable(false).identity(true), this, "主键ID");

    /**
     * The column <code>res_app_package.account_id</code>. 账号id
     */
    public final TableField<ResAppPackageRecord, UInteger> ACCOUNT_ID = createField(DSL.name("account_id"), org.jooq.impl.SQLDataType.INTEGERUNSIGNED.nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGERUNSIGNED)), this, "账号id");

    /**
     * The column <code>res_app_package.name</code>. 应用包名称
     */
    public final TableField<ResAppPackageRecord, String> NAME = createField(DSL.name("name"), org.jooq.impl.SQLDataType.VARCHAR(32).nullable(false).defaultValue(org.jooq.impl.DSL.inline("", org.jooq.impl.SQLDataType.VARCHAR)), this, "应用包名称");

    /**
     * The column <code>res_app_package.url</code>. 应用包原始下载链接
     */
    public final TableField<ResAppPackageRecord, String> URL = createField(DSL.name("url"), org.jooq.impl.SQLDataType.VARCHAR(512).nullable(false).defaultValue(org.jooq.impl.DSL.inline("", org.jooq.impl.SQLDataType.VARCHAR)), this, "应用包原始下载链接");

    /**
     * The column <code>res_app_package.package_name</code>. 应用包的包名称
     */
    public final TableField<ResAppPackageRecord, String> PACKAGE_NAME = createField(DSL.name("package_name"), org.jooq.impl.SQLDataType.VARCHAR(128).nullable(false).defaultValue(org.jooq.impl.DSL.inline("", org.jooq.impl.SQLDataType.VARCHAR)), this, "应用包的包名称");

    /**
     * The column <code>res_app_package.app_name</code>. 应用名称
     */
    public final TableField<ResAppPackageRecord, String> APP_NAME = createField(DSL.name("app_name"), org.jooq.impl.SQLDataType.VARCHAR(64).nullable(false).defaultValue(org.jooq.impl.DSL.inline("", org.jooq.impl.SQLDataType.VARCHAR)), this, "应用名称");

    /**
     * The column <code>res_app_package.platform</code>. 适应系统 1-IOS, 2-Android,3-iphone, 4-ipad
     */
    public final TableField<ResAppPackageRecord, UByte> PLATFORM = createField(DSL.name("platform"), org.jooq.impl.SQLDataType.TINYINTUNSIGNED.nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.TINYINTUNSIGNED)), this, "适应系统 1-IOS, 2-Android,3-iphone, 4-ipad");

    /**
     * The column <code>res_app_package.version</code>. 版本号
     */
    public final TableField<ResAppPackageRecord, String> VERSION = createField(DSL.name("version"), org.jooq.impl.SQLDataType.VARCHAR(64).nullable(false).defaultValue(org.jooq.impl.DSL.inline("", org.jooq.impl.SQLDataType.VARCHAR)), this, "版本号");

    /**
     * The column <code>res_app_package.size</code>. 应用包大小（单位字节）
     */
    public final TableField<ResAppPackageRecord, UInteger> SIZE = createField(DSL.name("size"), org.jooq.impl.SQLDataType.INTEGERUNSIGNED.nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGERUNSIGNED)), this, "应用包大小（单位字节）");

    /**
     * The column <code>res_app_package.md5</code>. 应用包的MD5
     */
    public final TableField<ResAppPackageRecord, String> MD5 = createField(DSL.name("md5"), org.jooq.impl.SQLDataType.VARCHAR(32).nullable(false).defaultValue(org.jooq.impl.DSL.inline("", org.jooq.impl.SQLDataType.VARCHAR)), this, "应用包的MD5");

    /**
     * The column <code>res_app_package.icon_url</code>. 图标url
     */
    public final TableField<ResAppPackageRecord, String> ICON_URL = createField(DSL.name("icon_url"), org.jooq.impl.SQLDataType.VARCHAR(255).nullable(false).defaultValue(org.jooq.impl.DSL.inline("", org.jooq.impl.SQLDataType.VARCHAR)), this, "图标url");

    /**
     * The column <code>res_app_package.ctime</code>. 创建时间
     */
    public final TableField<ResAppPackageRecord, LocalDateTime> CTIME = createField(DSL.name("ctime"), org.jooq.impl.SQLDataType.LOCALDATETIME.nullable(false).defaultValue(org.jooq.impl.DSL.field("CURRENT_TIMESTAMP", org.jooq.impl.SQLDataType.LOCALDATETIME)), this, "创建时间");

    /**
     * The column <code>res_app_package.mtime</code>. 修改时间
     */
    public final TableField<ResAppPackageRecord, LocalDateTime> MTIME = createField(DSL.name("mtime"), org.jooq.impl.SQLDataType.LOCALDATETIME.nullable(false).defaultValue(org.jooq.impl.DSL.field("CURRENT_TIMESTAMP", org.jooq.impl.SQLDataType.LOCALDATETIME)), this, "修改时间");

    /**
     * The column <code>res_app_package.is_deleted</code>. 软删除 0-有效, 1-删除
     */
    public final TableField<ResAppPackageRecord, UByte> IS_DELETED = createField(DSL.name("is_deleted"), org.jooq.impl.SQLDataType.TINYINTUNSIGNED.nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.TINYINTUNSIGNED)), this, "软删除 0-有效, 1-删除");

    /**
     * The column <code>res_app_package.internal_url</code>. 应用包内部下载链接
     */
    public final TableField<ResAppPackageRecord, String> INTERNAL_URL = createField(DSL.name("internal_url"), org.jooq.impl.SQLDataType.VARCHAR(255).nullable(false).defaultValue(org.jooq.impl.DSL.inline("", org.jooq.impl.SQLDataType.VARCHAR)), this, "应用包内部下载链接");

    /**
     * The column <code>res_app_package.status</code>. 应用包状态 0-有效，1-无效
     */
    public final TableField<ResAppPackageRecord, UByte> STATUS = createField(DSL.name("status"), org.jooq.impl.SQLDataType.TINYINTUNSIGNED.nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.TINYINTUNSIGNED)), this, "应用包状态 0-有效，1-无效");

    /**
     * The column <code>res_app_package.platform_status</code>. 平台状态 0-有效, 1-禁用
     */
    public final TableField<ResAppPackageRecord, UByte> PLATFORM_STATUS = createField(DSL.name("platform_status"), org.jooq.impl.SQLDataType.TINYINTUNSIGNED.nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.TINYINTUNSIGNED)), this, "平台状态 0-有效, 1-禁用");

    /**
     * The column <code>res_app_package.developer_name</code>. 开发商名称
     */
    public final TableField<ResAppPackageRecord, String> DEVELOPER_NAME = createField(DSL.name("developer_name"), org.jooq.impl.SQLDataType.VARCHAR(64).nullable(false).defaultValue(org.jooq.impl.DSL.inline("", org.jooq.impl.SQLDataType.VARCHAR)), this, "开发商名称");

    /**
     * The column <code>res_app_package.authority_url</code>. 权限地址
     */
    public final TableField<ResAppPackageRecord, String> AUTHORITY_URL = createField(DSL.name("authority_url"), org.jooq.impl.SQLDataType.VARCHAR(128).nullable(false).defaultValue(org.jooq.impl.DSL.inline("", org.jooq.impl.SQLDataType.VARCHAR)), this, "权限地址");

    /**
     * The column <code>res_app_package.auth_code_list</code>. 权限code
     */
    public final TableField<ResAppPackageRecord, String> AUTH_CODE_LIST = createField(DSL.name("auth_code_list"), org.jooq.impl.SQLDataType.VARCHAR(512).nullable(false).defaultValue(org.jooq.impl.DSL.inline("", org.jooq.impl.SQLDataType.VARCHAR)), this, "权限code");

    /**
     * The column <code>res_app_package.apk_update_time</code>. 安装包更新时间
     */
    public final TableField<ResAppPackageRecord, LocalDateTime> APK_UPDATE_TIME = createField(DSL.name("apk_update_time"), org.jooq.impl.SQLDataType.LOCALDATETIME.nullable(false).defaultValue(org.jooq.impl.DSL.inline("1970-01-01 00:00:00", org.jooq.impl.SQLDataType.LOCALDATETIME)), this, "安装包更新时间");

    /**
     * The column <code>res_app_package.privacy_policy</code>. 隐私政策地址
     */
    public final TableField<ResAppPackageRecord, String> PRIVACY_POLICY = createField(DSL.name("privacy_policy"), org.jooq.impl.SQLDataType.VARCHAR(512).nullable(false).defaultValue(org.jooq.impl.DSL.inline("", org.jooq.impl.SQLDataType.VARCHAR)), this, "隐私政策地址");

    /**
     * The column <code>res_app_package.dmp_app_id</code>. dmp_app_id
     */
    public final TableField<ResAppPackageRecord, Integer> DMP_APP_ID = createField(DSL.name("dmp_app_id"), org.jooq.impl.SQLDataType.INTEGER.nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "dmp_app_id");

    /**
     * The column <code>res_app_package.is_new_fly</code>. 是否新起飞：0-否 1-是
     */
    public final TableField<ResAppPackageRecord, UByte> IS_NEW_FLY = createField(DSL.name("is_new_fly"), org.jooq.impl.SQLDataType.TINYINTUNSIGNED.nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.TINYINTUNSIGNED)), this, "是否新起飞：0-否 1-是");

    /**
     * The column <code>res_app_package.description</code>. 描述信息
     */
    public final TableField<ResAppPackageRecord, String> DESCRIPTION = createField(DSL.name("description"), org.jooq.impl.SQLDataType.CLOB, this, "描述信息");

    /**
     * The column <code>res_app_package.sub_title</code>. 简介
     */
    public final TableField<ResAppPackageRecord, String> SUB_TITLE = createField(DSL.name("sub_title"), org.jooq.impl.SQLDataType.VARCHAR(256).nullable(false).defaultValue(org.jooq.impl.DSL.inline("", org.jooq.impl.SQLDataType.VARCHAR)), this, "简介");

    /**
     * Create a <code>res_app_package</code> table reference
     */
    public TResAppPackage() {
        this(DSL.name("res_app_package"), null);
    }

    /**
     * Create an aliased <code>res_app_package</code> table reference
     */
    public TResAppPackage(String alias) {
        this(DSL.name(alias), RES_APP_PACKAGE);
    }

    /**
     * Create an aliased <code>res_app_package</code> table reference
     */
    public TResAppPackage(Name alias) {
        this(alias, RES_APP_PACKAGE);
    }

    private TResAppPackage(Name alias, Table<ResAppPackageRecord> aliased) {
        this(alias, aliased, null);
    }

    private TResAppPackage(Name alias, Table<ResAppPackageRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("app应用包信息表"), TableOptions.table());
    }

    public <O extends Record> TResAppPackage(Table<O> child, ForeignKey<O, ResAppPackageRecord> key) {
        super(child, key, RES_APP_PACKAGE);
    }

    @Override
    public Schema getSchema() {
        return DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.<Index>asList(Indexes.RES_APP_PACKAGE_IX_ACCOUNT_ID, Indexes.RES_APP_PACKAGE_IX_ID_ACCOUNT_ID, Indexes.RES_APP_PACKAGE_IX_MD5, Indexes.RES_APP_PACKAGE_IX_MTIME, Indexes.RES_APP_PACKAGE_IX_NAME, Indexes.RES_APP_PACKAGE_IX_PACKAGE_NAME);
    }

    @Override
    public Identity<ResAppPackageRecord, UInteger> getIdentity() {
        return Keys.IDENTITY_RES_APP_PACKAGE;
    }

    @Override
    public UniqueKey<ResAppPackageRecord> getPrimaryKey() {
        return Keys.KEY_RES_APP_PACKAGE_PRIMARY;
    }

    @Override
    public List<UniqueKey<ResAppPackageRecord>> getKeys() {
        return Arrays.<UniqueKey<ResAppPackageRecord>>asList(Keys.KEY_RES_APP_PACKAGE_PRIMARY);
    }

    @Override
    public TResAppPackage as(String alias) {
        return new TResAppPackage(DSL.name(alias), this);
    }

    @Override
    public TResAppPackage as(Name alias) {
        return new TResAppPackage(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TResAppPackage rename(String name) {
        return new TResAppPackage(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TResAppPackage rename(Name name) {
        return new TResAppPackage(name, null);
    }
}
