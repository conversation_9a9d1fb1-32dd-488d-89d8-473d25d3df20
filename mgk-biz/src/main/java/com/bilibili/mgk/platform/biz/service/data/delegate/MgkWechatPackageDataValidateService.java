package com.bilibili.mgk.platform.biz.service.data.delegate;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.mgk.platform.api.data.dto.WechatPackageReportDataDto;
import com.bilibili.mgk.platform.biz.dao.MgkWechatPackageDataDao;
import com.bilibili.mgk.platform.biz.po.MgkFormDataPo;
import com.bilibili.mgk.platform.biz.po.MgkFormDataPoExample;
import com.bilibili.mgk.platform.biz.po.MgkWechatPackageDataPo;
import com.bilibili.mgk.platform.biz.po.MgkWechatPackageDataPoExample;
import com.bilibili.mgk.platform.common.MgkConstants;
import com.bilibili.mgk.platform.common.WhetherEnum;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.List;

/**
 * @ClassName MgkWechatPackageDataValidateService
 * <AUTHOR>
 * @Date 2022/6/18 8:56 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class MgkWechatPackageDataValidateService {

    @Value("${wechat.package.anti.cheat.date.range:5}")
    private Integer wechatPackageAntiCheatTimeRange;

    @Autowired
    private MgkWechatPackageDataDao mgkWechatPackageDataDao;

    public void validateWechatPackageData(WechatPackageReportDataDto reportDataDto) {
        Assert.notNull(reportDataDto, "上报数据不可为空");
        Assert.notNull(reportDataDto.getPageId(), "页面ID不可为空");
        Assert.notNull(reportDataDto.getWechatPackageDataDto(), "微信加粉数据不可为空");
        Assert.notNull(reportDataDto.getCtime(), "上报时间不可为空");
        if (reportDataDto.getSalesType() != null) {
            SalesType.getByCode(reportDataDto.getSalesType());
        }
        if (!Strings.isNullOrEmpty(reportDataDto.getRequestId())) {
            Assert.isTrue(reportDataDto.getRequestId().length() <= MgkConstants.REQUESTID_LEN, "requestId过长");
        }
        if (!Strings.isNullOrEmpty(reportDataDto.getBuvid())) {
            Assert.isTrue(reportDataDto.getBuvid().length() <= MgkConstants.BUVID_LEN, "buvid过长");
        }
        if (!Strings.isNullOrEmpty(reportDataDto.getImei())) {
            Assert.isTrue(reportDataDto.getImei().length() <= MgkConstants.IMEI_LEN, "imei过长");
        }
        if (!Strings.isNullOrEmpty(reportDataDto.getDeviceId())) {
            Assert.isTrue(reportDataDto.getDeviceId().length() <= MgkConstants.DEVICE_LEN, "deviceId过长");
        }
        Timestamp beginTime = Utils.getSomeDayAgo(Utils.getNow(), wechatPackageAntiCheatTimeRange);
        Integer isCheat = checkIsCheat(reportDataDto, beginTime);
        reportDataDto.setIsCheat(isCheat);
    }

    public Integer checkIsCheat(WechatPackageReportDataDto reportDataDto, Timestamp beginTime) {
        if (StringUtils.isEmpty(reportDataDto.getTrackId())
                || StringUtils.isEmpty(reportDataDto.getRequestId())
                || !Utils.isPositive(reportDataDto.getCreativeId())) {
            return WhetherEnum.NO.getCode();
        }
        // 目前只针对requestId creativeId反作弊 复制归复制 跳转归跳转反作弊
        Integer dataType = reportDataDto.getWorkWxDataType() != null ? reportDataDto.getWorkWxDataType()
                : reportDataDto.getWechatPackageDataDto().getDataType();
        List<MgkWechatPackageDataPo> antiCheatPoList = getRecentRequestIdRecord(reportDataDto.getRequestId(), reportDataDto.getCreativeId(),
                beginTime, dataType);
        if (CollectionUtils.isEmpty(antiCheatPoList)) {
            return WhetherEnum.NO.getCode();
        }
        return WhetherEnum.YES.getCode();
    }

    private List<MgkWechatPackageDataPo> getRecentRequestIdRecord(String requestId, Long creativeId, Timestamp beginTime, Integer dataType) {
        MgkWechatPackageDataPoExample exm = new MgkWechatPackageDataPoExample();
        exm.or().andRequestIdEqualTo(requestId)
                .andCreativeIdEqualTo(creativeId)
                .andTrackIdNotEqualTo("")
                .andDataTypeEqualTo(dataType)
                .andCtimeGreaterThanOrEqualTo(beginTime)
                .andIsCheatEqualTo(WhetherEnum.NO.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        return mgkWechatPackageDataDao.selectByExample(exm);
    }
}
