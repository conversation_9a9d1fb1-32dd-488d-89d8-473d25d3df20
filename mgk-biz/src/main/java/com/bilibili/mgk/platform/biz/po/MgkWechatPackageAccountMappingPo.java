package com.bilibili.mgk.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkWechatPackageAccountMappingPo implements Serializable {
    /**
     * 主键自增id
     */
    private Long id;

    /**
     * 微信账户id
     */
    private Integer wechatAccountId;

    /**
     * 微信包id
     */
    private Integer wechatPackageId;

    /**
     * 是否被删除 0-正常 1-被删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}