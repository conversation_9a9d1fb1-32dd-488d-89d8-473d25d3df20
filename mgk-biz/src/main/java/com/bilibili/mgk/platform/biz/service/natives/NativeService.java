package com.bilibili.mgk.platform.biz.service.natives;

import com.bapis.ad.audit.*;
import com.bilibili.adp.common.enums.AuditStatus;
import com.bilibili.collage.api.dto.CollageEnterpriseVideoDto;
import com.bilibili.mgk.platform.biz.bean.NativeArchiveBo;
import com.bilibili.mgk.platform.biz.bean.NativeCreativeBo;
import com.bilibili.mgk.platform.common.enums.natives.NativeStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class NativeService {

    @Autowired
    private AuditServiceGrpc.AuditServiceBlockingStub auditServiceBlockingStub;

    public static final Integer NATIVE_BIZ_TYPE_SANLAIN = 1;


    public List<NativeArchiveBo> queryNativeArchive(List<Long> avidList) {
        if (CollectionUtils.isEmpty(avidList)) {
            return new ArrayList<>();
        }

        QueryNativeArchivesReq queryNativeArchivesReq = QueryNativeArchivesReq.newBuilder()
                .addAllAvids(avidList)
                .setBizType(NATIVE_BIZ_TYPE_SANLAIN)
                .build();
        QueryNativeArchivesReply queryNativeArchivesReply = auditServiceBlockingStub.queryNativeArchives(queryNativeArchivesReq);
        List<SingleQueryNativeArchivesReply> nativeArchiveList = queryNativeArchivesReply.getListList();
        return nativeArchiveList.stream().map(this::convertNativeArchiveReply).collect(Collectors.toList());
    }

    private List<NativeCreativeBo> queryNativeCreative(List<Integer> creativeIds) {

        QueryNativeCreativeRelativityReq queryNativeCreativeRelativityReq = QueryNativeCreativeRelativityReq.newBuilder()
                .addAllCreativeIds(creativeIds)
                .build();
        QueryNativeCreativeRelativityReply queryNativeCreativeRelativityReply = auditServiceBlockingStub.queryNativeCreativeRelativityList(queryNativeCreativeRelativityReq);
        List<SingleQueryNativeCreativeRelativityReply> nativeCreativeList = queryNativeCreativeRelativityReply.getListList();
        return nativeCreativeList.stream().map(this::convertNativeCreativeReply).collect(Collectors.toList());

    }


    private NativeCreativeBo convertNativeCreativeReply(SingleQueryNativeCreativeRelativityReply relativityReply) {
        NativeCreativeBo nativeCreativeBo = new NativeCreativeBo();
        nativeCreativeBo.setCreativeId(relativityReply.getCreativeId());
        nativeCreativeBo.setAvid(relativityReply.getAvid());
        nativeCreativeBo.setAuditStatus(relativityReply.getAuditStatus());
        nativeCreativeBo.setReason(relativityReply.getReason());
        return nativeCreativeBo;
    }

    private NativeArchiveBo convertNativeArchiveReply(SingleQueryNativeArchivesReply archivesReply) {
        NativeArchiveBo nativeArchiveBo = new NativeArchiveBo();

        nativeArchiveBo.setAvid(archivesReply.getAvid());
        nativeArchiveBo.setAuditStatus(archivesReply.getAuditStatus());
//        nativeArchiveBo.setBizType(archivesReply.getBizType());

        return nativeArchiveBo;
    }

    public static void fillNativeStatus(List<CollageEnterpriseVideoDto> dtoList, List<NativeArchiveBo> nativeBoList) {
        if (CollectionUtils.isEmpty(nativeBoList) || CollectionUtils.isEmpty(dtoList)) {
            log.info("fillNativeStatus is null or empty nativeBoList={} dtoList ={}", nativeBoList, dtoList);
            return;
        }
        fillGeneralNativeStatus(dtoList, nativeBoList);
        dtoList.sort(Comparator.comparing(CollageEnterpriseVideoDto::getNativeStatus));

    }

    /**
     * 不需要按原生排序
     * @param dtoList
     * @param nativeBoList
     */
    public static void fillGeneralNativeStatus(List<CollageEnterpriseVideoDto> dtoList, List<NativeArchiveBo> nativeBoList) {

        if (CollectionUtils.isEmpty(nativeBoList)) {
            return;
        }

        Map<Long, Integer> archiveStatusMap = nativeBoList.stream()
                .collect(Collectors.toMap(NativeArchiveBo::getAvid, NativeArchiveBo::getAuditStatus, (t1, t2) -> t2));

        for (CollageEnterpriseVideoDto collageEnterpriseVideoDto : dtoList) {
            Long aid = collageEnterpriseVideoDto.getAid();
            Integer status = archiveStatusMap.get(aid);
            if (Objects.isNull(status)) {
                collageEnterpriseVideoDto.setNativeStatus(NativeStatusEnum.NOT_NATIVE.getCode());
                collageEnterpriseVideoDto.setNativeStatusText(NativeStatusEnum.NOT_NATIVE.getShowText());
            } else if (Objects.equals(status, com.bilibili.adp.common.enums.AuditStatus.ACCEPT.getCode())) {
                collageEnterpriseVideoDto.setNativeStatus(NativeStatusEnum.NATIVE_PASS.getCode());
                collageEnterpriseVideoDto.setNativeStatusText(NativeStatusEnum.NATIVE_PASS.getShowText());
            } else if (Objects.equals(status, AuditStatus.REJECT.getCode())) {
                collageEnterpriseVideoDto.setNativeStatus(NativeStatusEnum.NATIVE_REJECT.getCode());
                collageEnterpriseVideoDto.setNativeStatusText(NativeStatusEnum.NATIVE_REJECT.getShowText());
            } else {
                collageEnterpriseVideoDto.setNativeStatus(NativeStatusEnum.NOT_NATIVE.getCode());
                collageEnterpriseVideoDto.setNativeStatusText(NativeStatusEnum.NOT_NATIVE.getShowText());
            }
        }
    }
}
