package com.bilibili.mgk.platform.biz.dao.querydsl.pos;

import javax.annotation.Generated;

/**
 * MgkLandingPageGroupQueryDSLPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class MgkLandingPageGroupQueryDSLPo {

    private Integer accountId;

    private Integer auditCreativeId;

    private java.sql.Timestamp ctime;

    private Long groupId;

    private String groupName;

    private Integer groupSource;

    private Integer groupStatus;

    private Integer hasVideoPage;

    private Long id;

    private Integer isDeleted;

    private Long modifyVersion;

    private java.sql.Timestamp mtime;

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public Integer getAuditCreativeId() {
        return auditCreativeId;
    }

    public void setAuditCreativeId(Integer auditCreativeId) {
        this.auditCreativeId = auditCreativeId;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public Integer getGroupSource() {
        return groupSource;
    }

    public void setGroupSource(Integer groupSource) {
        this.groupSource = groupSource;
    }

    public Integer getGroupStatus() {
        return groupStatus;
    }

    public void setGroupStatus(Integer groupStatus) {
        this.groupStatus = groupStatus;
    }

    public Integer getHasVideoPage() {
        return hasVideoPage;
    }

    public void setHasVideoPage(Integer hasVideoPage) {
        this.hasVideoPage = hasVideoPage;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Long getModifyVersion() {
        return modifyVersion;
    }

    public void setModifyVersion(Long modifyVersion) {
        this.modifyVersion = modifyVersion;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
         return "accountId = " + accountId + ", auditCreativeId = " + auditCreativeId + ", ctime = " + ctime + ", groupId = " + groupId + ", groupName = " + groupName + ", groupSource = " + groupSource + ", groupStatus = " + groupStatus + ", hasVideoPage = " + hasVideoPage + ", id = " + id + ", isDeleted = " + isDeleted + ", modifyVersion = " + modifyVersion + ", mtime = " + mtime;
    }

}

