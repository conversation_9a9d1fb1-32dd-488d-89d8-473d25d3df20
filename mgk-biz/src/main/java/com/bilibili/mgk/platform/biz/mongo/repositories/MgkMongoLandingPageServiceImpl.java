package com.bilibili.mgk.platform.biz.mongo.repositories;

import com.bilibili.mgk.platform.api.landing_page.dto.MongoLandingPageDto;
import com.bilibili.mgk.platform.api.landing_page.service.IMgkMongoLandingPageService;
import com.bilibili.mgk.platform.biz.mongo.pojo.MongoLandPageConfigPo;
import com.bilibili.mgk.platform.common.MgkConstants;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCursor;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.*;
import static com.mongodb.client.model.Filters.in;

/**
 * @file: MgkMongoLandingPageServiceImpl
 * @author: gaoming
 * @date: 2021/03/02
 * @version: 1.0
 * @description:
 **/
@Service
public class MgkMongoLandingPageServiceImpl implements IMgkMongoLandingPageService {

    @Resource
    private IMgkMongoLandingPageRepository mgkMongoLandingPageRepository;

    @Autowired
    private MongoOperations mongoTemplate;


    @Override
    public void saveLandingPage(MongoLandingPageDto dto) {
        MongoLandPageConfigPo po = convertMongoLandingPageDto2Po(dto);
        mgkMongoLandingPageRepository.insert(po);
    }

    @Override
    public List<MongoLandingPageDto> findContainsWebUrlByPageIds(Integer accountId, List<Long> pageIds, Long pageId) {
        Bson filter = and(eq(MgkConstants.MGK_PAGE_MONGO_FIELD_NAME_ACCOUNT_ID, accountId.longValue()), regex("config.weburl", "cm.bilibili.com/mgk/page/" + pageId), in(MgkConstants.MGK_PAGE_MONGO_FIELD_NAME_PAGE_ID, pageIds));
        FindIterable<Document> businessMgk = mongoTemplate.getCollection(MgkConstants.MGK_PAGE_MONGO_COLLECTION_NAME).find(filter);

        List<Document> docs = new ArrayList<>();

        MongoCursor<Document> cousor = businessMgk.iterator();
        while (cousor.hasNext()) {
            docs.add(cousor.next());
        }

        if (CollectionUtils.isEmpty(docs)) {
            return Collections.emptyList();
        }

        return docs.stream().map(document -> {
            return MongoLandingPageDto.builder()
                    .accountId(document.getInteger("accountId"))
                    .config(document.get("config"))
                    .pageId(document.getLong("_id"))
                    .build();

        }).collect(Collectors.toList());
    }

    private MongoLandPageConfigPo convertMongoLandingPageDto2Po(MongoLandingPageDto dto) {
        return MongoLandPageConfigPo.builder()
                .pageId(dto.getPageId())
                .config(dto.getConfig())
                .accountId(dto.getAccountId())
                .build();
    }
}
