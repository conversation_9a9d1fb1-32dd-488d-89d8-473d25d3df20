package com.bilibili.mgk.platform.biz.dao.querydsl.pos;

import javax.annotation.Generated;

/**
 * MgkLandingPageGroupMappingQueryDSLPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class MgkLandingPageGroupMappingQueryDSLPo {

    private Integer accountId;

    private Long containerPageId;

    private String containerPageUrl;

    private java.sql.Timestamp ctime;

    private Long groupId;

    private Integer groupSource;

    private Long id;

    private Integer isDeleted;

    private Integer isEnable;

    private Integer mappingStatus;

    private java.sql.Timestamp mtime;

    private Long pageId;

    private String pageUrl;

    private String reason;

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public Long getContainerPageId() {
        return containerPageId;
    }

    public void setContainerPageId(Long containerPageId) {
        this.containerPageId = containerPageId;
    }

    public String getContainerPageUrl() {
        return containerPageUrl;
    }

    public void setContainerPageUrl(String containerPageUrl) {
        this.containerPageUrl = containerPageUrl;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Integer getGroupSource() {
        return groupSource;
    }

    public void setGroupSource(Integer groupSource) {
        this.groupSource = groupSource;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(Integer isEnable) {
        this.isEnable = isEnable;
    }

    public Integer getMappingStatus() {
        return mappingStatus;
    }

    public void setMappingStatus(Integer mappingStatus) {
        this.mappingStatus = mappingStatus;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public Long getPageId() {
        return pageId;
    }

    public void setPageId(Long pageId) {
        this.pageId = pageId;
    }

    public String getPageUrl() {
        return pageUrl;
    }

    public void setPageUrl(String pageUrl) {
        this.pageUrl = pageUrl;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    @Override
    public String toString() {
         return "accountId = " + accountId + ", containerPageId = " + containerPageId + ", containerPageUrl = " + containerPageUrl + ", ctime = " + ctime + ", groupId = " + groupId + ", groupSource = " + groupSource + ", id = " + id + ", isDeleted = " + isDeleted + ", isEnable = " + isEnable + ", mappingStatus = " + mappingStatus + ", mtime = " + mtime + ", pageId = " + pageId + ", pageUrl = " + pageUrl + ", reason = " + reason;
    }

}

