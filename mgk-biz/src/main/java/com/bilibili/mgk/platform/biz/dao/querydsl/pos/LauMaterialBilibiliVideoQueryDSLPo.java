package com.bilibili.mgk.platform.biz.dao.querydsl.pos;

import javax.annotation.Generated;

/**
 * LauMaterialBilibiliVideoQueryDSLPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class LauMaterialBilibiliVideoQueryDSLPo {

    private Long avid;

    private Long cid;

    private String coverMd5;

    private String coverUrl;

    private java.sql.Timestamp ctime;

    private Integer height;

    private Long id;

    private Integer isCm;

    private Long mid;

    private java.sql.Timestamp mtime;

    private String videoFrom;

    private Long videoPage;

    private Integer width;

    public Long getAvid() {
        return avid;
    }

    public void setAvid(Long avid) {
        this.avid = avid;
    }

    public Long getCid() {
        return cid;
    }

    public void setCid(Long cid) {
        this.cid = cid;
    }

    public String getCoverMd5() {
        return coverMd5;
    }

    public void setCoverMd5(String coverMd5) {
        this.coverMd5 = coverMd5;
    }

    public String getCoverUrl() {
        return coverUrl;
    }

    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getIsCm() {
        return isCm;
    }

    public void setIsCm(Integer isCm) {
        this.isCm = isCm;
    }

    public Long getMid() {
        return mid;
    }

    public void setMid(Long mid) {
        this.mid = mid;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public String getVideoFrom() {
        return videoFrom;
    }

    public void setVideoFrom(String videoFrom) {
        this.videoFrom = videoFrom;
    }

    public Long getVideoPage() {
        return videoPage;
    }

    public void setVideoPage(Long videoPage) {
        this.videoPage = videoPage;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    @Override
    public String toString() {
         return "avid = " + avid + ", cid = " + cid + ", coverMd5 = " + coverMd5 + ", coverUrl = " + coverUrl + ", ctime = " + ctime + ", height = " + height + ", id = " + id + ", isCm = " + isCm + ", mid = " + mid + ", mtime = " + mtime + ", videoFrom = " + videoFrom + ", videoPage = " + videoPage + ", width = " + width;
    }

}

