package com.bilibili.mgk.platform.biz.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.json.Json;

/**
 * @ClassName GeelyPostResponseDto
 * <AUTHOR>
 * @Date 2022/5/6 7:10 下午
 * @Version 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GeelyPostResponseDto {

    private Boolean success;

    private String currentTime;

    private String message;

    private Body object;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Body {
        private String leadId;
    }

}
