package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.SobotAdComplainAttachPo;
import com.bilibili.mgk.platform.biz.po.SobotAdComplainAttachPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SobotAdComplainAttachDao {
    long countByExample(SobotAdComplainAttachPoExample example);

    int deleteByExample(SobotAdComplainAttachPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(SobotAdComplainAttachPo record);

    int insertSelective(SobotAdComplainAttachPo record);

    List<SobotAdComplainAttachPo> selectByExample(SobotAdComplainAttachPoExample example);

    SobotAdComplainAttachPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") SobotAdComplainAttachPo record, @Param("example") SobotAdComplainAttachPoExample example);

    int updateByExample(@Param("record") SobotAdComplainAttachPo record, @Param("example") SobotAdComplainAttachPoExample example);

    int updateByPrimaryKeySelective(SobotAdComplainAttachPo record);

    int updateByPrimaryKey(SobotAdComplainAttachPo record);
}