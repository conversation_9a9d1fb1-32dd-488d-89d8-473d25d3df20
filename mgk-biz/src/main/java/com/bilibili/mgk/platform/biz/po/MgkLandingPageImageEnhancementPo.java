package com.bilibili.mgk.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkLandingPageImageEnhancementPo implements Serializable {
    /**
     * 自增ID
     */
    private Long id;

    /**
     * 页面ID
     */
    private Long pageId;

    /**
     * 亮度
     */
    private Double lum;

    /**
     * 灰度
     */
    private Double grey;

    /**
     * 颜色
     */
    private Double color;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}