package com.bilibili.mgk.platform.biz.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.mgk.platform.api.inspiration.dto.ArticleDto;
import com.bilibili.mgk.platform.api.inspiration.dto.NewArticleDto;
import com.bilibili.mgk.platform.api.inspiration.dto.QueryArticleDto;
import com.bilibili.mgk.platform.api.inspiration.dto.UpdateArticleDto;
import com.bilibili.mgk.platform.api.inspiration.service.IInspirationService;
import com.bilibili.mgk.platform.biz.validator.InspirationValidator;
import com.bilibili.mgk.platform.common.MgkConstants;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * @file: InspirationServiceImpl
 * @author: gaoming
 * @date: 2021/03/17
 * @version: 1.0
 * @description:
 **/
@Service
@Slf4j
public class InspirationServiceImpl implements IInspirationService {
    @Autowired
    private InspirationServiceDelegate inspirationServiceDelegate;

    @Autowired
    private InspirationValidator inspirationValidator;

    @Autowired
    private MgkBaseService mgkBaseService;


    @Override
    public List<String> uploadAndParsePDF(File file) throws IOException {
        return inspirationServiceDelegate.uploadAndParsePDF(file);
    }

    @Override
    public Long createArticle(Operator operator, NewArticleDto newArticleDto) {
        inspirationValidator.validNewArticleDto(newArticleDto);
        return inspirationServiceDelegate.createArticle(operator, newArticleDto);
    }

    @Override
    public void updateArticle(Operator operator, UpdateArticleDto updateArticleDto) {
        inspirationValidator.validUpdateArticleDto(updateArticleDto);

        RLock lock = mgkBaseService.getLock(updateArticleDto.getArticleId(), MgkConstants.INSPIRATION_LOCK_SUFFIX);
        try {
            inspirationServiceDelegate.updateArticle(operator, updateArticleDto);
        } finally {
            log.info(System.currentTimeMillis() + "---update article unLock----");
            lock.unlock();
        }
    }

    @Override
    public void addRead(Long articleId) {
        RLock lock = mgkBaseService.getLock(articleId, MgkConstants.INSPIRATION_LOCK_SUFFIX);
        try {
            inspirationServiceDelegate.addRead(articleId);
        } finally {
            log.info(System.currentTimeMillis() + "---update article read count unLock----");
            lock.unlock();
        }

    }

    @Override
    public void addLike(Operator operator, Long articleId) {
        RLock lock = mgkBaseService.getLock(articleId, MgkConstants.INSPIRATION_LOCK_SUFFIX);
        try {
            inspirationServiceDelegate.addLike(operator, articleId);
        } finally {
            log.info(System.currentTimeMillis() + "---update article like count unLock----");
            lock.unlock();
        }

    }

    @Override
    public void unlike(Operator operator, Long articleId) {
        RLock lock = mgkBaseService.getLock(articleId, MgkConstants.INSPIRATION_LOCK_SUFFIX);
        try {
            inspirationServiceDelegate.unlike(operator, articleId);
        } finally {
            log.info(System.currentTimeMillis() + "---update article unlike count unLock----");
            lock.unlock();
        }
    }

    @Override
    public PageResult<ArticleDto> queryArticle(QueryArticleDto queryArticleDto) {
        inspirationValidator.validQueryArticleDto(queryArticleDto);
        return inspirationServiceDelegate.queryArticle(queryArticleDto);
    }

    @Override
    public ArticleDto getArticleByArticleId(Operator operator, Long articleId) {
        return inspirationServiceDelegate.getArticleByArticleId(operator, articleId);
    }

    @Override
    public List<ArticleDto> queryRelevant(Operator operator) {
        return inspirationServiceDelegate.queryRelevant(operator);
    }

    @Override
    public void updateArticleStatus(Long articleId, Integer status) {
        RLock lock = mgkBaseService.getLock(articleId, MgkConstants.INSPIRATION_LOCK_SUFFIX);
        try {
            inspirationServiceDelegate.updateArticleStatus(articleId, status);
        } finally {
            log.info(System.currentTimeMillis() + "---update article status unLock----");
            lock.unlock();
        }
    }
}
