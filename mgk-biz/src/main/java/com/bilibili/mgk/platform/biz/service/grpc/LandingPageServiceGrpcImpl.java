package com.bilibili.mgk.platform.biz.service.grpc;

import static com.bilibili.mgk.platform.biz.service.grpc.convert.PageGrpcConvert.convertGrpc2adpOperator;
import static com.bilibili.mgk.platform.biz.service.grpc.convert.PageGrpcConvert.convertLandingPageDtoList2PageReply;
import static com.bilibili.mgk.platform.biz.service.grpc.convert.PageGrpcConvert.convertMgkPageDtoList2LaunchPageList;

import com.bapis.ad.mgk.*;
import com.bapis.ad.pandora.core.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.mgk.platform.api.landing_page.dto.MgkLandingPageDto;
import com.bilibili.mgk.platform.api.landing_page.dto.MgkLandingPageWithMacroParamDto;
import com.bilibili.mgk.platform.api.landing_page.dto.QueryLandingPageParamDto;
import com.bilibili.mgk.platform.api.landing_page.dto.QueryPageWithExtraParamDto;
import com.bilibili.mgk.platform.api.landing_page.dto.QuerySanlianTemplatePageDto;
import com.bilibili.mgk.platform.api.landing_page.dto.SanlianMgkTemplatePageDto;
import com.bilibili.mgk.platform.api.landing_page.dto.TemplatePageDto;
import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageService;
import com.bilibili.mgk.platform.biz.po.MgkLandingPagePo;
import com.bilibili.mgk.platform.biz.repo.MgkLandingPageRepo;
import com.bilibili.mgk.platform.biz.service.grpc.convert.PageGrpcConvert;
import com.bilibili.mgk.platform.biz.service.wechat.delegate.MgkWechatPackageServiceDelegate;
import com.bilibili.mgk.platform.common.page_bean.MgkLandingPageBean;
import com.bilibili.mgk.platform.common.utils.DataUtils;
import com.bilibili.mgk.platform.common.utils.ExceptionUtils;
import com.bilibili.mgk.platform.common.utils.TimeUtil;
import com.google.common.collect.Lists;
import com.google.protobuf.Empty;
import io.grpc.Status;
import io.grpc.stub.StreamObserver;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.concurrent.CompletableFuture;
/**
 * @ClassName LandingPageServiceGrpcImpl
 * <AUTHOR>
 * @Date 2022/11/30 8:40 下午
 * @Version 1.0
 * todo 注意 GRPC的返回对象里面的参数set塞值的时候,不能为null！！！
 **/
@Service
@Slf4j
public class LandingPageServiceGrpcImpl extends LandingPageServiceGrpc.LandingPageServiceImplBase {
    private static final String ID = "LandingPageServiceGrpcImpl";

    @Autowired
    private IMgkLandingPageService mgkLandingPageService;

    @Autowired
    private MgkWechatPackageServiceDelegate mgkWechatPackageServiceDelegate;
    @Autowired
    private MgkLandingPageRepo mgkLandingPageRepo;


    /**
     * <pre>
     * 获取可用联投落地页信息
     * </pre>
     */
    @Override
    public void templatePages(TemplatePagesReq request, StreamObserver<TemplatePagesReply> responseObserver) {
        try {
            Assert.notNull((request),
                    "查询参数不可为空");
            List<TemplatePageDto> templatePageList =
                    mgkLandingPageService.getMgkOnlineTemplatePage(request.getMgkPageIdList());
            Map<Long, TemplatePage> resultMap = templatePageList.stream().map(templatePage -> {
                TemplatePage.Builder templatePageBuilder = TemplatePage.newBuilder();
                templatePageBuilder.setMgkPageId(templatePage.getMgkPageId());
                templatePageBuilder.setJumpUrl(templatePage.getLaunchUrl());
                templatePageBuilder.setJumpUrlSecondary(templatePage.getLaunchUrlSecondary());
                return templatePageBuilder.build();
            }).collect(Collectors.toMap(TemplatePage::getMgkPageId, Function.identity()));
            TemplatePagesReply.Builder builder = TemplatePagesReply.newBuilder();
            if (!CollectionUtils.isEmpty(resultMap)) {
                builder.putAllTemplatePages(resultMap);
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}: templatePages 失败,{}", ID,  ExceptionUtils.getSubStringMsg(t));
        } catch (Exception t) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:templatePages 失败,{}", ID,  ExceptionUtils.getSubStringMsg(t));
        }
    }

    /**
     * <pre>
     * 获取落地页状态
     * </pre>
     */
    @Override
    public void mgkPageStatus(MgkPagesStatusReq request, StreamObserver<MgkPagesStatusReply> responseObserver) {
        try {
            Assert.notNull(request , "请求参数不能为空");
            List<MgkLandingPageDto> pageDtos =
                    mgkLandingPageService.getLandingPageDtoByPageIds(request.getMgkPageIdList());
            Map<Long, MgkPageStatus> pageStatusMap = pageDtos.stream()
                    .collect(Collectors.toMap(MgkLandingPageDto::getPageId, pageDto -> {
                        MgkPageStatus mgkPageStatus = MgkPageStatus.forNumber(pageDto.getStatus());
                        if (Objects.isNull(mgkPageStatus)) {
                            return MgkPageStatus.UNKNOWN;
                        }
                        return mgkPageStatus;
                    }));
            MgkPagesStatusReply.Builder builder = MgkPagesStatusReply.newBuilder();
            if (!CollectionUtils.isEmpty(pageStatusMap)) {
                builder.putAllPagesStatus(pageStatusMap);
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:mgkPageStatus 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        } catch (Exception t) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:mgkPageStatus 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        }
    }

    /**
     * <pre>
     *删除落地页
     * </pre>
     */
    @Override
    public void disable(HandlePagesReq request, StreamObserver<com.google.protobuf.Empty> responseObserver) {
        try {
            mgkLandingPageService.batchDisable(convertGrpc2adpOperator(request.getOperator()),
                    new ArrayList<>(request.getMgkPageIdList()));
            responseObserver.onNext(Empty.newBuilder().build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:disable 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        } catch (Exception t) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:disable 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        }
    }


    /**
     * <pre>
     *查询落地页
     * </pre>
     */
    @Override
    public void queryLandingPage(com.bapis.ad.mgk.QueryPagesReq request,
                                 io.grpc.stub.StreamObserver<com.bapis.ad.mgk.MgkPagesReply> responseObserver) {
        try {
            QueryLandingPageParamDto paramDto = new QueryLandingPageParamDto();
            BeanUtils.copyProperties(request, paramDto);
            paramDto.setEffectiveStartTime(TimeUtil.longToTimestamp(request.getEffectiveStartTime()));
            paramDto.setEffectiveEndTime(TimeUtil.longToTimestamp(request.getEffectiveEndTime()));
            paramDto.setPageIdList(request.getMgkPageIdList());
            paramDto.setOrderBy("mtime desc");
            log.info("queryLandingPageUseGrpc para is [{}]", paramDto);
            List<MgkLandingPageDto> pageDtos = mgkLandingPageService.getLandingPageDtos(paramDto);
            List<MgkPageReply> replyList = new ArrayList<>();
            if(!CollectionUtils.isEmpty(pageDtos)){
                log.info("queryLandingPageUseGrpc res pageIds is [{}]", pageDtos.stream()
                        .map(MgkLandingPageDto::getPageId).collect(Collectors.toList()));
                replyList = convertLandingPageDtoList2PageReply(pageDtos);
                log.info("queryLandingPageUseGrpc replyList count is [{}]", replyList.size());
            }
            responseObserver.onNext(MgkPagesReply.newBuilder().addAllMgkPageReply(replyList).build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:queryLandingPage 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        } catch (Exception t) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:queryLandingPage 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        }

    }


    /**
     * <pre>
     *根据微信包查询落地页
     * </pre>
     */
    @Override
    public void queryPageByWechatPackageId(MgkWechatPackageReq request,
                                           StreamObserver<MgkPageIdsReply> responseObserver) {
        log.info("queryPageByWechatPackageIdUseGrpc wechatPackageId is [{}]", request.getWechatPackageId());
        try {
            List<Long> pageIds = mgkWechatPackageServiceDelegate
                    .getPageIdsByWechatPackageIds(Lists.newArrayList(request.getWechatPackageId()));
            log.info("queryPageByWechatPackageIdUseGrpc wechatPackageId is [{}], pageIds is [{}]",
                    request.getWechatPackageId(), pageIds);
            responseObserver.onNext(MgkPageIdsReply.newBuilder().addAllPageId(pageIds).build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:queryPageByWechatPackageId 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        } catch (Exception t) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:queryPageByWechatPackageId 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        }
    }

    /**
     * <pre>
     * 查询建站落地页投放用信息
     * </pre>
     */
    @Override
    public void launchPage(LaunchPageReq request, StreamObserver<LaunchPageReply> responseObserver) {
        try {
            Assert.notNull(request, "请求参数不可为空");
            QueryLandingPageParamDto queryDto = QueryLandingPageParamDto.builder()
                    .accountIdList(request.getAccountIdList())
                    .pageIdList(request.getPageIdList())
                    .nameLike(request.getName())
                    .templateStyleList(request.getTemplateStyleList())
                    .statusList(request.getStatusList())
                    .typeList(request.getTypeList())
                    .isVideoPages(request.getIsVideoPageList())
                    .hasDpaGoods(request.getHasDpaGoods() < 0 ? null : request.getHasDpaGoods())
                    .build();
            List<MgkLandingPageDto> pageDtos = mgkLandingPageService.getLaunchLandingPageDtos(queryDto);

            Map<Long, List<Integer>> pageId2ApkId = new HashMap<>();
            if(!CollectionUtils.isEmpty(pageDtos)){
                pageId2ApkId = mgkLandingPageService.getAppPackageIdMapInPageIds(pageDtos.stream()
                        .map(MgkLandingPageDto::getPageId)
                        .collect(Collectors.toList()));
            }

            List<LaunchPage> launchPageList = convertMgkPageDtoList2LaunchPageList(pageDtos, pageId2ApkId);
            LaunchPageReply.Builder builder = LaunchPageReply.newBuilder();
            if (!CollectionUtils.isEmpty(launchPageList)) {
                builder.addAllPage(launchPageList);
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:launchPage 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        } catch (Exception t) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:launchPage 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        }
    }

    /**
     * <pre>
     * 分页查询建站落地页投放用信息
     * </pre>
     */
    @Override
    public void launchPageByPage(LaunchPageByPageReq request, StreamObserver<LaunchPageByPageReply> responseObserver) {
        try {
            QueryLandingPageParamDto queryDto = QueryLandingPageParamDto.builder()
                    .accountIdList(request.getAccountIdList())
                    .nameLike(request.getName())
                    .pageIdList(request.getPageIdList())
                    .templateStyleList(request.getTemplateStyleList())
                    .statusList(request.getStatusList())
                    .typeList(request.getTypeList())
                    .isVideoPages(request.getIsVideoPageList())
                    .hasDpaGoods(request.getHasDpaGoods() < 0 ? null : request.getHasDpaGoods())
                    .orderBy("mtime desc")
                    .build();
            Page page = Page.valueOf(request.getPage(), request.getPageSize());
            PageResult<MgkLandingPageDto> pageResult = mgkLandingPageService.getLaunchLandingPageDtoByPage(queryDto, page);

            Map<Long, List<Integer>> pageId2ApkId = new HashMap<>();
            if(Utils.isPositive(pageResult.getTotal())){
                pageId2ApkId = mgkLandingPageService.getAppPackageIdMapInPageIds(pageResult.getRecords().stream()
                        .map(MgkLandingPageDto::getPageId)
                        .collect(Collectors.toList()));
            }
            List<LaunchPage> launchPageList = convertMgkPageDtoList2LaunchPageList(
                    pageResult.getRecords(), pageId2ApkId);
            LaunchPageByPageReply.Builder builder = LaunchPageByPageReply.newBuilder()
                    .setTotal(pageResult.getTotal());
            if (!CollectionUtils.isEmpty(launchPageList)) {
                builder.addAllPage(launchPageList);
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:launchPageByPage 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        } catch (Exception t) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:launchPageByPage 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        }
    }


    /**
     * <pre>
     * 获取落地页
     * </pre>
     */
    @Override
    public void validatePageIdAndGetPage(ValidateAndGetPageReq request, StreamObserver<ValidateAndGetPageReply> responseObserver) {
        try {
            Assert.notNull(request, "请求参数不可为空");
            Assert.isTrue(Utils.isPositive(request.getPageId()), "请求落地页id不可为空");
            MgkLandingPageBean mgkLandingPageBean = mgkLandingPageService.validatePageIdAndGetPage(request.getPageId());
            ValidateAndGetPageReply reply = PageGrpcConvert.convertResultBean2Reply(mgkLandingPageBean);
            responseObserver.onNext(reply);
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:validatePageIdAndGetPage 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        } catch (Exception t) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:validatePageIdAndGetPage 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        }
    }

    @Override
    public void getTemplatePageList(GetTemplatePageListReq request, StreamObserver<GetTemplatePageListReply> responseObserver) {
        try {
            Operator originOperator = request.getOperator();
            com.bilibili.adp.common.bean.Operator operator = com.bilibili.adp.common.bean.Operator.builder()
                    .operatorId(originOperator.getOperatorId())
                    .operatorName(originOperator.getOperatorName())
                    .operatorType(OperatorType.getByCode(originOperator.getOperatorType()))
                    .build();
            List<Long> pageIdList = request.getPageIdList();
            List<Integer> gameBaseIdList = Utils.isPositive(request.getGameBaseId()) ?
                    Lists.newArrayList(request.getGameBaseId()) : Collections.emptyList();
            List<Integer> appPackageIdList = Utils.isPositive(request.getAppPackageId()) ?
                    Lists.newArrayList(request.getAppPackageId()) : Collections.emptyList();
            List<String> urlList = request.getUrlList();
            QuerySanlianTemplatePageDto queryDto = QuerySanlianTemplatePageDto.builder()
                    .operator(operator)
                    .pageIdList(pageIdList)
                    .gameBaseIdList(gameBaseIdList)
                    .appPackageIdList(appPackageIdList)
                    .urlList(urlList)
                    .build();
            List<SanlianMgkTemplatePageDto> templatePageDtoList = mgkLandingPageService.getMgkTemplatePageList(queryDto);
            List<TemplatePageEntity> entityList = templatePageDtoList.stream()
                    .map(this::convertDto2Entity)
                    .collect(Collectors.toList());

            GetTemplatePageListReply.Builder builder = GetTemplatePageListReply.newBuilder();
            if (!CollectionUtils.isEmpty(entityList)) {
                builder.addAllTemplatePage(entityList);
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:getTemplatePageList 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        } catch (Exception t) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:getTemplatePageList 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        }
    }

    private TemplatePageEntity convertDto2Entity(SanlianMgkTemplatePageDto templatePageDto) {
        return TemplatePageEntity.newBuilder()
                .setMgkPageId(templatePageDto.getMgkPageId())
                .setTemplatePageId(templatePageDto.getTemplatePageId())
                .setJumpUrl(templatePageDto.getLaunchUrl())
                .setJumpUrlSecondary(templatePageDto.getLaunchUrlSecondary())
                .setGameBaseId(templatePageDto.getGameBaseId())
                .setAppPackageId(templatePageDto.getAppPackageId())
                .setUrl(templatePageDto.getUrl())
                .build();
    }

    @Override
    public void validateGetPageList(ValidateAndGetPageListReq request, StreamObserver<ValidateAndGetPageListReply> responseObserver) {
        try {
            List<Long> pageIdList = request.getPageIdList();
            List<MgkLandingPageBean> pageBeanList = mgkLandingPageService.validateAndGetPageList(pageIdList);
            List<PageBaseInfoEntity> entityList = pageBeanList.stream().map(pageBean -> {
                return PageBaseInfoEntity.newBuilder()
                        .setPageId(pageBean.getMgkPageId())
                        .setAccountId(pageBean.getAccountId())
                        .setType(pageBean.getPageType())
                        .setTemplateStyle(pageBean.getTemplateStyle())
                        .setJumpUrl(pageBean.getLaunchUrl())
                        .setJumpUrlSecondary(pageBean.getLaunchUrlSecondary())
                        .setAdVersionControlId(pageBean.getAdVersionControllId())
                        .setPageStatus(MgkPageStatus.forNumber(pageBean.getPageStatus()))
                        .build();
            }).collect(Collectors.toList());
            ValidateAndGetPageListReply.Builder builder = ValidateAndGetPageListReply.newBuilder();
            if (!CollectionUtils.isEmpty(entityList)) {
                builder.addAllEntity(entityList);
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();

        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:validateGetPageList 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        } catch (Exception t) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:validateGetPageList 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        }
    }

    /**
     * <pre>
     * 获取可以投放的落地页
     * </pre>
     */
    @Override
    public void getLaunchAblePage(LaunchAblePageReq request, StreamObserver<LaunchAblePageReply> responseObserver) {
        try {
            Assert.notNull(request, "请求参数不可为空");
            List<Long> pageIdList = request.getPageIdList();
            List<Long> launchAblePageIds = mgkLandingPageService.getLaunchAblePageIds(pageIdList);
            LaunchAblePageReply.Builder builder = LaunchAblePageReply.newBuilder();
            if (!CollectionUtils.isEmpty(launchAblePageIds)) {
                builder.addAllPageId(launchAblePageIds);
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:getLaunchAblePage 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        } catch (Exception t) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:getLaunchAblePage 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        }
    }



    /**
     * <pre>
     *查询有限的落地页(目前限制了100,按照更新时间逆序排列的前100个),请求参数可以使用一些副表的条件
     * </pre>
     */
    public void queryLimitLandingPageWithExtraParam(QueryPagesExtraReq request,
                                                    StreamObserver<MgkPagesWithMacroParamReply> responseObserver) {
        try {
            QueryPageWithExtraParamDto query = QueryPageWithExtraParamDto.builder()
                    .templateStyleList(request.getTemplateStyleList())
                    .titleLike(request.getTitleLike())
                    .typeList(request.getTypeList())
                    .appIdList(request.getAppIdList())
                    .needWithApp(request.getNeedWithApp())
                    .statusList(request.getStatusList())
                    .formIdList(request.getFormIdList())
                    .needWithForm(request.getNeedWithForm())
                    .pageIdList(request.getMgkPageIdList())
                    .nameLike(request.getNameLike())
                    .accountIdList(request.getAccountIdList())
                    .lauMiniGameId(Lists.newArrayList(request.getLauMiniGameId()))
                    .needWithLauMiniGame(request.getNeedWithLauMiniGame())
                    .build();
            log.info("queryLimitLandingPageWithExtraParam query is [{}]", query);
            List<MgkLandingPageWithMacroParamDto> macroParamDtos = mgkLandingPageService
                    .queryLimitLandingPageWithExtraParam(query);

            List<MgkPageWithMacroParamReply> replyList;
            if(!CollectionUtils.isEmpty(macroParamDtos)){
                replyList = macroParamDtos.stream().map(dto->{
                    MgkPageWithMacroParamReply.Builder builder = MgkPageWithMacroParamReply.newBuilder();
                    BeanUtils.copyProperties(dto, builder, DataUtils.getNullPropertyNames(dto));
                    builder.setMtime(dto.getMtime().getTime());
                    builder.setEffectiveStartTime(dto.getEffectiveStartTime().getTime());
                    builder.setEffectiveEndTime(dto.getEffectiveEndTime().getTime());
                    if(!CollectionUtils.isEmpty(dto.getPlatform2AppUrls())){
                        builder.putAllAppUrls(dto.getPlatform2AppUrls());
                    }
                    if(!CollectionUtils.isEmpty(dto.getPlatformAccessUrlWithParams())){
                        builder.putAllPlatformAccessUrlWithParams(dto.getPlatformAccessUrlWithParams());
                    }
                    return builder.build();
                }).collect(Collectors.toList());
            }else {
                replyList = new ArrayList<>();
            }
            responseObserver.onNext(MgkPagesWithMacroParamReply.newBuilder()
                    .addAllMgkPageReply(replyList).build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:queryLimitLandingPageWithExtraParam 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        } catch (Exception t) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:queryLimitLandingPageWithExtraParam 失败,{}", ID, ExceptionUtils.getSubStringMsg(t));
        }
    }

    @Override
    public void queryPageCount(QueryPageCountReq request, StreamObserver<QueryPageCountReply> responseObserver) {
        try {
            Assert.notNull((request), "查询参数不可为空");

            Integer count = mgkLandingPageRepo.queryPageCount(request.getAccountId(), request.getBeginTime(), request.getEndTime());

            responseObserver.onNext(QueryPageCountReply.newBuilder().setCount(count).build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}: queryPageCount 失败,{}", ID,  ExceptionUtils.getSubStringMsg(t));
        } catch (Exception t) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:queryPageCount 失败,{}", ID,  ExceptionUtils.getSubStringMsg(t));
        }
    }

    @Override
    public void offlinePageByAccId(OfflinePageByAccIdReq request, StreamObserver<OfflinePageByAccIdReply> responseObserver) {
        int accountId = request.getAccountId();
        log.info("offlinePageByAccId request {}", request);
        CompletableFuture.supplyAsync(() -> {
            //异步下线
            List<MgkLandingPagePo> mgkLandingPagePos = mgkLandingPageRepo.queryPageByAcc(accountId);
            List<Long> pageIds = mgkLandingPagePos.stream().map(MgkLandingPagePo::getPageId).collect(Collectors.toList());
            // batchDownline
            try {
                List<Long> offlinePageIds = mgkLandingPageService.batchDownline(com.bilibili.adp.common.bean.Operator.SYSTEM, pageIds);
                log.info("offlinePageByAccId success {}, {}", request, offlinePageIds);
                return offlinePageIds;
            } catch (Exception e) {
                log.error("offlinePageByAccId error ", e);
            }
            return Collections.emptyList();
        });

        responseObserver.onNext(OfflinePageByAccIdReply.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void batchQueryPageCount(BatchQueryPageCountReq request, StreamObserver<QueryPageCountReply> responseObserver) {
        try {
            Assert.notNull((request), "查询参数不可为空");

            Integer count = mgkLandingPageRepo.batchQueryPageCount(request.getAccountIdsList(), request.getBeginTime(), request.getEndTime());

            responseObserver.onNext(QueryPageCountReply.newBuilder().setCount(count).build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}: batchQueryPageCount 失败,{}", ID,  ExceptionUtils.getSubStringMsg(t));
        } catch (Exception t) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("{}:batchQueryPageCount 失败,{}", ID,  ExceptionUtils.getSubStringMsg(t));
        }
    }

    @Override
    public void publish(PublishReq request, StreamObserver<PublishReply> responseObserver) {
        try {
            mgkLandingPageService.publish(buildOperator(request), request.getPageId());
            responseObserver.onNext(PublishReply.newBuilder().setMsg("success").build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("publish error ", e);
            responseObserver.onError(e);
        }
    }

    @Override
    public void downLine(DownLineReq request, StreamObserver<DownLineReply> responseObserver) {
        try {
            mgkLandingPageService.downline(buildOperator(request), request.getPageId());
            responseObserver.onNext(DownLineReply.newBuilder().setMsg("success").build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("downLine error ", e);
            responseObserver.onError(e);
        }
    }

    private static com.bilibili.adp.common.bean.Operator buildOperator(PublishReq request) {
        com.bilibili.adp.common.bean.Operator operator = new com.bilibili.adp.common.bean.Operator();
        operator.setOperatorId(request.getOperator().getOperatorId());
        operator.setOperatorName(request.getOperator().getOperatorName());
        operator.setOperatorType(OperatorType.getByCode(request.getOperator().getOperatorType()));
        return operator;
    }

    private static com.bilibili.adp.common.bean.Operator buildOperator(DownLineReq request) {
        com.bilibili.adp.common.bean.Operator operator = new com.bilibili.adp.common.bean.Operator();
        operator.setOperatorId(request.getOperator().getOperatorId());
        operator.setOperatorName(request.getOperator().getOperatorName());
        operator.setOperatorType(OperatorType.getByCode(request.getOperator().getOperatorType()));
        return operator;
    }

    public List<Long> offlinePageByAccIdManual(Integer accountId) {
        //异步下线
        List<MgkLandingPagePo> mgkLandingPagePos = mgkLandingPageRepo.queryPageByAcc(accountId);
        List<Long> pageIds = mgkLandingPagePos.stream().map(MgkLandingPagePo::getPageId).collect(Collectors.toList());
        // batchDownline
        try {
            List<Long> offlinePageIds = mgkLandingPageService.batchDownline(com.bilibili.adp.common.bean.Operator.SYSTEM, pageIds);
            log.info("offlinePageByAccId success {}, {}", accountId, offlinePageIds);
            return offlinePageIds;
        } catch (Exception e) {
            log.error("offlinePageByAccId error ", e);
        }
        return Collections.emptyList();
    }
}
