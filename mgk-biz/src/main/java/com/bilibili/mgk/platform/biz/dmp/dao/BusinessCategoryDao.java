package com.bilibili.mgk.platform.biz.dmp.dao;

import com.bilibili.mgk.platform.biz.dmp.po.BusinessCategoryPo;
import com.bilibili.mgk.platform.biz.dmp.po.BusinessCategoryPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface BusinessCategoryDao {
    long countByExample(BusinessCategoryPoExample example);

    int deleteByExample(BusinessCategoryPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(BusinessCategoryPo record);

    int insertBatch(List<BusinessCategoryPo> records);

    int insertUpdateBatch(List<BusinessCategoryPo> records);

    int insert(BusinessCategoryPo record);

    int insertUpdateSelective(BusinessCategoryPo record);

    int insertSelective(BusinessCategoryPo record);

    List<BusinessCategoryPo> selectByExample(BusinessCategoryPoExample example);

    BusinessCategoryPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") BusinessCategoryPo record, @Param("example") BusinessCategoryPoExample example);

    int updateByExample(@Param("record") BusinessCategoryPo record, @Param("example") BusinessCategoryPoExample example);

    int updateByPrimaryKeySelective(BusinessCategoryPo record);

    int updateByPrimaryKey(BusinessCategoryPo record);
}