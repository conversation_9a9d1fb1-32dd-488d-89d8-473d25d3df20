package com.bilibili.mgk.platform.biz.ad.dao;

import com.bilibili.mgk.platform.biz.ad.po.ResAppIosPackageScreenshotPo;
import com.bilibili.mgk.platform.biz.ad.po.ResAppIosPackageScreenshotPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface ResAppIosPackageScreenshotDao {
    long countByExample(ResAppIosPackageScreenshotPoExample example);

    int deleteByExample(ResAppIosPackageScreenshotPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(ResAppIosPackageScreenshotPo record);

    int insertBatch(List<ResAppIosPackageScreenshotPo> records);

    int insertUpdateBatch(List<ResAppIosPackageScreenshotPo> records);

    int insert(ResAppIosPackageScreenshotPo record);

    int insertUpdateSelective(ResAppIosPackageScreenshotPo record);

    int insertSelective(ResAppIosPackageScreenshotPo record);

    List<ResAppIosPackageScreenshotPo> selectByExample(ResAppIosPackageScreenshotPoExample example);

    ResAppIosPackageScreenshotPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") ResAppIosPackageScreenshotPo record, @Param("example") ResAppIosPackageScreenshotPoExample example);

    int updateByExample(@Param("record") ResAppIosPackageScreenshotPo record, @Param("example") ResAppIosPackageScreenshotPoExample example);

    int updateByPrimaryKeySelective(ResAppIosPackageScreenshotPo record);

    int updateByPrimaryKey(ResAppIosPackageScreenshotPo record);
}