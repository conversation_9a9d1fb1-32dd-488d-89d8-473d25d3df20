package com.bilibili.mgk.platform.biz.ad.po;

import java.io.Serializable;
import java.sql.Timestamp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccIndustryTagPo implements Serializable {
    /**
     * 自增id
     */
    private Integer id;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 父级id
     */
    private Integer parentId;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 软删除0未删除1 删除
     */
    private Integer isDeleted;

    /**
     * 1:正常 0:封禁
     */
    private Integer status;

    private static final long serialVersionUID = 1L;
}