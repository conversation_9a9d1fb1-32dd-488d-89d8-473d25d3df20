package com.bilibili.mgk.platform.biz.service;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.common.util.func.QueryDslFunctions;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.mgk.platform.api.conversion.MgkSecretService;
import com.bilibili.mgk.platform.api.conversion.bos.MgkSecretBo;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

import static com.bilibili.mgk.platform.biz.dao.querydsl.QMgkSecret.mgkSecret;

@Slf4j
@Service
public class MgkSecretServiceImpl implements MgkSecretService {
    private static final String invalidMsg = "invalid";

    private final BaseQueryFactory bqf;
    private final LoadingCache<Integer, String> kvs;

    public MgkSecretServiceImpl(BaseQueryFactory bqf, @Value("${secret.cache.ttl.mins:10}") Integer ttlMins) {
        this.bqf = bqf;
        kvs = CacheBuilder.newBuilder()
                .expireAfterWrite(Duration.ofMinutes(ttlMins))
                .build(new CacheLoader<Integer, String>() {
                    @Override
                    public String load(@NonNull Integer accountId) {
                        log.info("mgkSecretService: loading key[{}]", accountId);
                        final String secret = get(accountId);
                        return Optional.ofNullable(secret).orElse(invalidMsg);
                    }
                });
    }

    @Override
    public String get(Integer accountId) {
        Assert.isTrue(Utils.isPositive(accountId), "获取密钥的账号ID不能为空");
        return bqf.from(mgkSecret)
                .where(mgkSecret.accountId.eq(accountId)
                        .and(mgkSecret.isDeleted.eq(IsDeleted.VALID.getCode())))
                .select(mgkSecret.secret)
                .fetchFirst();
    }

    @Override
    public void delete(Integer accountId) {
        if (!Utils.isPositive(accountId)) return;
        QueryDslFunctions.deleteIfExists(bqf, mgkSecret, mgkSecret.accountId.eq(accountId));
    }

    @Override
    public List<MgkSecretBo> refresh(Collection<Integer> accountIds) {
        accountIds.forEach(this::delete);
        final List<MgkSecretBo> mgkSecretBos = insertOrIgnore(accountIds);
        mgkSecretBos.forEach(x -> kvs.put(x.getAccountId(), x.getSecret()));
        return mgkSecretBos;
    }

    @Override
    @Transactional("mgkPlatformTransactionManager")
    public List<MgkSecretBo> insertOrIgnore(Collection<Integer> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) return Collections.emptyList();
        final List<MgkSecretBo> existingBos = bqf.selectFrom(mgkSecret)
                .where(mgkSecret.accountId.in(accountIds)
                        .and(mgkSecret.isDeleted.eq(IsDeleted.VALID.getCode())))
                .fetch(MgkSecretBo.class);
        final Set<Integer> existingAccountIds = existingBos.stream()
                .map(MgkSecretBo::getAccountId)
                .collect(Collectors.toSet());
        final List<MgkSecretBo> insertBos = accountIds.stream()
                .filter(x -> !existingAccountIds.contains(x))
                .map(x -> MgkSecretBo.builder()
                        .accountId(x)
                        .secret(UUID.randomUUID().toString())
                        .build()
                ).collect(Collectors.toList());
        if (insertBos.size() > 0) {
            final List<Long> ids = bqf.insert(mgkSecret).insertGetKeys(insertBos);
            final Iterator<Long> idsIterator = ids.iterator();
            for (MgkSecretBo insertBo : insertBos) {
                insertBo.setId(idsIterator.next());
                existingBos.add(insertBo);
            }
        }
        return existingBos;
    }

    @Override
    public String getCachedOrReload(Integer accountId) {
        return kvs.getUnchecked(accountId);
    }

    @Override
    public boolean isSecretValid(String secret) {
        return !Objects.equals(secret, invalidMsg);
    }
}
