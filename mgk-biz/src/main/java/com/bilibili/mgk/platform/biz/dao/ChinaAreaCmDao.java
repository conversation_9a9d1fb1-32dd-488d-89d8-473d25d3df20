package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.ChinaAreaCmPo;
import com.bilibili.mgk.platform.biz.po.ChinaAreaCmPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface ChinaAreaCmDao {
    long countByExample(ChinaAreaCmPoExample example);

    int deleteByExample(ChinaAreaCmPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(ChinaAreaCmPo record);

    int insertBatch(List<ChinaAreaCmPo> records);

    int insertUpdateBatch(List<ChinaAreaCmPo> records);

    int insert(ChinaAreaCmPo record);

    int insertUpdateSelective(ChinaAreaCmPo record);

    int insertSelective(ChinaAreaCmPo record);

    List<ChinaAreaCmPo> selectByExample(ChinaAreaCmPoExample example);

    ChinaAreaCmPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") ChinaAreaCmPo record, @Param("example") ChinaAreaCmPoExample example);

    int updateByExample(@Param("record") ChinaAreaCmPo record, @Param("example") ChinaAreaCmPoExample example);

    int updateByPrimaryKeySelective(ChinaAreaCmPo record);

    int updateByPrimaryKey(ChinaAreaCmPo record);
}