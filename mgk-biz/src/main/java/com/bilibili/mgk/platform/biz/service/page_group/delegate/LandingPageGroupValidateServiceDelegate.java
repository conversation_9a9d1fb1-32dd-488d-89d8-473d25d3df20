package com.bilibili.mgk.platform.biz.service.page_group.delegate;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.mgk.platform.api.landing_page.dto.MgkLandingPageStatusBaseDto;
import com.bilibili.mgk.platform.api.landing_page.dto.QueryLandingPageParamDto;
import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageService;
import com.bilibili.mgk.platform.api.landing_page_group.dto.LandingPageGroupCreateDto;
import com.bilibili.mgk.platform.api.landing_page_group.dto.LandingPageGroupDto;
import com.bilibili.mgk.platform.api.landing_page_group.dto.LandingPageGroupUpdateDto;
import com.bilibili.mgk.platform.api.landing_page_group.dto.QueryLandingPageGroupDto;
import com.bilibili.mgk.platform.api.landing_page_group.dto.mapping.LandingPageGroupMappingDto;
import com.bilibili.mgk.platform.api.landing_page_group.dto.mapping.LandingPageGroupMappingListDto;
import com.bilibili.mgk.platform.api.landing_page_group.dto.mapping.LandingPageGroupMappingSaveDto;
import com.bilibili.mgk.platform.api.landing_page_group.dto.mapping.QueryLandingPageGroupMappingDto;
import com.bilibili.mgk.platform.api.landing_page_group.service.ILandingPageGroupService;
import com.bilibili.mgk.platform.api.landing_page_group.service.mapping.ILandingPageGroupMappingService;
import com.bilibili.mgk.platform.api.third_party.page.dto.MgkThirdPartyPageDto;
import com.bilibili.mgk.platform.api.third_party.page.dto.QueryMgkThirdPartyPageDto;
import com.bilibili.mgk.platform.api.third_party.page.service.IMgkThirdPartyPageService;
import com.bilibili.mgk.platform.biz.service.config.LandingPageGroupConfig;
import com.bilibili.mgk.platform.common.LandingPageStatusEnum;
import com.bilibili.mgk.platform.common.TemplateStyleEnum;
import com.bilibili.mgk.platform.common.WhetherEnum;
import com.bilibili.mgk.platform.common.page_group.PageGroupSourceEnum;
import com.bilibili.mgk.platform.common.page_group.PageGroupStatusEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName LandingPageGroupValidateServiceDelegate
 * <AUTHOR>
 * @Date 2023/5/22 4:26 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class LandingPageGroupValidateServiceDelegate {

    @Autowired
    private ILandingPageGroupService landingPageGroupService;
    @Autowired
    private ILandingPageGroupMappingService landingPageGroupMappingService;
    @Autowired
    private LandingPageGroupConfig landingPageGroupConfig;
    @Autowired
    private IMgkLandingPageService mgkLandingPageService;
    @Autowired
    private IMgkThirdPartyPageService mgkThirdPartyPageService;

    public void validateCreateDto(LandingPageGroupCreateDto createDto, int accountId) {
        Assert.isTrue(!StringUtils.isEmpty(createDto.getName()), "落地页组名称不可为空");
        PageGroupSourceEnum.getByCode(createDto.getGroupSource());
        validateMappingSaveDto(createDto.getMappingSaveDto(), accountId);
    }

    public void validateUpdateDtoAndFillGroupSource(LandingPageGroupUpdateDto updateDto, int accountId) {
        Assert.isTrue(Utils.isPositive(updateDto.getGroupId()), "落地页组id不可为空");
        Assert.isTrue(!StringUtils.isEmpty(updateDto.getName()), "落地页组名称不可为空");
        int groupSource = validateLandingPageGroupAccount(updateDto.getGroupId(), accountId);
        updateDto.getMappingSaveDto().setGroupSource(groupSource);
        validateMappingSaveDto(updateDto.getMappingSaveDto(), accountId);
    }

    private void validateMappingSaveDto(LandingPageGroupMappingSaveDto mappingSaveDto, int accountId) {
        Assert.notNull(mappingSaveDto, "落地页组绑定落地页对象不可为空");
        Assert.isTrue(!CollectionUtils.isEmpty(mappingSaveDto.getMappingList()), "落地页组绑定落地页不可空");
        Assert.isTrue(mappingSaveDto.getMappingList().size() >= 2, "落地页组最少绑定2个落地页");
        Assert.isTrue(mappingSaveDto.getMappingList().size() <= landingPageGroupConfig.getLandingPageGroupMappingMaxSize(),
                "落地页组最多绑定" + landingPageGroupConfig.getLandingPageGroupMappingMaxSize() + "个落地页");
        PageGroupSourceEnum groupSourceEnum = PageGroupSourceEnum.getByCode(mappingSaveDto.getGroupSource());
        boolean isMgkSource = groupSourceEnum.equals(PageGroupSourceEnum.MGK_SOURCE);
        validateSaveDtoList(mappingSaveDto, accountId, isMgkSource);
    }

    public void validateQueryDto(QueryLandingPageGroupDto queryDto) {
        Assert.isTrue(Utils.isPositive(queryDto.getAccountId())
                || !CollectionUtils.isEmpty(queryDto.getGroupIdList())
                || !CollectionUtils.isEmpty(queryDto.getAuditCreativeIdList()),
                "查询条件不足");
        if (!CollectionUtils.isEmpty(queryDto.getStatusList())) {
            queryDto.getStatusList().forEach(PageGroupStatusEnum::getByCode);
        }
    }

    public void validateQueryPageDto(QueryLandingPageGroupDto queryDto) {
        Assert.isTrue(Utils.isPositive(queryDto.getAccountId())
                || !CollectionUtils.isEmpty(queryDto.getGroupIdList())
                || !CollectionUtils.isEmpty(queryDto.getAuditCreativeIdList()),
                "查询条件不足");
        Assert.isTrue(Objects.nonNull(queryDto.getPage()), "分页参数不可为空");
        if (!CollectionUtils.isEmpty(queryDto.getStatusList())) {
            queryDto.getStatusList().forEach(PageGroupStatusEnum::getByCode);
        }
    }

    public int validateLandingPageGroupAccount(Long groupId, int accountId) {
        Assert.isTrue(Utils.isPositive(groupId), "更新目标落地页组id不可为空");
        QueryLandingPageGroupDto queryDto = QueryLandingPageGroupDto.builder()
                .groupIdList(Lists.newArrayList(groupId))
                .accountId(accountId)
                .build();
        List<LandingPageGroupDto> landingPageGroupDtos = landingPageGroupService.queryLandingPageGroup(queryDto);
        Assert.notEmpty(landingPageGroupDtos, "当前落地页组不存在或已被删除");
        return landingPageGroupDtos.get(0).getGroupSource();
    }

    public void validateSaveDtoList(LandingPageGroupMappingSaveDto saveDto, int accountId, boolean isMgkSource) {
        List<LandingPageGroupMappingListDto> mappingList = saveDto.getMappingList();
        if (CollectionUtils.isEmpty(mappingList)) {
            return;
        }
        Assert.isTrue(mappingList.stream()
                .anyMatch(mappingDto -> Utils.isPositive(mappingDto.getIsEnable())),
                "落地页组内开关不可全部关闭");
        if (isMgkSource) {
            validateMgkPageMappingList(mappingList, accountId, saveDto.getGroupId());
        } else {
            validateMgkThirdPartyMappingList(mappingList, accountId);
            List<String> pageUrlList = mappingList.stream()
                    .map(LandingPageGroupMappingListDto::getPageUrl)
                    .distinct()
                    .collect(Collectors.toList());
            Assert.isTrue(pageUrlList.size() == mappingList.size(), "三方落地页链接不允许重复");
            mappingList.forEach(mappingDto -> {
                Assert.isTrue(!StringUtils.isEmpty(mappingDto.getPageUrl()), "三方落地页具体链接不可为空");
                Assert.isTrue(!StringUtils.isEmpty(mappingDto.getName()), "三方落地页链接别名不可为空");
                Assert.isTrue(Objects.nonNull(mappingDto.getIsEnable()), "落地页组内落地页是否可用不可为空");
                WhetherEnum.getByCode(mappingDto.getIsEnable());
            });
        }
    }

    private void validateMgkPageMappingList(List<LandingPageGroupMappingListDto> mappingList, int accountId, Long groupId) {
        if (CollectionUtils.isEmpty(mappingList)) {
            return;
        }
        List<Long> pageIdList = mappingList
                .stream()
                .map(LandingPageGroupMappingListDto::getPageId)
                .filter(Utils::isPositive)
                .collect(Collectors.toList());
        mappingList.forEach(mappingDto -> {
            Assert.isTrue(Utils.isPositive(mappingDto.getPageId()), "建站落地页id不可为空");
            Assert.isTrue(Objects.nonNull(mappingDto.getIsEnable()), "落地页组内落地页是否可用不可为空");
            WhetherEnum.getByCode(mappingDto.getIsEnable());
        });
        QueryLandingPageParamDto queryDto = QueryLandingPageParamDto.builder()
                .accountIdList(Lists.newArrayList(accountId))
                .pageIdList(pageIdList)
                .statusList(Lists.newArrayList(LandingPageStatusEnum.PUBLISHED.getCode()))
                .templateStyleList(Lists.newArrayList(TemplateStyleEnum.APPLETS.getCode()))
                .build();
        List<MgkLandingPageStatusBaseDto> statusBaseDtoList = mgkLandingPageService.getLandingPageStatusBaseDtoList(queryDto);
        Assert.isTrue(statusBaseDtoList.size() == pageIdList.size(),
                "部分绑定建站落地页无效,请检查落地页状态为已发布&落地页类型为小程序");
        Assert.isTrue(mappingList.stream()
                .anyMatch(mappingDto -> Utils.isPositive(mappingDto.getIsEnable())),
                "落地页组内需至少包含一个开关打开的已发布落地页");
        QueryLandingPageGroupMappingDto queryMappingDto = QueryLandingPageGroupMappingDto.builder()
                .pageIdList(pageIdList)
                .groupSource(PageGroupSourceEnum.MGK_SOURCE.getCode())
                .build();
        List<LandingPageGroupMappingDto> existMgkPageMappingDtoList = landingPageGroupMappingService.queryPageGroupMappingDto(queryMappingDto);
        Integer maxSize = landingPageGroupConfig.getLandingPageGroupMappingMaxSize();
        Map<Long, List<LandingPageGroupMappingDto>> mgkPageIdMappingMap = existMgkPageMappingDtoList.stream()
                .filter(mappingDto -> !mappingDto.getGroupId().equals(groupId))
                .collect(Collectors.groupingBy(LandingPageGroupMappingDto::getPageId));
        List<Long> overLimitPageIdList = mgkPageIdMappingMap.keySet().stream()
                .filter(key -> mgkPageIdMappingMap.get(key).size() >= maxSize)
                .collect(Collectors.toList());
        Assert.isTrue(CollectionUtils.isEmpty(overLimitPageIdList),
                "落地页" + overLimitPageIdList.toString() + "最多绑定落地页组不能超过" + maxSize + "个，请检查配置");
    }

    private void validateMgkThirdPartyMappingList(List<LandingPageGroupMappingListDto> mappingList, int accountId) {
        List<Long> pageIdList = mappingList
                .stream()
                .map(LandingPageGroupMappingListDto::getPageId)
                .filter(Utils::isPositive)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pageIdList)) {
            return;
        }
        QueryMgkThirdPartyPageDto queryDto = QueryMgkThirdPartyPageDto.builder()
                .accountId(accountId)
                .pageIdList(pageIdList)
                .build();
        List<MgkThirdPartyPageDto> mgkThirdPartyPageDtoList = mgkThirdPartyPageService.queryMgkThirdPartyPageList(queryDto);
        Map<Long, String> existThirdPagePartyMap = mgkThirdPartyPageDtoList.stream()
                .collect(Collectors.toMap(MgkThirdPartyPageDto::getPageId, MgkThirdPartyPageDto::getPageUrl));
        Assert.isTrue(mgkThirdPartyPageDtoList.size() == pageIdList.size(), "部分绑定三方落地页无效");
        mappingList.forEach(mappingDto -> {
            if (!Utils.isPositive(mappingDto.getPageId())) {
                return;
            }
            String pageUrl = existThirdPagePartyMap.get(mappingDto.getPageId());
            Assert.isTrue(pageUrl.equals(mappingDto.getPageUrl()), "三方落地页链接错误");
        });
    }

}
