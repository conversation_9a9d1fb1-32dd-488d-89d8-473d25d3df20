package com.bilibili.mgk.platform.biz.service.page_group.impl;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.mgk.platform.api.landing_page_group.dto.mapping.LandingPageGroupMappingAuditDto;
import com.bilibili.mgk.platform.api.landing_page_group.dto.mapping.LandingPageGroupMappingDto;
import com.bilibili.mgk.platform.api.landing_page_group.dto.mapping.LandingPageGroupMappingSaveDto;
import com.bilibili.mgk.platform.api.landing_page_group.dto.mapping.QueryLandingPageGroupMappingDto;
import com.bilibili.mgk.platform.api.landing_page_group.service.mapping.ILandingPageGroupMappingService;
import com.bilibili.mgk.platform.biz.service.page_group.delegate.LandingPageGroupMappingServiceDelegate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName LandingPageGroupMappingServiceImpl
 * <AUTHOR>
 * @Date 2023/5/17 8:21 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class LandingPageGroupMappingServiceImpl implements ILandingPageGroupMappingService {

    @Autowired
    private LandingPageGroupMappingServiceDelegate landingPageGroupMappingServiceDelegate;

    @Override
    public boolean savePageGroupMapping(LandingPageGroupMappingSaveDto saveDto, Operator operator) {
        return landingPageGroupMappingServiceDelegate.savePageGroupMapping(saveDto, operator);
    }

    @Override
    public void deletePageGroupMapping(Long pageGroupId) {
        landingPageGroupMappingServiceDelegate.deletePageGroupMapping(pageGroupId);
    }

    @Override
    public void updateTemplatePageUrl(List<Long> idList, Long pageId) {
        landingPageGroupMappingServiceDelegate.updateTemplatePageUrl(idList, pageId);
    }

    @Override
    public List<LandingPageGroupMappingDto> queryPageGroupMappingDto(QueryLandingPageGroupMappingDto queryDto) {
        return landingPageGroupMappingServiceDelegate.queryPageGroupMappingDto(queryDto);
    }

    @Override
    public List<LandingPageGroupMappingDto> queryPageGroupMappingList(Long groupId) {
        return landingPageGroupMappingServiceDelegate.queryPageGroupMappingList(groupId);
    }

    @Override
    public void auditPageGroupMappingList(Long pageGroupId, List<LandingPageGroupMappingAuditDto> mappingAuditDtoList) {
        landingPageGroupMappingServiceDelegate.auditPageGroupMappingList(pageGroupId, mappingAuditDtoList);
    }

    @Override
    public void batchRejectPageGroupMappingStatus(List<Long> pageIdList, List<Long> pageGroupIdList, String reason) {
        landingPageGroupMappingServiceDelegate.batchRejectPageGroupMappingStatus(pageIdList, pageGroupIdList, reason);
    }

    @Override
    public void batchUpdatePageGroupMappingUsing(List<Long> pageGroupIdList) {
        landingPageGroupMappingServiceDelegate.batchUpdatePageGroupMappingUsing(pageGroupIdList);
    }
}
