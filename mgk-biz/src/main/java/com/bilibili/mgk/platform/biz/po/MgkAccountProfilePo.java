package com.bilibili.mgk.platform.biz.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkAccountProfilePo implements Serializable {
    /**
     * 自增id
     */
    private Integer id;

    /**
     * 账号ID
     */
    private Integer accountId;

    /**
     * 是否允许代理商获取表单数据
     */
    private Integer allowAgentGetFormData;

    /**
     * 软删除: 0-有效 1-删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}