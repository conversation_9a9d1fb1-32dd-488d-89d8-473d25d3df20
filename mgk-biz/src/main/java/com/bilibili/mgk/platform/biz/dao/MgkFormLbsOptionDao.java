package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkFormLbsOptionPo;
import com.bilibili.mgk.platform.biz.po.MgkFormLbsOptionPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MgkFormLbsOptionDao {
    long countByExample(MgkFormLbsOptionPoExample example);

    int deleteByExample(MgkFormLbsOptionPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MgkFormLbsOptionPo record);

    int insertBatch(List<MgkFormLbsOptionPo> records);

    int insertUpdateBatch(List<MgkFormLbsOptionPo> records);

    int insert(MgkFormLbsOptionPo record);

    int insertUpdateSelective(MgkFormLbsOptionPo record);

    int insertSelective(MgkFormLbsOptionPo record);

    List<MgkFormLbsOptionPo> selectByExample(MgkFormLbsOptionPoExample example);

    MgkFormLbsOptionPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MgkFormLbsOptionPo record, @Param("example") MgkFormLbsOptionPoExample example);

    int updateByExample(@Param("record") MgkFormLbsOptionPo record, @Param("example") MgkFormLbsOptionPoExample example);

    int updateByPrimaryKeySelective(MgkFormLbsOptionPo record);

    int updateByPrimaryKey(MgkFormLbsOptionPo record);
}