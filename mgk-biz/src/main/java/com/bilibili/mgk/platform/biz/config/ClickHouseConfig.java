package com.bilibili.mgk.platform.biz.config;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import ru.yandex.clickhouse.ClickHouseConnection;
import ru.yandex.clickhouse.ClickHouseDriver;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

import java.sql.SQLException;

/**
 * @file: DtuidConfig
 * @author: gaoming
 * @date: 2021/06/18
 * @version: 1.0
 * @description:
 **/

@Configuration
public class ClickHouseConfig {

    @Value("${new.clickhouse.url:*******************************************}")
    private String ck_url;
    @Value("${new.clickhouse.username:jssz_bigdata_cluster_replica}")
    private String ck_user;
    @Value("${new.clickhouse.passport:0066355d}")
    private String ck_passport;

    @Bean(name = "clickHouseDataProperties")
    public ClickHouseProperties clickHouseDataProperties(){
        ClickHouseProperties properties = new ClickHouseProperties();
        properties.setConnectionTimeout(5 * 1000);
        properties.setSocketTimeout(30 * 1000);
        properties.setUser(ck_user);
        properties.setPassword(ck_passport);
        return properties;
    }

    @Bean(name = "clickHouseConnection")
    public ClickHouseConnection clickHouseConnection(ClickHouseProperties clickHouseDataProperties) throws SQLException {
        ClickHouseDriver driver = new ClickHouseDriver();
        return driver.connect(ck_url, clickHouseDataProperties);
    }

}
