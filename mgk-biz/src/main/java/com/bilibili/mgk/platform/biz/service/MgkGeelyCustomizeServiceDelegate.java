package com.bilibili.mgk.platform.biz.service;

import com.alibaba.cloudapi.sdk.enums.HttpMethod;
import com.alibaba.cloudapi.sdk.enums.Scheme;
import com.alibaba.cloudapi.sdk.model.ApiRequest;
import com.alibaba.cloudapi.sdk.model.HttpClientBuilderParams;
import com.alibaba.cloudapi.sdk.util.HttpCommonUtil;
import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.mgk.platform.api.data.dto.FormItemDataDto;
import com.bilibili.mgk.platform.api.data.dto.ReportDataDto;
import com.bilibili.mgk.platform.biz.bean.GeelyPostResponseDto;
import com.bilibili.mgk.platform.biz.dao.LbsAreaDao;
import com.bilibili.mgk.platform.biz.dao.MgkFormDataExtraDao;
import com.bilibili.mgk.platform.biz.dao.MgkFormGeelyReportDao;
import com.bilibili.mgk.platform.biz.po.*;
import com.bilibili.mgk.platform.biz.service.geelyExcelImport.ExcelRowDto;
import com.bilibili.mgk.platform.biz.service.geelyExcelImport.OptionDto;
import com.bilibili.mgk.platform.biz.service.geelyExcelImport.OptionNode;
import com.bilibili.mgk.platform.biz.utils.AliyunApiSignUtil;
import com.bilibili.mgk.platform.biz.utils.MyOkHttp3Client;
import com.bilibili.mgk.platform.common.MgkConstants;
import com.bilibili.mgk.platform.common.MgkEnvironmentEnum;
import com.bilibili.mgk.platform.common.WhetherEnum;
import com.bilibili.mgk.platform.common.utils.GeelyOKHttpClientUtil;
import com.bilibili.mgk.platform.common.utils.excel.ExcelReadUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName MgkGeelyCustomizeServiceDelegate
 * <AUTHOR>
 * @Date 2022/5/4 10:21 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class MgkGeelyCustomizeServiceDelegate {

    private static final String LABEL_NAME = "姓名";
    private static final String LABEL_MOBILE = "手机号";
    private static final String LABEL_TYPE = "预约车型";
    private static final String LABEL_AGENT = "经销商信息";
    private static final String GEELY_URL_SUFFIX = "/api/v1/lead/mediaLeadInput";

    @Resource
    private LbsAreaDao areaDao;

    @Autowired
    private MgkFormGeelyReportDao mgkFormGeelyReportDao;

    @Autowired
    private MgkFormDataExtraDao mgkFormDataExtraDao;

    @Value("${mgk.geely.api.host:lead-vma-uat.geely.com}")
    private String geelyApiHost;
    @Value("${mgk.geely.api.app.key:203738801}")
    private String geelyApiAppKey;
    @Value("${mgk.geely.api.secret.key:8jhgkscr7ap14ve4n31u83nzzl04l8dm}")
    private String geelyApiSecretKey;
    @Value("${mgk.geely.lead.source.code:ZTSJ0012}")
    private String leadSourceCode;
    @Value("${mgk.environment:1}")
    private Integer mgkEnvironment;
    @Value("${mgk.geely.activity.code}")
    private String mgkGeelyActivityCode;
    @Value("#{'${mgk.geely.need.model.form.ids:0}'.split(',')}")
    private List<Long> needModelFormIds;

    private final Map<Long, String> mgkGeelyActivityCodeMap;
    private final Map<Long, String> leadSourceCodeMap;

    public MgkGeelyCustomizeServiceDelegate(@Value("${mgk.geely.lead.source.code.map}") String leadSourceCodeMapStr,
                                            @Value("${mgk.geely.activity.code.map}") String mgkGeelyActivityCodeMapStr) {
        this.leadSourceCodeMap = buildFormIdConfigMap(leadSourceCodeMapStr);
        this.mgkGeelyActivityCodeMap = buildFormIdConfigMap(mgkGeelyActivityCodeMapStr);
    }

    public static Map<Long, String> buildFormIdConfigMap(String configStr) {
        if (StringUtils.isEmpty(configStr)) {
            return new HashMap<>();
        }
        String[] configStrArray = configStr.split(";");
        return Arrays.stream(configStrArray).map(v -> {
            return v.split(",");
        }).collect(Collectors.toMap(o -> Long.valueOf(o[0]), o -> o[1]));
    }

    public Integer reportGeelyData(Long formDataId, ReportDataDto reportDataDto) {
        if (!MgkConstants.MGK_FORM_CUSTOMIZE_GEELY.equals(reportDataDto.getCustomize())) {
            return 0;
        }

        // 反作弊
        if (formDataId == 0L || WhetherEnum.YES.getCode().equals(reportDataDto.getIsCheat())) {
            return 0;
        }

        List<FormItemDataDto> formItemDataList = reportDataDto.getFormData();
        Map<String, FormItemDataDto> dataLabelMap = formItemDataList.stream()
                .collect(Collectors.toMap(FormItemDataDto::getLabel, Function.identity()));
        FormItemDataDto nameData = dataLabelMap.get(LABEL_NAME);
        FormItemDataDto mobileData = dataLabelMap.get(LABEL_MOBILE);
        FormItemDataDto typeData = dataLabelMap.get(LABEL_TYPE);
        FormItemDataDto agentData = dataLabelMap.get(LABEL_AGENT);

        if (Objects.isNull(nameData)
                || Objects.isNull(mobileData)
                || Objects.isNull(typeData)
                || Objects.isNull(agentData)) {
            log.info("吉利汽车表单所需数据不足, dataId:{}", formDataId);
            return 0;
        }

        MgkFormGeelyReportPo recordPo = MgkFormGeelyReportPo
                .builder()
                .formDataId(formDataId)
                .formId(reportDataDto.getFormId())
                .build();
        try {
            Scheme scheme = Scheme.HTTP;
            String name = nameData.getValue(),
                    mobile = mobileData.getValue(),
                    seriesCode = typeData.getCode(),
                    seriesValue = typeData.getValue();
            String[] agentCodeArr = agentData.getCode().split(",");
            String cityCode = agentCodeArr[1],
                    dealerCode = agentCodeArr[2];
            Map<String, String> querys = new HashMap<>();
            querys.put(MgkConstants.GEELY_LEAD_SOURCE_CODE, leadSourceCodeMap.getOrDefault(reportDataDto.getFormId(), leadSourceCode));
            querys.put(MgkConstants.GEELY_NAME, name);
            querys.put(MgkConstants.GEELY_MOBILE, mobile);
            querys.put(MgkConstants.GEELY_CITY_CODE, cityCode);
            querys.put(MgkConstants.GEELY_DEALER_CODE, dealerCode);
            querys.put(MgkConstants.GEELY_SERIES_CODE, seriesCode);
            if (MgkEnvironmentEnum.PROD.getCode().equals(mgkEnvironment) ||
                    MgkEnvironmentEnum.PRE.getCode().equals(mgkEnvironment)) {
                querys.put(MgkConstants.GEELY_ACTIVITY_CODE, mgkGeelyActivityCodeMap.getOrDefault(reportDataDto.getFormId(), mgkGeelyActivityCode));
            }
            if (needModelFormIds.contains(reportDataDto.getFormId())) {
                querys.put(MgkConstants.GEELY_MODEL_CODE, generateModelCode(seriesValue, seriesCode));
            }
            log.info("geely query :{}", querys);

            ApiRequest apiRequest = new ApiRequest(HttpMethod.POST_BODY, GEELY_URL_SUFFIX, JSON.toJSONString(querys).getBytes(StandardCharsets.UTF_8));
            apiRequest.setHost(geelyApiHost);
            apiRequest.setScheme(scheme);

            MyOkHttp3Client client = new MyOkHttp3Client();
            HttpClientBuilderParams params = new HttpClientBuilderParams();
            params.setAppKey(geelyApiAppKey);
            params.setAppSecret(geelyApiSecretKey);
            params.setHost(geelyApiHost);
            params.setScheme(scheme);
            client.init(params);

            AliyunApiSignUtil.make(apiRequest, geelyApiAppKey, geelyApiSecretKey);
            Request request = AliyunApiSignUtil.generateRequest(apiRequest);
            GeelyPostResponseDto responseDto = GeelyOKHttpClientUtil.callForObject(request, GeelyPostResponseDto.class);
            if (Objects.isNull(responseDto) || !responseDto.getSuccess()) {
                log.error("geely report failed, formDataId:{}", formDataId);
                recordPo.setIsFinished(WhetherEnum.NO.getCode());
            } else {
                recordPo.setIsFinished(WhetherEnum.YES.getCode());
                if (Objects.nonNull(responseDto.getObject()) && !StringUtils.isEmpty(responseDto.getObject().getLeadId())) {
                    recordPo.setLeadId(Long.parseLong(responseDto.getObject().getLeadId()));
                }
            }
        } catch (Exception e) {
            log.error("geely report url failed, formDataId:{}", formDataId, e);
        } finally {
            if (!WhetherEnum.YES.getCode().equals(recordPo.getIsFinished())) {
                recordPo.setIsFinished(WhetherEnum.NO.getCode());
            }
            mgkFormGeelyReportDao.insertUpdateSelective(recordPo);
        }
        return 0;
    }

    private String generateModelCode(String value, String seriesCode) {
        if (StringUtils.isEmpty(value) || StringUtils.isEmpty(seriesCode)) {
            return "";
        }

        String[] split = seriesCode.split("-");
        if (split.length < 2) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        sb.append(split[1]);
        if (value.contains("EM-F")) {
            sb.append("EM-F");
            return sb.toString();
        }
        if (value.contains("EM-P")) {
            sb.append("EM-P");
            return sb.toString();
        }
        if (value.toLowerCase().contains("hatchback")
                || value.toLowerCase().contains("hb")) {
            sb.append("HB");
            return sb.toString();
        }
        if (value.contains("+C")) {
            sb.append("+C");
            return sb.toString();
        }
        if (value.contains("+")) {
            sb.append("+");
            return sb.toString();
        }
        if (value.contains("PHEV")) {
            if (split[1].equals("05")) {
                sb.append(" ");
            }
            sb.append("PHEV");
            return sb.toString();
        }
        if (value.contains("MH")) {
            sb.append("MH");
            return sb.toString();
        }
        if (value.contains("E371")) {
            sb.append("-EV");
            return sb.toString();
        }
        return sb.toString();
    }

    public void refreshGeelyReportRecord() {
        Integer startId = 0;
        List<MgkFormGeelyReportPo> reportPos = getGeelyReportPosByPage(startId);
        while (!CollectionUtils.isEmpty(reportPos)) {
            List<Long> formDataIds = reportPos.stream()
                    .map(MgkFormGeelyReportPo::getFormDataId)
                    .collect(Collectors.toList());
            reportRecordsByFormDataIds(formDataIds);
            startId = reportPos.get(reportPos.size() - 1).getId();
            reportPos = getGeelyReportPosByPage(startId);
        }
    }

    private List<MgkFormGeelyReportPo> getGeelyReportPosByPage(Integer startId) {
        MgkFormGeelyReportPoExample exm = new MgkFormGeelyReportPoExample();
        exm.or().andIdGreaterThan(startId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIsFinishedEqualTo(WhetherEnum.NO.getCode());
        exm.setLimit(1000);
        exm.setOrderByClause("id asc");
        return mgkFormGeelyReportDao.selectByExample(exm);
    }

    private void reportRecordsByFormDataIds(List<Long> formDataIds) {
        MgkFormDataExtraPoExample exm = new MgkFormDataExtraPoExample();
        exm.or().andIsCheatEqualTo(WhetherEnum.NO.getCode())
                .andFormDataIdIn(formDataIds)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MgkFormDataExtraPo> pos = mgkFormDataExtraDao.selectByExample(exm);
        if (CollectionUtils.isEmpty(pos)) {
            return;
        }
        Map<Long, List<MgkFormDataExtraPo>> extraPoMap = pos.stream()
                .collect(Collectors.groupingBy(MgkFormDataExtraPo::getFormDataId));
        extraPoMap.keySet().forEach(formDataId -> {
            List<MgkFormDataExtraPo> extraPos = extraPoMap.get(formDataId);
            List<FormItemDataDto> formItemDataDtos = extraPos.stream().map(po -> FormItemDataDto.builder()
                    .id(po.getFormItemId())
                    .code(po.getCode())
                    .label(po.getLabel())
                    .value(po.getValue())
                    .build()).collect(Collectors.toList());
            Long formId = extraPos.get(0).getFormId();
            ReportDataDto reportDataDto = ReportDataDto.builder()
                    .formId(formId)
                    .isCheat(WhetherEnum.NO.getCode())
                    .customize(MgkConstants.MGK_FORM_CUSTOMIZE_GEELY)
                    .formData(formItemDataDtos)
                    .build();
            reportGeelyData(formDataId, reportDataDto);
        });
    }

    /**
     * 未来调试用 但是是对方线上环境 不能随便调用！
     */
    public static void main1(String[] args) {

        // 线上环境
        String appKey = "204027896";
        String secret = "4BNtWjPYmshXpRHJh57hLF87aD0AW8Nc";
        String host = "lead-vma.lynkco.com.cn";
        Scheme scheme = Scheme.HTTP;

        Map<String, String> querys = new HashMap<>();
        // 线上环境
        querys.put("leadSourceCode", "NRYX0007");

        // 通用
        querys.put("activityCode", "NRYX20220406001");
        querys.put("name", "调试验签");
        querys.put("mobile", "13711110011");
        querys.put("cityCode", "310100");
        querys.put("dealerCode", "407021");
        querys.put("seriesCode", "Lynkco-01");

        ApiRequest apiRequest = new ApiRequest(HttpMethod.POST_BODY, GEELY_URL_SUFFIX, JSON.toJSONString(querys).getBytes(StandardCharsets.UTF_8));
        apiRequest.setHost(host);
        apiRequest.setScheme(scheme);

        MyOkHttp3Client client = new MyOkHttp3Client();
        HttpClientBuilderParams params = new HttpClientBuilderParams();
        params.setAppKey(appKey);
        params.setAppSecret(secret);
        params.setHost(host);
        params.setScheme(scheme);
        client.init(params);

        AliyunApiSignUtil.make(apiRequest, appKey, secret);
        RequestBody requestBody = null;
        if (null != apiRequest.getFormParams() && apiRequest.getFormParams().size() > 0) {
            requestBody = RequestBody.create(MediaType.parse(apiRequest.getFirstHeaderValue("content-type")), HttpCommonUtil.buildParamString(apiRequest.getFormParams()));
        } else if (null != apiRequest.getBody() && apiRequest.getBody().length > 0) {
            requestBody = RequestBody.create(MediaType.parse(apiRequest.getFirstHeaderValue("content-type")), apiRequest.getBody());
        }

        Request request = (new Request.Builder()).method(apiRequest.getMethod().getValue(), requestBody).url(apiRequest.getUrl()).headers(getHeadersFromMap(apiRequest.getHeaders())).build();
        System.out.println(GeelyOKHttpClientUtil.callForString(request));
    }

    private static Headers getHeadersFromMap(Map<String, List<String>> map) {
        List<String> nameAndValues = new ArrayList();
        Iterator iterator = map.entrySet().iterator();

        while (iterator.hasNext()) {
            Map.Entry<String, List<String>> entry = (Map.Entry) iterator.next();
            Iterator entryIterator = ((List) entry.getValue()).iterator();

            while (entryIterator.hasNext()) {
                String value = (String) entryIterator.next();
                nameAndValues.add(entry.getKey());
                nameAndValues.add(value);
            }
        }

        return Headers.of((String[]) nameAndValues.toArray(new String[nameAndValues.size()]));
    }

    //吉利经销商Excel生成
    public String geelyExcelProcess(File file) {
        LbsAreaPoExample provinceExample = new LbsAreaPoExample();
        provinceExample.createCriteria().andLevelEqualTo(1);
        List<LbsAreaPo> provinces = areaDao.selectByExample(provinceExample);
        Map<String, String> provinceCodeMap = provinces.stream().collect(Collectors.toMap(LbsAreaPo::getName, LbsAreaPo::getAreaCode));

        LbsAreaPoExample cityprovinceExample = new LbsAreaPoExample();
        cityprovinceExample.createCriteria().andLevelEqualTo(2);
        List<LbsAreaPo> citys = areaDao.selectByExample(cityprovinceExample);
        Map<String, String> cityCodeMap = citys.stream().collect(Collectors.toMap(LbsAreaPo::getName, LbsAreaPo::getAreaCode));

        OptionDto rootOptionDto = new OptionDto();
        try {
            List<ExcelRowDto> excelRowDtos = new ArrayList<>();
            List<Object[]> contentList = ExcelReadUtil.readExcel(file);
            contentList.remove(0);
            for (Object[] objects : contentList) {
                String provinceName = objects[2].toString();
                String cityName = objects[4].toString();
                String cityNameKey = "吉林市".equals(cityName) ? cityName : String.valueOf(cityName).replace("市", "");
                excelRowDtos.add(ExcelRowDto.builder()
                        .agentCode(Integer.parseInt(objects[1].toString()))
                        .agentName(objects[0].toString())
                        .city(cityName)
                        .cityCode(Integer.parseInt(cityCodeMap.get(cityNameKey)))
                        .province(provinceName)
                        .provinceCode(Integer.parseInt(provinceCodeMap.get(provinceName)))
                        .build());
            }

            Map<String, List<ExcelRowDto>> provinceMap = excelRowDtos.stream().collect(Collectors.groupingBy(ExcelRowDto::getProvince));
//            rootOptionDto.setChildren();
            rootOptionDto.setTitle("省,市,经销商");
            rootOptionDto.setId(0);
            rootOptionDto.setName("root");
            Map<String, String> fieldProvince = new HashMap<>();
            fieldProvince.put("name", "省");
            Map<String, String> fieldCity = new HashMap<>();
            fieldCity.put("name", "市");
            Map<String, String> fieldAgent = new HashMap<>();
            fieldAgent.put("name", "经销商");
            rootOptionDto.setFields(Lists.newArrayList(fieldProvince, fieldCity, fieldAgent));

            StringBuilder contentBuilder = new StringBuilder("");
            List<OptionNode> rootNodeChildList = new ArrayList<>();

            for (Map.Entry<String, List<ExcelRowDto>> provinceEntry : provinceMap.entrySet()) {
                String provinceEntryKey = provinceEntry.getKey();

                Integer provinceCode = provinceEntry.getValue().get(0).getProvinceCode();
                OptionNode provinceNode = new OptionNode();
                provinceNode.setId(provinceCode);
                provinceNode.setPid(0);
                provinceNode.setName(provinceEntryKey);
                provinceNode.setLevel(1);

                List<OptionNode> provinceNodeChildList = new ArrayList<>();
                Map<String, List<ExcelRowDto>> cityMap = provinceEntry.getValue().stream().collect(Collectors.groupingBy(ExcelRowDto::getCity));
                for (Map.Entry<String, List<ExcelRowDto>> cityEntry : cityMap.entrySet()) {
                    String cityEntryKey = cityEntry.getKey();


                    Integer cityCode = cityEntry.getValue().get(0).getCityCode();
                    OptionNode cityNode = new OptionNode();
                    cityNode.setId(cityCode);
                    cityNode.setPid(provinceCode);
                    cityNode.setName(cityEntryKey);
                    cityNode.setLevel(2);
                    List<OptionNode> agentDtos = cityEntry.getValue().stream().map(r -> {
                        contentBuilder.append(provinceEntryKey).append(",");
                        contentBuilder.append(cityEntryKey).append(",");
                        contentBuilder.append(r.getAgentName()).append("\n");
                        return OptionNode.builder()
                                .id(r.getAgentCode())
                                .name(r.getAgentName())
                                .level(3)
                                .pid(cityCode)
                                .build();
                    }).collect(Collectors.toList());
                    cityNode.setChildren(agentDtos);

                    provinceNodeChildList.add(cityNode);
                }

                provinceNode.setChildren(provinceNodeChildList);

                rootNodeChildList.add(provinceNode);
            }

            String content = contentBuilder.toString();
            rootOptionDto.setContent(content.substring(0, content.length() - 1));
            rootOptionDto.setChildren(rootNodeChildList);

            log.info("result {}", JSON.toJSONString(rootOptionDto));
        } catch (Exception e) {
            log.error("error ", e);
        }
        return JSON.toJSONString(rootOptionDto);
    }


}
