package com.bilibili.mgk.platform.biz.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class MgkMidPhonePoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public MgkMidPhonePoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andMidIsNull() {
            addCriterion("mid is null");
            return (Criteria) this;
        }

        public Criteria andMidIsNotNull() {
            addCriterion("mid is not null");
            return (Criteria) this;
        }

        public Criteria andMidEqualTo(Long value) {
            addCriterion("mid =", value, "mid");
            return (Criteria) this;
        }

        public Criteria andMidNotEqualTo(Long value) {
            addCriterion("mid <>", value, "mid");
            return (Criteria) this;
        }

        public Criteria andMidGreaterThan(Long value) {
            addCriterion("mid >", value, "mid");
            return (Criteria) this;
        }

        public Criteria andMidGreaterThanOrEqualTo(Long value) {
            addCriterion("mid >=", value, "mid");
            return (Criteria) this;
        }

        public Criteria andMidLessThan(Long value) {
            addCriterion("mid <", value, "mid");
            return (Criteria) this;
        }

        public Criteria andMidLessThanOrEqualTo(Long value) {
            addCriterion("mid <=", value, "mid");
            return (Criteria) this;
        }

        public Criteria andMidIn(List<Long> values) {
            addCriterion("mid in", values, "mid");
            return (Criteria) this;
        }

        public Criteria andMidNotIn(List<Long> values) {
            addCriterion("mid not in", values, "mid");
            return (Criteria) this;
        }

        public Criteria andMidBetween(Long value1, Long value2) {
            addCriterion("mid between", value1, value2, "mid");
            return (Criteria) this;
        }

        public Criteria andMidNotBetween(Long value1, Long value2) {
            addCriterion("mid not between", value1, value2, "mid");
            return (Criteria) this;
        }

        public Criteria andBuvidIsNull() {
            addCriterion("buvid is null");
            return (Criteria) this;
        }

        public Criteria andBuvidIsNotNull() {
            addCriterion("buvid is not null");
            return (Criteria) this;
        }

        public Criteria andBuvidEqualTo(String value) {
            addCriterion("buvid =", value, "buvid");
            return (Criteria) this;
        }

        public Criteria andBuvidNotEqualTo(String value) {
            addCriterion("buvid <>", value, "buvid");
            return (Criteria) this;
        }

        public Criteria andBuvidGreaterThan(String value) {
            addCriterion("buvid >", value, "buvid");
            return (Criteria) this;
        }

        public Criteria andBuvidGreaterThanOrEqualTo(String value) {
            addCriterion("buvid >=", value, "buvid");
            return (Criteria) this;
        }

        public Criteria andBuvidLessThan(String value) {
            addCriterion("buvid <", value, "buvid");
            return (Criteria) this;
        }

        public Criteria andBuvidLessThanOrEqualTo(String value) {
            addCriterion("buvid <=", value, "buvid");
            return (Criteria) this;
        }

        public Criteria andBuvidLike(String value) {
            addCriterion("buvid like", value, "buvid");
            return (Criteria) this;
        }

        public Criteria andBuvidNotLike(String value) {
            addCriterion("buvid not like", value, "buvid");
            return (Criteria) this;
        }

        public Criteria andBuvidIn(List<String> values) {
            addCriterion("buvid in", values, "buvid");
            return (Criteria) this;
        }

        public Criteria andBuvidNotIn(List<String> values) {
            addCriterion("buvid not in", values, "buvid");
            return (Criteria) this;
        }

        public Criteria andBuvidBetween(String value1, String value2) {
            addCriterion("buvid between", value1, value2, "buvid");
            return (Criteria) this;
        }

        public Criteria andBuvidNotBetween(String value1, String value2) {
            addCriterion("buvid not between", value1, value2, "buvid");
            return (Criteria) this;
        }

        public Criteria andImeiIsNull() {
            addCriterion("imei is null");
            return (Criteria) this;
        }

        public Criteria andImeiIsNotNull() {
            addCriterion("imei is not null");
            return (Criteria) this;
        }

        public Criteria andImeiEqualTo(String value) {
            addCriterion("imei =", value, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiNotEqualTo(String value) {
            addCriterion("imei <>", value, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiGreaterThan(String value) {
            addCriterion("imei >", value, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiGreaterThanOrEqualTo(String value) {
            addCriterion("imei >=", value, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiLessThan(String value) {
            addCriterion("imei <", value, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiLessThanOrEqualTo(String value) {
            addCriterion("imei <=", value, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiLike(String value) {
            addCriterion("imei like", value, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiNotLike(String value) {
            addCriterion("imei not like", value, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiIn(List<String> values) {
            addCriterion("imei in", values, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiNotIn(List<String> values) {
            addCriterion("imei not in", values, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiBetween(String value1, String value2) {
            addCriterion("imei between", value1, value2, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiNotBetween(String value1, String value2) {
            addCriterion("imei not between", value1, value2, "imei");
            return (Criteria) this;
        }

        public Criteria andDeviceIdIsNull() {
            addCriterion("device_id is null");
            return (Criteria) this;
        }

        public Criteria andDeviceIdIsNotNull() {
            addCriterion("device_id is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceIdEqualTo(String value) {
            addCriterion("device_id =", value, "deviceId");
            return (Criteria) this;
        }

        public Criteria andDeviceIdNotEqualTo(String value) {
            addCriterion("device_id <>", value, "deviceId");
            return (Criteria) this;
        }

        public Criteria andDeviceIdGreaterThan(String value) {
            addCriterion("device_id >", value, "deviceId");
            return (Criteria) this;
        }

        public Criteria andDeviceIdGreaterThanOrEqualTo(String value) {
            addCriterion("device_id >=", value, "deviceId");
            return (Criteria) this;
        }

        public Criteria andDeviceIdLessThan(String value) {
            addCriterion("device_id <", value, "deviceId");
            return (Criteria) this;
        }

        public Criteria andDeviceIdLessThanOrEqualTo(String value) {
            addCriterion("device_id <=", value, "deviceId");
            return (Criteria) this;
        }

        public Criteria andDeviceIdLike(String value) {
            addCriterion("device_id like", value, "deviceId");
            return (Criteria) this;
        }

        public Criteria andDeviceIdNotLike(String value) {
            addCriterion("device_id not like", value, "deviceId");
            return (Criteria) this;
        }

        public Criteria andDeviceIdIn(List<String> values) {
            addCriterion("device_id in", values, "deviceId");
            return (Criteria) this;
        }

        public Criteria andDeviceIdNotIn(List<String> values) {
            addCriterion("device_id not in", values, "deviceId");
            return (Criteria) this;
        }

        public Criteria andDeviceIdBetween(String value1, String value2) {
            addCriterion("device_id between", value1, value2, "deviceId");
            return (Criteria) this;
        }

        public Criteria andDeviceIdNotBetween(String value1, String value2) {
            addCriterion("device_id not between", value1, value2, "deviceId");
            return (Criteria) this;
        }

        public Criteria andPrefixIsNull() {
            addCriterion("prefix is null");
            return (Criteria) this;
        }

        public Criteria andPrefixIsNotNull() {
            addCriterion("prefix is not null");
            return (Criteria) this;
        }

        public Criteria andPrefixEqualTo(String value) {
            addCriterion("prefix =", value, "prefix");
            return (Criteria) this;
        }

        public Criteria andPrefixNotEqualTo(String value) {
            addCriterion("prefix <>", value, "prefix");
            return (Criteria) this;
        }

        public Criteria andPrefixGreaterThan(String value) {
            addCriterion("prefix >", value, "prefix");
            return (Criteria) this;
        }

        public Criteria andPrefixGreaterThanOrEqualTo(String value) {
            addCriterion("prefix >=", value, "prefix");
            return (Criteria) this;
        }

        public Criteria andPrefixLessThan(String value) {
            addCriterion("prefix <", value, "prefix");
            return (Criteria) this;
        }

        public Criteria andPrefixLessThanOrEqualTo(String value) {
            addCriterion("prefix <=", value, "prefix");
            return (Criteria) this;
        }

        public Criteria andPrefixLike(String value) {
            addCriterion("prefix like", value, "prefix");
            return (Criteria) this;
        }

        public Criteria andPrefixNotLike(String value) {
            addCriterion("prefix not like", value, "prefix");
            return (Criteria) this;
        }

        public Criteria andPrefixIn(List<String> values) {
            addCriterion("prefix in", values, "prefix");
            return (Criteria) this;
        }

        public Criteria andPrefixNotIn(List<String> values) {
            addCriterion("prefix not in", values, "prefix");
            return (Criteria) this;
        }

        public Criteria andPrefixBetween(String value1, String value2) {
            addCriterion("prefix between", value1, value2, "prefix");
            return (Criteria) this;
        }

        public Criteria andPrefixNotBetween(String value1, String value2) {
            addCriterion("prefix not between", value1, value2, "prefix");
            return (Criteria) this;
        }

        public Criteria andPhoneNumIsNull() {
            addCriterion("phone_num is null");
            return (Criteria) this;
        }

        public Criteria andPhoneNumIsNotNull() {
            addCriterion("phone_num is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneNumEqualTo(String value) {
            addCriterion("phone_num =", value, "phoneNum");
            return (Criteria) this;
        }

        public Criteria andPhoneNumNotEqualTo(String value) {
            addCriterion("phone_num <>", value, "phoneNum");
            return (Criteria) this;
        }

        public Criteria andPhoneNumGreaterThan(String value) {
            addCriterion("phone_num >", value, "phoneNum");
            return (Criteria) this;
        }

        public Criteria andPhoneNumGreaterThanOrEqualTo(String value) {
            addCriterion("phone_num >=", value, "phoneNum");
            return (Criteria) this;
        }

        public Criteria andPhoneNumLessThan(String value) {
            addCriterion("phone_num <", value, "phoneNum");
            return (Criteria) this;
        }

        public Criteria andPhoneNumLessThanOrEqualTo(String value) {
            addCriterion("phone_num <=", value, "phoneNum");
            return (Criteria) this;
        }

        public Criteria andPhoneNumLike(String value) {
            addCriterion("phone_num like", value, "phoneNum");
            return (Criteria) this;
        }

        public Criteria andPhoneNumNotLike(String value) {
            addCriterion("phone_num not like", value, "phoneNum");
            return (Criteria) this;
        }

        public Criteria andPhoneNumIn(List<String> values) {
            addCriterion("phone_num in", values, "phoneNum");
            return (Criteria) this;
        }

        public Criteria andPhoneNumNotIn(List<String> values) {
            addCriterion("phone_num not in", values, "phoneNum");
            return (Criteria) this;
        }

        public Criteria andPhoneNumBetween(String value1, String value2) {
            addCriterion("phone_num between", value1, value2, "phoneNum");
            return (Criteria) this;
        }

        public Criteria andPhoneNumNotBetween(String value1, String value2) {
            addCriterion("phone_num not between", value1, value2, "phoneNum");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andAllowHistoryIsNull() {
            addCriterion("allow_history is null");
            return (Criteria) this;
        }

        public Criteria andAllowHistoryIsNotNull() {
            addCriterion("allow_history is not null");
            return (Criteria) this;
        }

        public Criteria andAllowHistoryEqualTo(Integer value) {
            addCriterion("allow_history =", value, "allowHistory");
            return (Criteria) this;
        }

        public Criteria andAllowHistoryNotEqualTo(Integer value) {
            addCriterion("allow_history <>", value, "allowHistory");
            return (Criteria) this;
        }

        public Criteria andAllowHistoryGreaterThan(Integer value) {
            addCriterion("allow_history >", value, "allowHistory");
            return (Criteria) this;
        }

        public Criteria andAllowHistoryGreaterThanOrEqualTo(Integer value) {
            addCriterion("allow_history >=", value, "allowHistory");
            return (Criteria) this;
        }

        public Criteria andAllowHistoryLessThan(Integer value) {
            addCriterion("allow_history <", value, "allowHistory");
            return (Criteria) this;
        }

        public Criteria andAllowHistoryLessThanOrEqualTo(Integer value) {
            addCriterion("allow_history <=", value, "allowHistory");
            return (Criteria) this;
        }

        public Criteria andAllowHistoryIn(List<Integer> values) {
            addCriterion("allow_history in", values, "allowHistory");
            return (Criteria) this;
        }

        public Criteria andAllowHistoryNotIn(List<Integer> values) {
            addCriterion("allow_history not in", values, "allowHistory");
            return (Criteria) this;
        }

        public Criteria andAllowHistoryBetween(Integer value1, Integer value2) {
            addCriterion("allow_history between", value1, value2, "allowHistory");
            return (Criteria) this;
        }

        public Criteria andAllowHistoryNotBetween(Integer value1, Integer value2) {
            addCriterion("allow_history not between", value1, value2, "allowHistory");
            return (Criteria) this;
        }

        public Criteria andAllowHistoryToastIsNull() {
            addCriterion("allow_history_toast is null");
            return (Criteria) this;
        }

        public Criteria andAllowHistoryToastIsNotNull() {
            addCriterion("allow_history_toast is not null");
            return (Criteria) this;
        }

        public Criteria andAllowHistoryToastEqualTo(Integer value) {
            addCriterion("allow_history_toast =", value, "allowHistoryToast");
            return (Criteria) this;
        }

        public Criteria andAllowHistoryToastNotEqualTo(Integer value) {
            addCriterion("allow_history_toast <>", value, "allowHistoryToast");
            return (Criteria) this;
        }

        public Criteria andAllowHistoryToastGreaterThan(Integer value) {
            addCriterion("allow_history_toast >", value, "allowHistoryToast");
            return (Criteria) this;
        }

        public Criteria andAllowHistoryToastGreaterThanOrEqualTo(Integer value) {
            addCriterion("allow_history_toast >=", value, "allowHistoryToast");
            return (Criteria) this;
        }

        public Criteria andAllowHistoryToastLessThan(Integer value) {
            addCriterion("allow_history_toast <", value, "allowHistoryToast");
            return (Criteria) this;
        }

        public Criteria andAllowHistoryToastLessThanOrEqualTo(Integer value) {
            addCriterion("allow_history_toast <=", value, "allowHistoryToast");
            return (Criteria) this;
        }

        public Criteria andAllowHistoryToastIn(List<Integer> values) {
            addCriterion("allow_history_toast in", values, "allowHistoryToast");
            return (Criteria) this;
        }

        public Criteria andAllowHistoryToastNotIn(List<Integer> values) {
            addCriterion("allow_history_toast not in", values, "allowHistoryToast");
            return (Criteria) this;
        }

        public Criteria andAllowHistoryToastBetween(Integer value1, Integer value2) {
            addCriterion("allow_history_toast between", value1, value2, "allowHistoryToast");
            return (Criteria) this;
        }

        public Criteria andAllowHistoryToastNotBetween(Integer value1, Integer value2) {
            addCriterion("allow_history_toast not between", value1, value2, "allowHistoryToast");
            return (Criteria) this;
        }

        public Criteria andIsPhoneValidateIsNull() {
            addCriterion("is_phone_validate is null");
            return (Criteria) this;
        }

        public Criteria andIsPhoneValidateIsNotNull() {
            addCriterion("is_phone_validate is not null");
            return (Criteria) this;
        }

        public Criteria andIsPhoneValidateEqualTo(Integer value) {
            addCriterion("is_phone_validate =", value, "isPhoneValidate");
            return (Criteria) this;
        }

        public Criteria andIsPhoneValidateNotEqualTo(Integer value) {
            addCriterion("is_phone_validate <>", value, "isPhoneValidate");
            return (Criteria) this;
        }

        public Criteria andIsPhoneValidateGreaterThan(Integer value) {
            addCriterion("is_phone_validate >", value, "isPhoneValidate");
            return (Criteria) this;
        }

        public Criteria andIsPhoneValidateGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_phone_validate >=", value, "isPhoneValidate");
            return (Criteria) this;
        }

        public Criteria andIsPhoneValidateLessThan(Integer value) {
            addCriterion("is_phone_validate <", value, "isPhoneValidate");
            return (Criteria) this;
        }

        public Criteria andIsPhoneValidateLessThanOrEqualTo(Integer value) {
            addCriterion("is_phone_validate <=", value, "isPhoneValidate");
            return (Criteria) this;
        }

        public Criteria andIsPhoneValidateIn(List<Integer> values) {
            addCriterion("is_phone_validate in", values, "isPhoneValidate");
            return (Criteria) this;
        }

        public Criteria andIsPhoneValidateNotIn(List<Integer> values) {
            addCriterion("is_phone_validate not in", values, "isPhoneValidate");
            return (Criteria) this;
        }

        public Criteria andIsPhoneValidateBetween(Integer value1, Integer value2) {
            addCriterion("is_phone_validate between", value1, value2, "isPhoneValidate");
            return (Criteria) this;
        }

        public Criteria andIsPhoneValidateNotBetween(Integer value1, Integer value2) {
            addCriterion("is_phone_validate not between", value1, value2, "isPhoneValidate");
            return (Criteria) this;
        }

        public Criteria andRejectLoginInfoTimeIsNull() {
            addCriterion("reject_login_info_time is null");
            return (Criteria) this;
        }

        public Criteria andRejectLoginInfoTimeIsNotNull() {
            addCriterion("reject_login_info_time is not null");
            return (Criteria) this;
        }

        public Criteria andRejectLoginInfoTimeEqualTo(Timestamp value) {
            addCriterion("reject_login_info_time =", value, "rejectLoginInfoTime");
            return (Criteria) this;
        }

        public Criteria andRejectLoginInfoTimeNotEqualTo(Timestamp value) {
            addCriterion("reject_login_info_time <>", value, "rejectLoginInfoTime");
            return (Criteria) this;
        }

        public Criteria andRejectLoginInfoTimeGreaterThan(Timestamp value) {
            addCriterion("reject_login_info_time >", value, "rejectLoginInfoTime");
            return (Criteria) this;
        }

        public Criteria andRejectLoginInfoTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("reject_login_info_time >=", value, "rejectLoginInfoTime");
            return (Criteria) this;
        }

        public Criteria andRejectLoginInfoTimeLessThan(Timestamp value) {
            addCriterion("reject_login_info_time <", value, "rejectLoginInfoTime");
            return (Criteria) this;
        }

        public Criteria andRejectLoginInfoTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("reject_login_info_time <=", value, "rejectLoginInfoTime");
            return (Criteria) this;
        }

        public Criteria andRejectLoginInfoTimeIn(List<Timestamp> values) {
            addCriterion("reject_login_info_time in", values, "rejectLoginInfoTime");
            return (Criteria) this;
        }

        public Criteria andRejectLoginInfoTimeNotIn(List<Timestamp> values) {
            addCriterion("reject_login_info_time not in", values, "rejectLoginInfoTime");
            return (Criteria) this;
        }

        public Criteria andRejectLoginInfoTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("reject_login_info_time between", value1, value2, "rejectLoginInfoTime");
            return (Criteria) this;
        }

        public Criteria andRejectLoginInfoTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("reject_login_info_time not between", value1, value2, "rejectLoginInfoTime");
            return (Criteria) this;
        }

        public Criteria andAllowLoginInfoToastIsNull() {
            addCriterion("allow_login_info_toast is null");
            return (Criteria) this;
        }

        public Criteria andAllowLoginInfoToastIsNotNull() {
            addCriterion("allow_login_info_toast is not null");
            return (Criteria) this;
        }

        public Criteria andAllowLoginInfoToastEqualTo(Integer value) {
            addCriterion("allow_login_info_toast =", value, "allowLoginInfoToast");
            return (Criteria) this;
        }

        public Criteria andAllowLoginInfoToastNotEqualTo(Integer value) {
            addCriterion("allow_login_info_toast <>", value, "allowLoginInfoToast");
            return (Criteria) this;
        }

        public Criteria andAllowLoginInfoToastGreaterThan(Integer value) {
            addCriterion("allow_login_info_toast >", value, "allowLoginInfoToast");
            return (Criteria) this;
        }

        public Criteria andAllowLoginInfoToastGreaterThanOrEqualTo(Integer value) {
            addCriterion("allow_login_info_toast >=", value, "allowLoginInfoToast");
            return (Criteria) this;
        }

        public Criteria andAllowLoginInfoToastLessThan(Integer value) {
            addCriterion("allow_login_info_toast <", value, "allowLoginInfoToast");
            return (Criteria) this;
        }

        public Criteria andAllowLoginInfoToastLessThanOrEqualTo(Integer value) {
            addCriterion("allow_login_info_toast <=", value, "allowLoginInfoToast");
            return (Criteria) this;
        }

        public Criteria andAllowLoginInfoToastIn(List<Integer> values) {
            addCriterion("allow_login_info_toast in", values, "allowLoginInfoToast");
            return (Criteria) this;
        }

        public Criteria andAllowLoginInfoToastNotIn(List<Integer> values) {
            addCriterion("allow_login_info_toast not in", values, "allowLoginInfoToast");
            return (Criteria) this;
        }

        public Criteria andAllowLoginInfoToastBetween(Integer value1, Integer value2) {
            addCriterion("allow_login_info_toast between", value1, value2, "allowLoginInfoToast");
            return (Criteria) this;
        }

        public Criteria andAllowLoginInfoToastNotBetween(Integer value1, Integer value2) {
            addCriterion("allow_login_info_toast not between", value1, value2, "allowLoginInfoToast");
            return (Criteria) this;
        }

        public Criteria andEncryptPhoneNumIsNull() {
            addCriterion("encrypt_phone_num is null");
            return (Criteria) this;
        }

        public Criteria andEncryptPhoneNumIsNotNull() {
            addCriterion("encrypt_phone_num is not null");
            return (Criteria) this;
        }

        public Criteria andEncryptPhoneNumEqualTo(String value) {
            addCriterion("encrypt_phone_num =", value, "encryptPhoneNum");
            return (Criteria) this;
        }

        public Criteria andEncryptPhoneNumNotEqualTo(String value) {
            addCriterion("encrypt_phone_num <>", value, "encryptPhoneNum");
            return (Criteria) this;
        }

        public Criteria andEncryptPhoneNumGreaterThan(String value) {
            addCriterion("encrypt_phone_num >", value, "encryptPhoneNum");
            return (Criteria) this;
        }

        public Criteria andEncryptPhoneNumGreaterThanOrEqualTo(String value) {
            addCriterion("encrypt_phone_num >=", value, "encryptPhoneNum");
            return (Criteria) this;
        }

        public Criteria andEncryptPhoneNumLessThan(String value) {
            addCriterion("encrypt_phone_num <", value, "encryptPhoneNum");
            return (Criteria) this;
        }

        public Criteria andEncryptPhoneNumLessThanOrEqualTo(String value) {
            addCriterion("encrypt_phone_num <=", value, "encryptPhoneNum");
            return (Criteria) this;
        }

        public Criteria andEncryptPhoneNumLike(String value) {
            addCriterion("encrypt_phone_num like", value, "encryptPhoneNum");
            return (Criteria) this;
        }

        public Criteria andEncryptPhoneNumNotLike(String value) {
            addCriterion("encrypt_phone_num not like", value, "encryptPhoneNum");
            return (Criteria) this;
        }

        public Criteria andEncryptPhoneNumIn(List<String> values) {
            addCriterion("encrypt_phone_num in", values, "encryptPhoneNum");
            return (Criteria) this;
        }

        public Criteria andEncryptPhoneNumNotIn(List<String> values) {
            addCriterion("encrypt_phone_num not in", values, "encryptPhoneNum");
            return (Criteria) this;
        }

        public Criteria andEncryptPhoneNumBetween(String value1, String value2) {
            addCriterion("encrypt_phone_num between", value1, value2, "encryptPhoneNum");
            return (Criteria) this;
        }

        public Criteria andEncryptPhoneNumNotBetween(String value1, String value2) {
            addCriterion("encrypt_phone_num not between", value1, value2, "encryptPhoneNum");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}