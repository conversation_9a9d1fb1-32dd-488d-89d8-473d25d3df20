package com.bilibili.mgk.platform.biz.bean.bo;

import com.bilibili.adp.common.util.func.HasKey;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MgkGameVideoJobBo implements Serializable, HasKey<String> {
    private Integer accountId;

    private Long avid;

    private Integer bizId;

    private Long cid;

    private java.sql.Timestamp ctime;

    private Integer gameBaseId;

    private Integer id;

    private java.sql.Timestamp mtime;

    private String name;

    private Integer status;

    @Override
    public String getKey() {
        return accountId + "-" + gameBaseId;
    }
}
