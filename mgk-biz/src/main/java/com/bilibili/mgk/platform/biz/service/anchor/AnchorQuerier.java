package com.bilibili.mgk.platform.biz.service.anchor;

import com.bapis.ad.scv.anchor.NativeAnchorServiceGrpc;
import com.bapis.ad.scv.anchor.QueryAnchorRep;
import com.bapis.ad.scv.anchor.QueryAnchorReq;
import com.bapis.ad.scv.anchor.SingleQueryAnchorRep;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/16 14:13
 */
@Component
@Slf4j
public class AnchorQuerier {

    @Autowired
    private NativeAnchorServiceGrpc.NativeAnchorServiceBlockingStub nativeAnchorServiceBlockingStub;


    public Map<Long, SingleQueryAnchorRep> queryAnchorMap(List<Long> avids) {

        List<SingleQueryAnchorRep> singleQueryAnchorReps = this.queryAnchorList(avids);
        return singleQueryAnchorReps.stream().collect(Collectors.toMap(t -> t.getAid(), t -> t, (t1, t2) -> t2));
    }

    public List<SingleQueryAnchorRep> queryAnchorList(List<Long> avids) {
        if (CollectionUtils.isEmpty(avids)) {
            return Collections.EMPTY_LIST;
        }

        QueryAnchorReq queryAnchorReq = QueryAnchorReq.newBuilder().addAllAid(avids).build();
        QueryAnchorRep queryAnchorRep = nativeAnchorServiceBlockingStub.queryAnchorList(queryAnchorReq);

        return queryAnchorRep.getDataList();
    }

}
