package com.bilibili.mgk.platform.biz.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.mgk.platform.biz.dao.querydsl.pos.MgkFormDataQueryDSLPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QMgkFormData is a Querydsl query type for MgkFormDataQueryDSLPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QMgkFormData extends com.querydsl.sql.RelationalPathBase<MgkFormDataQueryDSLPo> {

    private static final long serialVersionUID = -**********;

    public static final QMgkFormData mgkFormData = new QMgkFormData("mgk_form_data");

    public final NumberPath<Integer> accountId = createNumber("accountId", Integer.class);

    public final StringPath assemblyTrackId = createString("assemblyTrackId");

    public final NumberPath<Long> avid = createNumber("avid", Long.class);

    public final StringPath buvid = createString("buvid");

    public final NumberPath<Long> creativeId = createNumber("creativeId", Long.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Integer> customerId = createNumber("customerId", Integer.class);

    public final StringPath deviceId = createString("deviceId");

    public final NumberPath<Integer> eventSourceType = createNumber("eventSourceType", Integer.class);

    public final NumberPath<Long> formId = createNumber("formId", Long.class);

    public final StringPath fromTrackId = createString("fromTrackId");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath imei = createString("imei");

    public final NumberPath<Integer> isCheat = createNumber("isCheat", Integer.class);

    public final NumberPath<Integer> isDeleted = createNumber("isDeleted", Integer.class);

    public final NumberPath<Integer> isLiveAssemblyCard = createNumber("isLiveAssemblyCard", Integer.class);

    public final NumberPath<Long> mid = createNumber("mid", Long.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final NumberPath<Integer> os = createNumber("os", Integer.class);

    public final NumberPath<Long> pageId = createNumber("pageId", Long.class);

    public final NumberPath<Long> phoneOrderId = createNumber("phoneOrderId", Long.class);

    public final NumberPath<Integer> phoneOrderStatus = createNumber("phoneOrderStatus", Integer.class);

    public final NumberPath<Integer> productId = createNumber("productId", Integer.class);

    public final NumberPath<Integer> reportSource = createNumber("reportSource", Integer.class);

    public final NumberPath<Integer> reportStatus = createNumber("reportStatus", Integer.class);

    public final StringPath requestId = createString("requestId");

    public final NumberPath<Integer> salesType = createNumber("salesType", Integer.class);

    public final NumberPath<Long> sharerUid = createNumber("sharerUid", Long.class);

    public final NumberPath<Integer> sourceId = createNumber("sourceId", Integer.class);

    public final StringPath trackId = createString("trackId");

    public final com.querydsl.sql.PrimaryKey<MgkFormDataQueryDSLPo> primary = createPrimaryKey(id);

    public QMgkFormData(String variable) {
        super(MgkFormDataQueryDSLPo.class, forVariable(variable), "null", "mgk_form_data");
        addMetadata();
    }

    public QMgkFormData(String variable, String schema, String table) {
        super(MgkFormDataQueryDSLPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QMgkFormData(String variable, String schema) {
        super(MgkFormDataQueryDSLPo.class, forVariable(variable), schema, "mgk_form_data");
        addMetadata();
    }

    public QMgkFormData(Path<? extends MgkFormDataQueryDSLPo> path) {
        super(path.getType(), path.getMetadata(), "null", "mgk_form_data");
        addMetadata();
    }

    public QMgkFormData(PathMetadata metadata) {
        super(MgkFormDataQueryDSLPo.class, metadata, "null", "mgk_form_data");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(accountId, ColumnMetadata.named("account_id").withIndex(2).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(assemblyTrackId, ColumnMetadata.named("assembly_track_id").withIndex(27).ofType(Types.VARCHAR).withSize(1024).notNull());
        addMetadata(avid, ColumnMetadata.named("avid").withIndex(25).ofType(Types.BIGINT).withSize(19).notNull());
        addMetadata(buvid, ColumnMetadata.named("buvid").withIndex(9).ofType(Types.VARCHAR).withSize(64).notNull());
        addMetadata(creativeId, ColumnMetadata.named("creative_id").withIndex(5).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(13).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(customerId, ColumnMetadata.named("customer_id").withIndex(21).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(deviceId, ColumnMetadata.named("device_id").withIndex(10).ofType(Types.VARCHAR).withSize(64).notNull());
        addMetadata(eventSourceType, ColumnMetadata.named("event_source_type").withIndex(24).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(formId, ColumnMetadata.named("form_id").withIndex(4).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(fromTrackId, ColumnMetadata.named("from_track_id").withIndex(26).ofType(Types.VARCHAR).withSize(1024).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(imei, ColumnMetadata.named("imei").withIndex(8).ofType(Types.VARCHAR).withSize(32).notNull());
        addMetadata(isCheat, ColumnMetadata.named("is_cheat").withIndex(23).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(12).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(isLiveAssemblyCard, ColumnMetadata.named("is_live_assembly_card").withIndex(28).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(mid, ColumnMetadata.named("mid").withIndex(7).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(14).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(os, ColumnMetadata.named("os").withIndex(11).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(pageId, ColumnMetadata.named("page_id").withIndex(3).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(phoneOrderId, ColumnMetadata.named("phone_order_id").withIndex(20).ofType(Types.BIGINT).withSize(19).notNull());
        addMetadata(phoneOrderStatus, ColumnMetadata.named("phone_order_status").withIndex(19).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(productId, ColumnMetadata.named("product_id").withIndex(22).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(reportSource, ColumnMetadata.named("report_source").withIndex(29).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(reportStatus, ColumnMetadata.named("report_status").withIndex(18).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(requestId, ColumnMetadata.named("request_id").withIndex(15).ofType(Types.VARCHAR).withSize(64).notNull());
        addMetadata(salesType, ColumnMetadata.named("sales_type").withIndex(6).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(sharerUid, ColumnMetadata.named("sharer_uid").withIndex(30).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(sourceId, ColumnMetadata.named("source_id").withIndex(16).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(trackId, ColumnMetadata.named("track_id").withIndex(17).ofType(Types.VARCHAR).withSize(255).notNull());
    }

}

