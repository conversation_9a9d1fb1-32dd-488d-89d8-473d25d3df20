package com.bilibili.mgk.platform.biz.dao.ext;

import com.bilibili.mgk.platform.api.landing_page.dto.QueryPageWithExtraParamDto;
import com.bilibili.mgk.platform.biz.po.MgkLandingPagePo;
import com.bilibili.mgk.platform.biz.po.MgkLandingPagePoExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ExtMgkLandingPageDao {

    List<Long> selectPageIdsByExample(MgkLandingPagePoExample example);

    List<Integer> selectWithFormExtra(QueryPageWithExtraParamDto paramDto);

    List<Integer> selectWithAppExtra(QueryPageWithExtraParamDto paramDto);

    List<Integer> selectWithMiniGameExtra(QueryPageWithExtraParamDto paramDto);


}