package com.bilibili.mgk.platform.biz.service;

import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.mgk.platform.biz.dao.MgkCmSpaceDao;
import com.bilibili.mgk.platform.biz.po.MgkCmSpacePo;
import com.bilibili.mgk.platform.biz.po.MgkCmSpacePoExample;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.support.Assert;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @file: MgkCmSpaceServiceDelegate
 * @author: gaoming
 * @date: 2021/12/13
 * @version: 1.0
 * @description:
 **/
@Service
public class MgkCmSpaceServiceDelegate {

    @Autowired
    private MgkCmSpaceDao cmSpaceDao;

    public Map<Integer, Long> getAidToMidMap(List<Integer> aids) {

        if (CollectionUtils.isEmpty(aids)) {
            return Collections.emptyMap();
        }

        List<MgkCmSpacePo> pos = getPosByAids(aids);

        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyMap();
        }

        return pos.stream().collect(Collectors.toMap(MgkCmSpacePo::getAccountId, MgkCmSpacePo::getMid));
    }

    private List<MgkCmSpacePo> getPosByAids(List<Integer> aids) {

        if (CollectionUtils.isEmpty(aids)) {
            return Collections.emptyList();
        }

        MgkCmSpacePoExample example = new MgkCmSpacePoExample();
        MgkCmSpacePoExample.Criteria or = example.or();

        if (aids.size() == 1) {
            ObjectUtils.setObject(() -> aids.get(0), or::andAccountIdEqualTo);
        } else {
            ObjectUtils.setList(() -> aids, or::andAccountIdIn);
        }

        return cmSpaceDao.selectByExample(example);
    }

    public Map<Long, Integer> getMidToAidMap(List<Long> mids) {

        if (CollectionUtils.isEmpty(mids)) {
            return Collections.emptyMap();
        }
        List<MgkCmSpacePo> pos = getPosByMids(mids);

        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyMap();
        }

        return pos.stream().collect(Collectors.toMap(MgkCmSpacePo::getMid, MgkCmSpacePo::getAccountId));
    }

    private List<MgkCmSpacePo> getPosByMids(List<Long> mids) {
        if (CollectionUtils.isEmpty(mids)) {
            return Collections.emptyList();
        }
        MgkCmSpacePoExample example = new MgkCmSpacePoExample();
        MgkCmSpacePoExample.Criteria or = example.or();

        if (mids.size() == 1) {
            ObjectUtils.setObject(() -> mids.get(0), or::andMidEqualTo);
        } else {
            ObjectUtils.setList(() -> mids, or::andMidIn);
        }

        return cmSpaceDao.selectByExample(example);
    }

    public List<Long> getArcMidsInMgkCmSpace(List<Long> mids) {
        if (CollectionUtils.isEmpty(mids)) {
            return Collections.emptyList();
        }
        Assert.isTrue(mids.size() <= 100, "单次查询量不得大于100");
        MgkCmSpacePoExample exm = new MgkCmSpacePoExample();
        exm.or().andMidIn(mids);
        return cmSpaceDao.selectByExample(exm).stream()
                .map(MgkCmSpacePo::getMid)
                .distinct()
                .collect(Collectors.toList());
    }
}
