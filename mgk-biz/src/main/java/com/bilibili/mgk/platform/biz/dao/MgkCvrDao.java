package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkCvrPo;
import com.bilibili.mgk.platform.biz.po.MgkCvrPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MgkCvrDao {
    long countByExample(MgkCvrPoExample example);

    int deleteByExample(MgkCvrPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MgkCvrPo record);

    int insertBatch(List<MgkCvrPo> records);

    int insertUpdateBatch(List<MgkCvrPo> records);

    int insert(MgkCvrPo record);

    int insertUpdateSelective(MgkCvrPo record);

    int insertSelective(MgkCvrPo record);

    List<MgkCvrPo> selectByExample(MgkCvrPoExample example);

    MgkCvrPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MgkCvrPo record, @Param("example") MgkCvrPoExample example);

    int updateByExample(@Param("record") MgkCvrPo record, @Param("example") MgkCvrPoExample example);

    int updateByPrimaryKeySelective(MgkCvrPo record);

    int updateByPrimaryKey(MgkCvrPo record);
}