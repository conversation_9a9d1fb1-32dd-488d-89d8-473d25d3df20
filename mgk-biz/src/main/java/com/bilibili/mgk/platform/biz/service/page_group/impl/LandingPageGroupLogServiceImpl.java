package com.bilibili.mgk.platform.biz.service.page_group.impl;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.GsonUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.bjcom.querydsl.clause.BaseQuery;
import com.bilibili.mgk.platform.api.landing_page_group.dto.LandingPageGroupLogDiffDto;
import com.bilibili.mgk.platform.api.landing_page_group.dto.LandingPageGroupLogDto;
import com.bilibili.mgk.platform.api.landing_page_group.dto.LandingPageGroupLogInfoDto;
import com.bilibili.mgk.platform.api.landing_page_group.dto.QueryLandingPageGroupLogDto;
import com.bilibili.mgk.platform.api.log.dto.MgkDiffItem;
import com.bilibili.mgk.platform.biz.dao.querydsl.pos.MgkPageGroupOperationLogQueryDSLPo;
import com.bilibili.mgk.platform.biz.utils.MgkLogDiffUtil;
import com.bilibili.mgk.platform.common.MgkOperationType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.bilibili.mgk.platform.biz.dao.querydsl.QMgkPageGroupOperationLog.mgkPageGroupOperationLog;

/**
 * @ClassName LandingPageGroupLogServiceImpl
 * <AUTHOR>
 * @Date 2023/5/31 5:09 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class LandingPageGroupLogServiceImpl {

    @Autowired
    private BaseQueryFactory bqf;

    public void addCreatePageGroupLog(Long groupId,
                                      LandingPageGroupLogDiffDto createLog,
                                      Operator operator) {
        List<MgkDiffItem> diffItems = MgkLogDiffUtil.genDiffItems(LandingPageGroupLogDiffDto.builder().build(), createLog);
        String operateValue = GsonUtils.getGson().toJson(diffItems);
        LandingPageGroupLogDto logDto = LandingPageGroupLogDto.builder()
                .groupId(groupId)
                .operateType(MgkOperationType.CREATE.getCode())
                .operateValue(operateValue)
                .operatorType(operator.getOperatorType().getCode())
                .operatorName(operator.getOperatorName())
                .operatorId(operator.getOperatorId())
                .build();
        bqf.insert(mgkPageGroupOperationLog).insertBean(logDto);
    }

    public void addUpdatePageGroupLog(Long groupId,
                                      LandingPageGroupLogDiffDto oldLogDto,
                                      LandingPageGroupLogDiffDto newLogDto,
                                      Operator operator) {
        List<MgkDiffItem> diffItems = MgkLogDiffUtil.genDiffItems(oldLogDto, newLogDto);
        String operateValue = GsonUtils.getGson().toJson(diffItems);
        LandingPageGroupLogDto logDto = LandingPageGroupLogDto.builder()
                .groupId(groupId)
                .operateType(MgkOperationType.UPDATE.getCode())
                .operateValue(operateValue)
                .operatorType(operator.getOperatorType().getCode())
                .operatorName(operator.getOperatorName())
                .operatorId(operator.getOperatorId())
                .build();
        bqf.insert(mgkPageGroupOperationLog).insertBean(logDto);
    }

    public void addUpdateStatusPageGroupLog(Long groupId,
                                            LandingPageGroupLogDiffDto oldLogDto,
                                            LandingPageGroupLogDiffDto newLogDto,
                                            Operator operator) {
        List<MgkDiffItem> diffItems = MgkLogDiffUtil.genDiffItems(oldLogDto, newLogDto);
        String operateValue = GsonUtils.getGson().toJson(diffItems);
        LandingPageGroupLogDto logDto = LandingPageGroupLogDto.builder()
                .groupId(groupId)
                .operateType(MgkOperationType.UPDATE_STATUS.getCode())
                .operateValue(operateValue)
                .operatorType(operator.getOperatorType().getCode())
                .operatorName(operator.getOperatorName())
                .operatorId(operator.getOperatorId())
                .build();
        bqf.insert(mgkPageGroupOperationLog).insertBean(logDto);
    }

    public void addAuditPageGroupLog(Long groupId,
                                     boolean isAuditPass,
                                     LandingPageGroupLogDiffDto oldLogDto,
                                     LandingPageGroupLogDiffDto newLogDto,
                                     Operator operator) {
        Integer operateType = isAuditPass ? MgkOperationType.AUDIT_PASS.getCode() : MgkOperationType.AUDIT_REJECT.getCode();
        List<MgkDiffItem> diffItems = MgkLogDiffUtil.genDiffItems(oldLogDto, newLogDto);
        String operateValue = GsonUtils.getGson().toJson(diffItems);
        LandingPageGroupLogDto logDto = LandingPageGroupLogDto.builder()
                .groupId(groupId)
                .operateType(operateType)
                .operatorId(operator.getOperatorId())
                .operateValue(operateValue)
                .operatorType(operator.getOperatorType().getCode())
                .operatorName(operator.getOperatorName())
                .build();
        bqf.insert(mgkPageGroupOperationLog).insertBean(logDto);
    }

    public PageResult<LandingPageGroupLogInfoDto> queryLog(QueryLandingPageGroupLogDto queryDto) {
        BaseQuery<MgkPageGroupOperationLogQueryDSLPo> countQuery = generateBaseQuery(queryDto);
        int total = (int) countQuery.fetchCount();
        if (!Utils.isPositive(total)) {
            return PageResult.emptyPageResult();
        }
        List<LandingPageGroupLogInfoDto> records = generateBaseQuery(queryDto)
                .fetch(LandingPageGroupLogInfoDto.class);
        records.forEach(record -> {
            Integer operateType = record.getOperateType();
            if (MgkOperationType.QUERY_LOG_REPLACE_OPERATE_LIST.contains(operateType)) {
                operateType = MgkOperationType.UPDATE_STATUS.getCode();
            }
            record.setOperateType(operateType);
        });
        return PageResult.<LandingPageGroupLogInfoDto>builder()
                .records(records)
                .total(total)
                .build();
    }

    private BaseQuery<MgkPageGroupOperationLogQueryDSLPo> generateBaseQuery(QueryLandingPageGroupLogDto queryDto) {
        return bqf.selectFrom(mgkPageGroupOperationLog)
                .where(mgkPageGroupOperationLog.groupId.eq(queryDto.getGroupId()))
                .whereIfNotNull(queryDto.getAccountId(), mgkPageGroupOperationLog.operatorId::eq)
                .where(mgkPageGroupOperationLog.isDeleted.eq(IsDeleted.VALID.getCode()))
                .limit(queryDto.getPage().getLimit())
                .offset(queryDto.getPage().getOffset())
                .orderBy(mgkPageGroupOperationLog.ctime.desc());
    }

}
