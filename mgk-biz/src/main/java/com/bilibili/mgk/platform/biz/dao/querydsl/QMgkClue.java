package com.bilibili.mgk.platform.biz.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.mgk.platform.biz.dao.querydsl.pos.MgkClueQueryDSLPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QMgkClue is a Querydsl query type for MgkClueQueryDSLPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QMgkClue extends com.querydsl.sql.RelationalPathBase<MgkClueQueryDSLPo> {

    private static final long serialVersionUID = -*********;

    public static final QMgkClue mgkClue = new QMgkClue("mgk_clue");

    public final NumberPath<Integer> accountId = createNumber("accountId", Integer.class);

    public final NumberPath<Integer> clueState = createNumber("clueState", Integer.class);

    public final NumberPath<Integer> creativeId = createNumber("creativeId", Integer.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> isDeleted = createNumber("isDeleted", Integer.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final NumberPath<Integer> reportStatus = createNumber("reportStatus", Integer.class);

    public final NumberPath<Integer> sourceId = createNumber("sourceId", Integer.class);

    public final StringPath trackId = createString("trackId");

    public final com.querydsl.sql.PrimaryKey<MgkClueQueryDSLPo> primary = createPrimaryKey(id);

    public QMgkClue(String variable) {
        super(MgkClueQueryDSLPo.class, forVariable(variable), "null", "mgk_clue");
        addMetadata();
    }

    public QMgkClue(String variable, String schema, String table) {
        super(MgkClueQueryDSLPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QMgkClue(String variable, String schema) {
        super(MgkClueQueryDSLPo.class, forVariable(variable), schema, "mgk_clue");
        addMetadata();
    }

    public QMgkClue(Path<? extends MgkClueQueryDSLPo> path) {
        super(path.getType(), path.getMetadata(), "null", "mgk_clue");
        addMetadata();
    }

    public QMgkClue(PathMetadata metadata) {
        super(MgkClueQueryDSLPo.class, metadata, "null", "mgk_clue");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(accountId, ColumnMetadata.named("account_id").withIndex(2).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(clueState, ColumnMetadata.named("clue_state").withIndex(6).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(creativeId, ColumnMetadata.named("creative_id").withIndex(3).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(9).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(8).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(10).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(reportStatus, ColumnMetadata.named("report_status").withIndex(7).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(sourceId, ColumnMetadata.named("source_id").withIndex(4).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(trackId, ColumnMetadata.named("track_id").withIndex(5).ofType(Types.VARCHAR).withSize(255).notNull());
    }

}

