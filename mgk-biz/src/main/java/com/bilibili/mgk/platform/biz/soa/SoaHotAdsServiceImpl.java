package com.bilibili.mgk.platform.biz.soa;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.mgk.platform.api.hot_ads.dto.DaiHuoHotAdDto;
import com.bilibili.mgk.platform.api.hot_ads.dto.HotAdsDto;
import com.bilibili.mgk.platform.api.hot_ads.dto.QueryDaiHuoHotAdDto;
import com.bilibili.mgk.platform.api.hot_ads.dto.QueryHotAdsDto;
import com.bilibili.mgk.platform.api.hot_ads.service.IDaiHuoHotAdsService;
import com.bilibili.mgk.platform.api.hot_ads.service.IHotAdsService;
import com.bilibili.mgk.platform.api.hot_ads.soa.ISoaHotAdsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.SQLException;

/**
 * @file: SoaHotAdsServiceImpl
 * @author: gaoming
 * @date: 2021/01/13
 * @version: 1.0
 * @description:
 **/

@Service
@Slf4j
public class SoaHotAdsServiceImpl implements ISoaHotAdsService {

    @Autowired
    private IHotAdsService hotAdsService;

    @Autowired
    private IDaiHuoHotAdsService daiHuoHotAdsService;

    @Override
    public PageResult<HotAdsDto> getHotAdsDtos(Operator operator, QueryHotAdsDto queryHotAdsDto) {
        return hotAdsService.getHotAdsDtos(operator, queryHotAdsDto);
    }

    @Override
    public PageResult<DaiHuoHotAdDto> getDaiHuoHotAdsDtos(Operator operator, QueryDaiHuoHotAdDto query)
            throws ServiceException, SQLException {
        return daiHuoHotAdsService.getDaiHuoHotAdDtos(query, operator);
    }
}
