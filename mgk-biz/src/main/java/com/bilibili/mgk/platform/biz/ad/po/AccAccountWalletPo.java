package com.bilibili.mgk.platform.biz.ad.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccAccountWalletPo implements Serializable {
    /**
     * 自增ID
     */
    private Integer id;

    /**
     * 账号ID
     */
    private Integer accountId;

    /**
     * 现金（单位分）
     */
    private Long cash;

    /**
     * 红包（单位分）
     */
    private Long redPacket;

    /**
     * 专项返货（单位分）
     */
    private Long specialRedPacket;

    /**
     * 总现金充值
     */
    private Long totalCashRecharge;

    /**
     * 总现金消费
     */
    private Long totalCashConsume;

    /**
     * 总红包充值
     */
    private Long totalRedPacketRecharge;

    /**
     * 总红包消费
     */
    private Long totalRedPacketConsume;

    /**
     * 总专返充值
     */
    private Long totalSpecialRedPacketRecharge;

    /**
     * 总专返消费
     */
    private Long totalSpecialRedPacketConsume;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 软删除，0是有效，1是删除
     */
    private Integer isDeleted;

    /**
     * 签约-托管余额（单位分）
     */
    private Long trustFund;

    /**
     * 签约-订单余额（单位分）
     */
    private Long withholdFund;

    /**
     * 起飞-现金-托管-余额（单位分）
     */
    private Long trustCash;

    /**
     * 起飞-激励金-托管-余额（单位分）
     */
    private Long trustIncentive;

    /**
     * 起飞-起飞币-托管-余额（单位分）
     */
    private Long trustFlyCoin;

    private static final long serialVersionUID = 1L;
}