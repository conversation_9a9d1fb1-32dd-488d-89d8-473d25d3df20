package com.bilibili.mgk.platform.biz.soa;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.mgk.platform.api.form.dto.MgkFormDto;
import com.bilibili.mgk.platform.api.form.dto.QueryFormParamDto;
import com.bilibili.mgk.platform.api.form.service.IMgkFormService;
import com.bilibili.mgk.platform.api.form.soa.ISoaMgkFormService;
import com.bilibili.mgk.platform.api.landing_page.dto.MgkLandingPageDto;
import com.bilibili.mgk.platform.api.landing_page.dto.QueryLandingPageParamDto;
import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageService;
import com.bilibili.mgk.platform.common.IsModelEnum;
import com.bilibili.mgk.platform.common.LandingPageStatusEnum;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
public class SoaMgkFormServiceImpl implements ISoaMgkFormService {

    @Autowired
    private IMgkFormService mgkFormService;

    @Autowired
    private IMgkLandingPageService landingPageService;

    @Override
    public MgkFormDto getFormDtoByFormId(Long formId) {
        return mgkFormService.getFormDtoByFormId(formId);
    }

    @Override
    public List<MgkFormDto> getFormDtoS(QueryFormParamDto queryFormParamDto) {
        return mgkFormService.getFormDtos(queryFormParamDto);
    }

    @Override
    public MgkLandingPageDto getPageDtoByFormIdForCommentConvert(Long formId) {
        if(Utils.isPositive(formId)){
            List<MgkLandingPageDto> pageDtoS = landingPageService
                    .getLandingPageDtos(QueryLandingPageParamDto.builder()
                            .isModelList(Lists.newArrayList(IsModelEnum.COMMENT.getCode()))
                            .statusList(Lists.newArrayList(LandingPageStatusEnum.PUBLISHED.getCode()))
                            .formIdList(Lists.newArrayList(formId)).build());

            if(!CollectionUtils.isEmpty(pageDtoS)){
                //评论区浮层表单一个表单只会生成一个落地页
                return pageDtoS.get(0);
            }
        }

       return null;
    }
}
