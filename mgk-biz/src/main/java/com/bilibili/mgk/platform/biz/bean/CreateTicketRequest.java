package com.bilibili.mgk.platform.biz.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: wangbin01
 * @create: 2018-12-04
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateTicketRequest {

    private String action;

    private String access_token;

    @JsonProperty("data")
    private TicketData data;

    private String method;
}


