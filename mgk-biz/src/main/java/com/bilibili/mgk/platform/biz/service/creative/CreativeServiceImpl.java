package com.bilibili.mgk.platform.biz.service.creative;

import com.bapis.ad.pandora.core.auto.AutoServiceGrpc;
import com.bapis.ad.pandora.core.auto.FetchAutoCreativeReq;
import com.bapis.ad.pandora.core.auto.FetchAutoCreativeResp;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.mgk.platform.api.creative.CreativeService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
@Slf4j
public class CreativeServiceImpl implements CreativeService {

    @Resource
    private AutoServiceGrpc.AutoServiceBlockingStub autoServiceBlockingStub;

    public Map<Integer, Integer> queryParentCreative(List<Integer> creativeIds) {
        Assert.isTrue(creativeIds.size() <= 10, "creativeIds不能多于10个");
        FetchAutoCreativeResp resp = autoServiceBlockingStub.fetchAutoCreative(FetchAutoCreativeReq.newBuilder()
                .addAllCreativeId(creativeIds)
                .build());
        return resp.getAutoCreativeInfoMap().entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getParentCreativeId()));
    }

    @Override
    public Long queryParentCreativeIdOrDefaultSelf(Long creativeId) {
        if (!Utils.isPositive(creativeId)) {
            return creativeId;
        }

        int intCreativeId = creativeId.intValue();
        Integer parentId = queryParentCreative(Lists.newArrayList(intCreativeId)).get(intCreativeId);
        if (Utils.isPositive(parentId)) {
            return (long) parentId;
        }

        return creativeId;
    }
}
