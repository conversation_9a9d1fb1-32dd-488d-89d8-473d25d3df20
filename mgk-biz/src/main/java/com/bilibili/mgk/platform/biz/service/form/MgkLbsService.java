package com.bilibili.mgk.platform.biz.service.form;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.GsonUtils;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.http.utils.OkHttpUtils;
import com.bilibili.mgk.platform.api.form.dto.*;
import com.bilibili.mgk.platform.api.form.service.IMgkLbsService;
import com.bilibili.mgk.platform.biz.dao.LbsAddressDao;
import com.bilibili.mgk.platform.biz.dao.LbsAreaDao;
import com.bilibili.mgk.platform.biz.dao.LbsShopDao;
import com.bilibili.mgk.platform.biz.po.*;
import com.bilibili.mgk.platform.biz.redis.RedisService;
import com.bilibili.mgk.platform.biz.service.FormServiceDelegate;
import com.bilibili.mgk.platform.biz.thread.MgkExecutorPoolUtils;
import com.bilibili.mgk.platform.biz.validator.MgkFormValidator;
import com.bilibili.mgk.platform.common.DealStatusEnum;
import com.bilibili.mgk.platform.common.enums.form.LBSExcelColEnum;
import com.bilibili.mgk.platform.common.enums.form.LbsAreaLevelEnum;
import com.bilibili.mgk.platform.common.utils.ExampleUtils;
import com.bilibili.mgk.platform.common.utils.GeoUtil;
import com.bilibili.mgk.platform.common.utils.excel.ExcelReadUtil;
import com.google.common.base.Throwables;
import com.mysema.commons.lang.Pair;
import joptsimple.internal.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.bilibili.mgk.platform.biz.redis.keys.FormCacheKey.CUSTOMIZE_LBS_REDIS_PREFIX;
import static com.bilibili.mgk.platform.biz.redis.keys.FormCacheKey.LBS_REDIS_PREFIX;
import static com.bilibili.mgk.platform.common.enums.form.LBSExcelColEnum.PROVINCE_AND_CITY_CODE;

@Service
@Slf4j
@RequiredArgsConstructor
public class MgkLbsService implements IMgkLbsService {

    private static final Long TH_CACHE_TIME = 3000L;
    private static final String ADDRESS_QUERY_URL = "https://api.map.baidu.com/place/v2/search";
    private static final String AREA_LOCATION_CACHE_PRFIX = "mgk_form_area_location_";
    @Value("#{'${mgk.lbs.query.address.appkey.list:l1mnHtGUdR7nS110PochueTmNrTAVZQX,ofdB2AyabMqpHd0FlB0N2QZ625wEIvBg,Cxh8EAObIx1Q9a23xndTHick6eEyzvTW,8Aq5qi07pkQG5XH1CDiczckqYSdC7cG8}'.split(',')}")
    private List<String> appkeyList;
    @Value("#{'${mgk.lbs.replace.name:AITO授权用户中心•,华为旗舰店•}'.split(',')}")
    private List<String> replaceNameList;

    @Value("${mgk.lbs.query.address.appkey:i38jZGPbo1yAv08QxzzNDQ9LsRdMLt6K}")
    private String appKey;

    private final LbsShopDao lbsShopDao;
    private final LbsAreaDao areaDao;
    private final RedisService redisService;
    private final SnowflakeIdWorker snowflakeIdWorker;
    private final MgkFormValidator formValidator;
    private final FormServiceDelegate formServiceDelegate;
    private final LbsAddressDao addressDao;

    /*
     * 查询离当前用户地址最近的门店
     * @Param lng 经度
     * @Param lat 纬度
     * @Param formItemId 表单项id
     * @Param product 产品
     *
     * @return AddressInfoDto 最近的门店信息
     */
    @Override
    public AddressInfoDto queryNearestShop(String lng, String lat, String product, String cityCode, Long formItemId) {
        //查询当前城市距离最近的门店
        AddressInfoDto infoDto = choseProvinceInnerMinDistanceShop(lng, lat, product, cityCode, formItemId, true);
        if (infoDto != null) {
            return infoDto;
        }
        //查询当前省内距离最近的门店
        infoDto = choseProvinceInnerMinDistanceShop(lng, lat, product, cityCode, formItemId, false);
        if (infoDto != null) {
            return infoDto;
        }

        return choseMinDistanceAreaInOtherProvince(lng, lat, product, formItemId);
    }

    /*
     * 查询当前省内离用户最近的门店
     */
    private AddressInfoDto choseProvinceInnerMinDistanceShop(String lng, String lat, String product,
                                                             String cityCode, Long formItemId, boolean isCityInner) {

        LbsShopPoExample poExample = new LbsShopPoExample();
        LbsShopPoExample.Criteria criteria = poExample.or().andFormItemIdEqualTo(formItemId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        ExampleUtils.notNull(product, criteria::andProductEqualTo);

        //非当前城市内的门店
        if (isCityInner) {
            criteria.andCityCodeEqualTo(cityCode);
        } else {
            //查询当前城市所在的省份
            LbsAreaPoExample areaPoExample = new LbsAreaPoExample();
            areaPoExample.or().andAreaCodeEqualTo(cityCode).andLevelEqualTo(LbsAreaLevelEnum.CITY.getCode())
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            LbsAreaPo cityInfo = areaDao.selectByExample(areaPoExample).stream().findFirst().orElse(new LbsAreaPo());
            criteria.andProvinceCodeEqualTo(cityInfo.getParentCode());
        }

        List<LbsShopPo> lbsShopPos = lbsShopDao.selectByExample(poExample);
        return sortAndConv2AddressInfo(lbsShopPos, lng, lat);
    }

    private AddressInfoDto sortAndConv2AddressInfo(List<LbsShopPo> lbsShopPos, String lng, String lat) {
        if (CollectionUtils.isEmpty(lbsShopPos)) {
            return null;
        }
        Pair<Double, LbsShopPo> dis2Shop = lbsShopPos.stream()
                .map(shopPo -> Pair.of(GeoUtil.getDistanceOfKM(shopPo.getLat().doubleValue(),
                        shopPo.getLng().doubleValue(),
                        Double.parseDouble(lat), Double.parseDouble(lng)), shopPo))
                .min(Comparator.comparing(Pair::getFirst))
                .orElse(null);
        if (dis2Shop == null) {
            return null;
        }

        LbsShopPo minDisShop = dis2Shop.getSecond();
        return AddressInfoDto.builder().shopAddress(minDisShop.getShopAddress())
                .shopName(minDisShop.getShopName())
                .chinaAdminCode(minDisShop.getCityCode())
                .provinceCode(minDisShop.getProvinceCode())
                .distance(String.valueOf(dis2Shop.getFirst())).build();
    }

    /*
     * 查询其他省内离用户最近的门店
     */
    private AddressInfoDto choseMinDistanceAreaInOtherProvince(String lng, String lat, String product, Long formItemId) {
        //如果省內没查到,就先遍历这个商家在那个省有门店，找一个离这个市最近的省，然后从这个省内找一个最近的门店
        LbsShopPoExample poExample = new LbsShopPoExample();
        LbsShopPoExample.Criteria criteria = poExample.or().andFormItemIdEqualTo(formItemId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        ExampleUtils.notNull(product, criteria::andProductEqualTo);
        Set<String> allLbsShopProvinces = lbsShopDao.selectByExample(poExample).stream().map(LbsShopPo::getProvinceCode)
                .collect(Collectors.toSet());
        Assert.notEmpty(allLbsShopProvinces, "根据您的定位找不到合适的门店,请手动选择!");

        LbsAreaPoExample areaPoExample = new LbsAreaPoExample();
        areaPoExample.or().andAreaCodeIn(new ArrayList<>(allLbsShopProvinces))
                .andLevelEqualTo(LbsAreaLevelEnum.PROVINCE.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<LbsAreaPo> areaPos = areaDao.selectByExample(areaPoExample);

        LbsAreaPo minDistanceArea = new LbsAreaPo();
        double temp, minDistance = Double.MAX_VALUE;
        for (LbsAreaPo areaPo : areaPos) {
            temp = GeoUtil.getDistanceOfKM(areaPo.getLat().doubleValue(), areaPo.getLng().doubleValue(),
                    Double.parseDouble(lat), Double.parseDouble(lng));
            if (temp <= minDistance) {
                minDistance = temp;
                minDistanceArea = areaPo;
            }
        }

        poExample.clear();
        LbsShopPoExample.Criteria criteria1 = poExample.or().andFormItemIdEqualTo(formItemId)
                .andProvinceCodeEqualTo(minDistanceArea.getAreaCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        ExampleUtils.notNull(product, criteria1::andProductEqualTo);
        List<LbsShopPo> lbsShopPos = lbsShopDao.selectByExample(poExample);
        return sortAndConv2AddressInfo(lbsShopPos, lng, lat);
    }

    /*
     * 查询在指定城市的门店列表
     * @Param lng 经度
     * @Param lat 纬度
     * @Param cityCode 城市码
     * @Param formItemId 表单项id
     *
     * @return AddressInfoDto 门店列表信息
     */
    @Override
    public List<AddressInfoDto> queryShopListByCityCode(String lng, String lat, String product, String cityCode, Long formItemId) {
        LbsShopPoExample poExample = new LbsShopPoExample();
        LbsShopPoExample.Criteria criteria = poExample.createCriteria();
        criteria.andCityCodeEqualTo(cityCode).andFormItemIdEqualTo(formItemId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (!StringUtils.isEmpty(product)) {
            criteria.andProductEqualTo(product);
        }
        List<LbsShopPo> lbsShopPos = lbsShopDao.selectByExample(poExample);

        if (!CollectionUtils.isEmpty(lbsShopPos)) {
            return lbsShopPos.stream().map(lbsShopPo -> {
                double distance = GeoUtil.getDistanceOfKM(lbsShopPo.getLat().doubleValue(),
                        lbsShopPo.getLng().doubleValue(),
                        Double.parseDouble(lat), Double.parseDouble(lng));
                return AddressInfoDto.builder().shopAddress(lbsShopPo.getShopAddress()).shopName(lbsShopPo.getShopName())
                        .chinaAdminCode(lbsShopPo.getCityCode()).distance(String.valueOf(distance)).build();
            }).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /*
     * 异步解析lbs门店地址
     * @Param options lbs信息
     * @Param formItemId 表单项id
     *
     * @return AddressInfoDto 门店列表信息
     */
    @Override
    public MgkFormLbsDto asynchronousAnalyzeLbsShop(String options, Long formItemId, Long oldFormItemId) {
        if (Utils.isPositive(oldFormItemId)) {
            MgkFormCustomizeLbsDto lbsDto = copyOldCustomizeLbs(oldFormItemId, snowflakeIdWorker.nextId());
            return MgkFormLbsDto.finishStatus(lbsDto.getFormItemId());
        }
        if (Utils.isPositive(formItemId)) {
            String res = redisService.getCache(LBS_REDIS_PREFIX + formItemId);
            return GsonUtils.toObject(res, MgkFormLbsDto.class);
        }

        long finalFormItemId = snowflakeIdWorker.nextId();
        MgkFormLbsDto lbsDto = MgkFormLbsDto.initStatus(finalFormItemId);
        redisService.addCache(LBS_REDIS_PREFIX + finalFormItemId, GsonUtils.toJson(lbsDto), TH_CACHE_TIME);

        MgkExecutorPoolUtils.execute(() -> refreshLbsFormData(options, finalFormItemId));
        return lbsDto;
    }

    /*
     * 异步解析 自定义LBS的门店信息
     * @Param excel LBS的门店信息
     * @Param formItemId 表单项id
     *
     * @return AddressInfoDto 门店列表信息
     */
    @Override
    public MgkFormCustomizeLbsDto asynchronousAnalyzeCustomizeLbsExcel(File excel, Long formItemId, Long oldFormItemId) {
        if (Utils.isPositive(oldFormItemId)) {
            return copyOldCustomizeLbs(oldFormItemId, snowflakeIdWorker.nextId());
        }
        if (Utils.isPositive(formItemId)) {
            String res = redisService.getCache(CUSTOMIZE_LBS_REDIS_PREFIX + formItemId);
            return GsonUtils.toObject(res, MgkFormCustomizeLbsDto.class);
        } else {
            //首次调用,必须要传文件
            Assert.notNull(excel, "上传的lbs文件不能为空");
        }

        long finalFormItemId = snowflakeIdWorker.nextId();
        MgkFormCustomizeLbsDto lbsDto = MgkFormCustomizeLbsDto.initStatus(finalFormItemId);
        redisService.addCache(CUSTOMIZE_LBS_REDIS_PREFIX + finalFormItemId, GsonUtils.toJson(lbsDto), TH_CACHE_TIME);
        MgkExecutorPoolUtils.execute(() -> analyzeCustomizeLbsExcel(excel, finalFormItemId));
        return lbsDto;
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public MgkFormCustomizeLbsDto copyOldCustomizeLbs(long oldFormItemId, long newFormItemId) {
        LbsShopPoExample shopPoExample = new LbsShopPoExample();
        shopPoExample.or().andFormItemIdEqualTo(oldFormItemId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<LbsShopPo> lbsShopPos = lbsShopDao.selectByExample(shopPoExample);
        Assert.notEmpty(lbsShopPos, "您复制的lbs表单已无效,请重新上传");

        String options = formServiceDelegate.getOptionByFormItemId(oldFormItemId);
        Assert.isTrue(StringUtils.hasText(options), "您复制的lbs表单已无效,请重新上传");

        Timestamp now = new Timestamp(System.currentTimeMillis());
        List<LbsShopPo> newShops = lbsShopPos.stream().peek(shop -> {
            shop.setFormItemId(newFormItemId);
            shop.setCtime(now);
            shop.setMtime(now);
            shop.setId(null);
        }).collect(Collectors.toList());

        newShops.forEach(lbsShopDao::insertSelective);
        return MgkFormCustomizeLbsDto.finishStatus(newFormItemId, options);
    }

    private void analyzeCustomizeLbsExcel(File excel, Long formItemId) {
        LbsConfigDto configDto = new LbsConfigDto();
        String cacheKey = CUSTOMIZE_LBS_REDIS_PREFIX + formItemId;
        try {
            //获取地理映射码
            Map<String, String> regionNameCodeMap = formValidator.getRegionNameCodeMap();

            //读取excel
            List<Object[]> contentList = ExcelReadUtil.readExcelBySheetIndex(excel, 1);
            Assert.notNull(contentList, "无法解析exel");

            //解析excel头
            List<String> header = analyzeExcelHeader(contentList.get(0));
            int addressIndex = header.indexOf(LBSExcelColEnum.ADDRESS.getCode());
            String title = Strings.join(header, ",");
            configDto.setTitle(title);
            contentList.remove(0);

            //拼接config中的field参数,例如城市码和城市应该看成是一条数据
            configDto.setFields(header.stream().filter(head -> !LBSExcelColEnum.NOT_IN_FIELD_CODE.contains(head))
                    .map(head -> FieldDto.builder().name(LBSExcelColEnum.getByCode(head).getDesc()).type(head).build())
                    .collect(Collectors.toList()));

            //拼接config中的content
            StringBuilder contentSb = new StringBuilder(title);

            //组件的父子关系管理映射
            List<Map<String, Set<String>>> parent2ChildList = new ArrayList<>();
            header.forEach(head -> parent2ChildList.add(new HashMap<>()));
            //Option子集合
            List<LbsOptionDto> subOptions = new ArrayList<>();
            //每一行中header对应的option对象
            Map<String, LbsOptionDto> subHeader2Option = new HashMap<>();
            //整个excel表格汇总的数据健值对
            Map<String, LbsOptionDto> nameAndLevel2Option = new HashMap<>();

            List<LbsShopPo> shopPos = new ArrayList<>();
            AtomicInteger id = new AtomicInteger();

            int total = contentList.size() - 1;
            Map<Integer, Integer> reportPoint2Ratio = new HashMap<>();
            reportPoint2Ratio.put(total / 10, 10);
            reportPoint2Ratio.put(total / 5, 20);
            reportPoint2Ratio.put((total / 5) * 2, 40);
            reportPoint2Ratio.put((total / 5) * 3, 60);
            reportPoint2Ratio.put((total / 5) * 4, 80);
            AtomicInteger current = new AtomicInteger();
            contentList.forEach(content -> {
                current.getAndIncrement();
                // //设置操作进度
                if (reportPoint2Ratio.containsKey(current.get())) {
                    String res = redisService.getCache(cacheKey);
                    if (StringUtils.hasText(res)) {
                        MgkFormLbsDto lbsDto = GsonUtils.toObject(res, MgkFormLbsDto.class);
                        lbsDto.setProcessingProgress(reportPoint2Ratio.get(current.get()));
                        redisService.addCache(cacheKey, GsonUtils.toJson(lbsDto), TH_CACHE_TIME);
                    }
                }
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < content.length; i++) {
                    if (addressIndex != i) {
                        if (i == 0) {
                            sb.append(content[i]);
                        } else {
                            sb.append(",").append(content[i]);
                        }

                    }
                }
                String lineStr = sb.toString();
                Assert.isTrue(content.length == header.size(),
                        "请检查excel的[" + lineStr + "]这一行是否存在没填写的字段," +
                                "如果某一列不需要,则一定要把对应的表头如:shop、city、city_code等一起删掉");
                //拼接content
                contentSb.append("\n").append(lineStr);

                //组装option对象
                subOptions.clear();
                subHeader2Option.clear();
                String areaCode, areaName, cur, address;
                LbsOptionDto curOption;
                for (int index = 0; index < content.length; index++) {
                    Assert.notNull(content[index], "请检查excel的[" + lineStr + "]这一行是否存在没填写的字段");
                    cur = content[index].toString();
                    Assert.isTrue(StringUtils.hasText(cur), "请检查excel的[" + lineStr + "]这一行是否存在没填写的字段");
                    String col = header.get(index);
                    if (PROVINCE_AND_CITY_CODE.contains(col)) {
                        int next = index + 1;
                        areaName = cur;
                        Assert.notNull(content[next], "请检查excel的[" + lineStr + "]这一行是否存在没填写的字段");
                        areaCode = content[next].toString();
                        Assert.isTrue(StringUtils.hasText(areaCode), "请检查excel的[" + lineStr + "]这一行是否存在没填写的字段");
                        String realAreaName = regionNameCodeMap.getOrDefault(areaCode, "");
                        Assert.isTrue(!StringUtils.isEmpty(realAreaName)
                                && areaName.equals(realAreaName), areaName + "省id行政代码错误，请检查后重新提交");

                        curOption = LbsOptionDto.builder().type(col).name(areaName)
                                .id(Integer.parseInt(areaCode)).build();
                        subOptions.add(curOption);
                        index = next;
                    } else if (LBSExcelColEnum.SHOP.getCode().equals(col)) {
                        int next = index + 1;
                        Assert.isTrue(next < content.length, "如果lbs包含门店,那么门店地址必传");
                        address = content[next].toString();
                        Assert.notNull(content[next], "请检查excel的[" + lineStr + "]这一行是否存在没填写的字段");
                        curOption = LbsOptionDto.builder().type(col).name(cur).address(address)
                                .id(id.getAndIncrement()).build();
                        subOptions.add(curOption);
                        index = next;
                    } else if (LBSExcelColEnum.ADDRESS.getCode().equals(col)) {
                        throw new IllegalArgumentException("address必须在shop的后面或者传了address一定要写shop");
                    } else {
                        curOption = LbsOptionDto.builder().type(col).id(id.getAndIncrement())
                                .name(cur).build();
                        subOptions.add(curOption);
                    }
                    subHeader2Option.put(col, curOption);
                }

                //组装门店定位对象
                shopPos.add(buildLbsShopPo(formItemId, subHeader2Option));

                //将option对象放在其对应层级的列表里
                int pre;
                String preKey = null, finalKey;
                StringBuilder key;
                for (int index = 0; index < subOptions.size(); index++) {
                    curOption = subOptions.get(index);

                    key = new StringBuilder();
                    for (int j = index; j >= 0; j--) {
                        key.append("&").append(subOptions.get(j).getName());
                    }
                    finalKey = key.substring(1);
                    if (index == 0) {
                        preKey = finalKey;
                    }

                    if (index > 0) {
                        pre = index - 1;
                        //获取此列的父级列
                        Map<String, Set<String>> parent2Child = parent2ChildList.get(pre);
                        //获取此列的同级子列列表
                        Set<String> peer = parent2Child.getOrDefault(preKey, new HashSet<>());
                        peer.add(finalKey);
                        parent2Child.put(preKey, peer);
                    }
                    preKey = finalKey;
                    //因为第0层放root,所以层级在index上加1
                    int level = index + 1;

                    if (!nameAndLevel2Option.containsKey(finalKey)) {
                        curOption.setLevel(level);
                        nameAndLevel2Option.put(finalKey, curOption);
                    }
                }
            });

            configDto.setContent(contentSb.toString());

            List<LbsOptionDto> res = new ArrayList<>();
            List<Map<String, Set<String>>> finalParent2ChildList = parent2ChildList.stream()
                    .filter(map -> !CollectionUtils.isEmpty(map))
                    .collect(Collectors.toList());
            //只有一个option
            //要把address置为空,因为选项展示的时候不需要
            if (CollectionUtils.isEmpty(finalParent2ChildList)) {
                nameAndLevel2Option.forEach((nameAndLevel, option) -> res.add(option));
            } else {
                Map<String, Set<String>> parent2Child = finalParent2ChildList.get(0);

                parent2Child.forEach((parent, child) -> {
                    LbsOptionDto optionDto = nameAndLevel2Option.get(parent);
                    optionDto.setChildren(buildChildOptions(finalParent2ChildList, nameAndLevel2Option, 0, parent));
                    res.add(optionDto);
                });
            }

            configDto.setId(0);
            configDto.setName("root");
            configDto.setChildren(res);

            batchInsertLbsShop(shopPos);
        } catch (Exception e) {
            log.error("analyzeCustomizeLbsExcel" + formItemId + " error"
                    + Throwables.getStackTraceAsString(e));
            String res = redisService.getCache(cacheKey);
            if (StringUtils.hasText(res)) {
                MgkFormCustomizeLbsDto lbsDto = GsonUtils.toObject(res, MgkFormCustomizeLbsDto.class);
                lbsDto.setDealStatus(DealStatusEnum.FAILED.getCode());
                lbsDto.setFailMsg("解析表单项id为[" + formItemId + " +]lbs文件失败,失败原因为:[" + e.getMessage() + "]");
                lbsDto.setFormItemId(null);
                redisService.addCache(cacheKey, GsonUtils.toJson(lbsDto), TH_CACHE_TIME);
            }
            throw new IllegalArgumentException("analyzeCustomizeLbsExcel fail" + formItemId
                    + Throwables.getStackTraceAsString(e));
        }

        MgkFormCustomizeLbsDto lbsDto = MgkFormCustomizeLbsDto.finishStatus(formItemId, GsonUtils.toJson(configDto));
        redisService.addCache(cacheKey, GsonUtils.toJson(lbsDto), TH_CACHE_TIME);
    }

    @Transactional(value = "mgkPlatformTransactionManager", rollbackFor = Exception.class)
    public void batchInsertLbsShop(List<LbsShopPo> shopPos) {
        shopPos.forEach(lbsShopDao::insertSelective);
    }

    private LbsShopPo buildLbsShopPo(Long formItemId, Map<String, LbsOptionDto> subHeader2Option) {
        String appkey = appkeyList.get(0);
        LbsOptionDto cityOption = subHeader2Option.get(LBSExcelColEnum.CITY.getCode());
        LbsOptionDto provinceOption = subHeader2Option.get(LBSExcelColEnum.PROVINCE.getCode());
        LbsOptionDto shopOption = subHeader2Option.get(LBSExcelColEnum.SHOP.getCode());
        String product = subHeader2Option.getOrDefault(LBSExcelColEnum.PRODUCT.getCode(),
                new LbsOptionDto()).getName();

        if (cityOption != null && provinceOption != null) {
            Assert.isTrue(cityOption.getId().toString().substring(0, 2)
                            .equals(provinceOption.getId().toString().substring(0, 2)),
                    cityOption.getName() + "," + provinceOption.getName() + "城市和省份不匹配,请检查后重新提交");
        }

        //如果excel包含门店
        if (shopOption != null) {
            Assert.notNull(cityOption, "有门店,城市必填");
            LocationInfoDto location = queryAndAddAddressInfo(shopOption.getAddress(), cityOption.getName(), appkey);
            return LbsShopPo.builder().cityCode(cityOption.getId().toString())
                    .lng(new BigDecimal(location.getLng()))
                    .lat(new BigDecimal(location.getLat()))
                    .shopAddress(shopOption.getAddress())
                    .product(product).formItemId(formItemId)
                    .provinceCode(cityOption.getId().toString().substring(0, 2) + "0000")
                    .shopName(shopOption.getName()).build();
        } else if (cityOption != null) {
            Pair<String, String> location = getLocationByAreaCode(cityOption.getId().toString());
            return LbsShopPo.builder()
                    .cityCode(cityOption.getId().toString())
                    .lng(new BigDecimal(location.getFirst()))
                    .lat(new BigDecimal(location.getSecond()))
                    .product(product).formItemId(formItemId)
                    //有一些用户上传的门店数据仅仅只有 城市+门店,为了方便后续的定位，我们把省份补齐，多返回的省份，前端能把他隐藏掉
                    .provinceCode(cityOption.getId().toString().substring(0, 2) + "0000")
                    .build();
        } else if (provinceOption != null) {
            Pair<String, String> location = getLocationByAreaCode(provinceOption.getId().toString());
            return LbsShopPo.builder()
                    .lng(new BigDecimal(location.getFirst()))
                    .lat(new BigDecimal(location.getSecond()))
                    .product(product).formItemId(formItemId)
                    .provinceCode(provinceOption.getId().toString())
                    .build();
        }
        throw new IllegalArgumentException("lbs的下拉项至少要包含省份");
    }

    private Pair<String, String> getLocationByAreaCode(String code) {
        String cacheKey = AREA_LOCATION_CACHE_PRFIX + code;
        String location = redisService.getCache(cacheKey);
        if (StringUtils.isEmpty(location)) {
            LbsAreaPoExample poExample = new LbsAreaPoExample();
            poExample.or().andAreaCodeEqualTo(code).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            List<LbsAreaPo> areaPos = areaDao.selectByExample(poExample);
            Assert.notEmpty(areaPos, "未识别的区域码" + code);
            LbsAreaPo areaPo = areaPos.get(0);
            redisService.addCache(cacheKey, areaPo.getLng() + "_" + areaPo.getLat(), 360000000);
            return Pair.of(areaPo.getLng().toString(), areaPo.getLat().toString());
        } else {
            String[] locationArray = location.split("_");
            return Pair.of(locationArray[0], locationArray[1]);
        }
    }

    private List<LbsOptionDto> buildChildOptions(List<Map<String, Set<String>>> parent2ChildList,
                                                 Map<String, LbsOptionDto> nameAndLevel2Option,
                                                 int index,
                                                 String parent) {
        if (index >= parent2ChildList.size()) {
            return null;
        }
        Map<String, Set<String>> parent2Child = parent2ChildList.get(index);
        Set<String> sons = parent2Child.get(parent);
        List<LbsOptionDto> res = new ArrayList<>();
        if (!CollectionUtils.isEmpty(sons)) {
            int next = index + 1;
            sons.forEach(s -> {
                LbsOptionDto optionDto = nameAndLevel2Option.get(s);
                if (StringUtils.hasText(optionDto.getAddress())) {
                    optionDto.setAddress(null);
                }
                optionDto.setChildren(buildChildOptions(parent2ChildList, nameAndLevel2Option, next, s));
                res.add(optionDto);
            });
        }
        return res;

    }

    private List<String> analyzeExcelHeader(Object[] header) {
        Assert.isTrue(header != null && header.length > 0, "文件头不能为空");

        List<String> headerList = new ArrayList<>();
        Map<String, Integer> col2Seq = new HashMap<>();
        int i = 0;
        String colHeader;
        for (Object obj : header) {
            i++;
            colHeader = String.valueOf(obj);
            col2Seq.put(colHeader, i);
            headerList.add(LBSExcelColEnum.getByCode(colHeader).getCode());
        }
        Assert.isTrue(headerList.contains(LBSExcelColEnum.CITY.getCode()) ==
                headerList.contains(LBSExcelColEnum.CITY_CODE.getCode()), "城市和城市码必须同时存在");
        Assert.isTrue(headerList.contains(LBSExcelColEnum.PROVINCE.getCode()) ==
                headerList.contains(LBSExcelColEnum.PROVINCE_CODE.getCode()), "省份和省份码必须同时存在");
        //省份必须要放在城市的前面
        if (headerList.contains(LBSExcelColEnum.CITY.getCode())) {
            Assert.isTrue(col2Seq.getOrDefault(LBSExcelColEnum.PROVINCE.getCode(), 0)
                    < col2Seq.get(LBSExcelColEnum.CITY.getCode()), "省份必须要放在城市的前面");
            Assert.isTrue(col2Seq.get(LBSExcelColEnum.CITY_CODE.getCode())
                    - col2Seq.get(LBSExcelColEnum.CITY.getCode()) == 1, "城市码必须和城市临近,并在其后一列");
        }
        if (headerList.contains(LBSExcelColEnum.PROVINCE.getCode())) {
            Assert.isTrue(col2Seq.get(LBSExcelColEnum.PROVINCE_CODE.getCode())
                    - col2Seq.get(LBSExcelColEnum.PROVINCE.getCode()) == 1, "省份码必须和省份临近,并在其后一列");
        }
        if (headerList.contains(LBSExcelColEnum.SHOP.getCode())) {
            Assert.isTrue(headerList.contains(LBSExcelColEnum.CITY.getCode()), "城市和门店必须同时存在");
            Assert.isTrue(col2Seq.get(LBSExcelColEnum.CITY.getCode())
                    < col2Seq.get(LBSExcelColEnum.SHOP.getCode()), "城市必须要放在门店的前面");
        }
        if (headerList.contains(LBSExcelColEnum.PRODUCT.getCode())) {
            Assert.isTrue(col2Seq.get(LBSExcelColEnum.PRODUCT.getCode()) == 1, "产品必须放在第一列");
            Assert.isTrue(headerList.size() > 1,
                    "仅包含产品的下拉组件请勿使用LBS,可以使用基础下拉选项");
        }
        return headerList;
    }

    private void refreshLbsFormData(String options, Long formItemId) {
        LbsDropDown dropDown = GsonUtils.toObject(options, LbsDropDown.class);
        String appkey = appkeyList.get(0);
        int total = 0;

        for (LbsSubDropDown product : dropDown.getChildren()) {
            for (LbsSubDropDown province : product.getChildren()) {
                for (LbsSubDropDown city : province.getChildren()) {
                    for (LbsSubDropDown shop : city.getChildren()) {
                        total++;
                    }
                }
            }
        }

        int current = 0;
        Map<Integer, Integer> reportPoint2Ratio = new HashMap<>();
        reportPoint2Ratio.put(total / 10, 10);
        reportPoint2Ratio.put(total / 5, 20);
        reportPoint2Ratio.put((total / 5) * 2, 40);
        reportPoint2Ratio.put((total / 5) * 3, 60);
        reportPoint2Ratio.put((total / 5) * 4, 80);
        for (LbsSubDropDown product : dropDown.getChildren()) {
            for (LbsSubDropDown province : product.getChildren()) {
                for (LbsSubDropDown city : province.getChildren()) {
                    for (LbsSubDropDown shop : city.getChildren()) {
                        current++;
                        if (reportPoint2Ratio.containsKey(current)) {
                            String res = redisService.getCache(LBS_REDIS_PREFIX + formItemId);
                            if (StringUtils.hasText(res)) {
                                MgkFormLbsDto lbsDto = GsonUtils.toObject(res, MgkFormLbsDto.class);
                                lbsDto.setProcessingProgress(reportPoint2Ratio.get(current));
                                redisService.addCache(LBS_REDIS_PREFIX + formItemId,
                                        GsonUtils.toJson(lbsDto), TH_CACHE_TIME);
                            }
                        }
                        try {
                            LocationInfoDto locationInfoDto = queryAndAddAddressInfo(shop.getAddress(), city.getName(), appkey);
                            if (locationInfoDto != null) {
                                lbsShopDao.insertUpdateSelective(LbsShopPo.builder()
                                        .cityCode(city.getId().toString()).lng(new BigDecimal(locationInfoDto.getLng()))
                                        .lat(new BigDecimal(locationInfoDto.getLat())).product(product.getName())
                                        .formItemId(formItemId).provinceCode(province.getId().toString())
                                        .shopName(shop.getName()).shopAddress(shop.getAddress()).build());
                            }
                        } catch (Exception e) {
                            log.error("refreshLbsFormData" + formItemId + " error" + Throwables.getStackTraceAsString(e));
                            String res = redisService.getCache(LBS_REDIS_PREFIX + formItemId);
                            if (StringUtils.hasText(res)) {
                                MgkFormLbsDto lbsDto = GsonUtils.toObject(res, MgkFormLbsDto.class);
                                lbsDto.setDealStatus(DealStatusEnum.FAILED.getCode());
                                lbsDto.setFailMsg("定位门店失败,表单项id为" + formItemId + "," +
                                        "失败门店名称为" + shop.getName() + ",可能的失败原因为:" + e.getMessage());
                                redisService.addCache(LBS_REDIS_PREFIX + formItemId,
                                        GsonUtils.toJson(lbsDto), TH_CACHE_TIME);
                            }
                            throw new IllegalArgumentException("refreshLbsFormData fail" + formItemId + " shopName" + shop.getName()
                                    + Throwables.getStackTraceAsString(e));
                        }
                    }
                }
            }
        }

        MgkFormLbsDto lbsDto = MgkFormLbsDto.builder()
                .dealStatus(DealStatusEnum.FINISHED.getCode()).processingProgress(100)
                .formItemId(formItemId).build();
        redisService.addCache(LBS_REDIS_PREFIX + formItemId, GsonUtils.toJson(lbsDto), TH_CACHE_TIME);
    }

    public LocationInfoDto queryAddressInfo(String address, String cityName) {
        Assert.hasText(address, "门店地址为必传项.不能为空");

        for (String rp : replaceNameList) {
            address = address.replace(rp, "");
        }
        LbsAddressPoExample addressPoExample = new LbsAddressPoExample();
        addressPoExample.or().andShopAddressEqualTo(address);
        LbsAddressPo addressPo = addressDao.selectByExample(addressPoExample).stream()
                .findFirst().orElse(null);
        if (addressPo != null) {
            return LocationInfoDto.builder().lat(addressPo.getLat().toString())
                    .lng(addressPo.getLng().toString())
                    .shopAddress(address)
                    .build();
        }
        MapGeoRes geoRes = OkHttpUtils.get(ADDRESS_QUERY_URL)
                .param("query", address)
                .param("region", cityName)
                .param("output", "json")
                .param("ak", appKey)
                .callForObject(MapGeoRes.class);

        Assert.isTrue(0 == geoRes.getStatus(), address + "查询百度地图服务失败,失败原因" + geoRes.getMessage());
        Assert.isTrue(!CollectionUtils.isEmpty(geoRes.getResults()), address + "查询百度地图服务失败");
        MapGeoRes.Result result = geoRes.getResults().get(0);

        MapGeoRes.Location location = result.getLocation();
        Assert.isTrue(location != null, address + "查询百度地图服务失败");

        return LocationInfoDto.builder().lat(location.getLat().toString())
                .lng(location.getLng().toString())
                .shopAddress(address)
                .build();
    }

    private LocationInfoDto queryAndAddAddressInfo(String address, String cityName, String appkey) {
        Assert.hasText(address, "门店地址为必传项.不能为空");
        for (String rp : replaceNameList) {
            address = address.replace(rp, "");
        }
        LbsAddressPoExample addressPoExample = new LbsAddressPoExample();
        addressPoExample.or().andShopAddressEqualTo(address);
        LbsAddressPo addressPo = addressDao.selectByExample(addressPoExample).stream()
                .findFirst().orElse(null);
        if (addressPo != null) {
            return LocationInfoDto.builder().lat(addressPo.getLat().toString())
                    .lng(addressPo.getLng().toString())
                    .shopAddress(address)
                    .build();
        }
        MapGeoRes geoRes = OkHttpUtils.get(ADDRESS_QUERY_URL)
                .param("query", address)
                .param("region", cityName)
                .param("output", "json")
                .param("ak", appkey)
                .callForObject(MapGeoRes.class);

        Assert.isTrue(0 == geoRes.getStatus(), address + "查询百度地图服务失败,失败原因" + geoRes.getMessage());
        Assert.isTrue(!CollectionUtils.isEmpty(geoRes.getResults()), address + "查询百度地图服务失败");
        MapGeoRes.Result result = geoRes.getResults().get(0);

        MapGeoRes.Location location = result.getLocation();
        Assert.isTrue(location != null, address + "查询百度地图服务失败");
        addressDao.insertSelective(LbsAddressPo.builder().lat(BigDecimal.valueOf(location.getLat()))
                .lng(BigDecimal.valueOf(location.getLng()))
                .shopAddress(address).build());
        return LocationInfoDto.builder().lat(location.getLat().toString())
                .lng(location.getLng().toString())
                .shopAddress(address)
                .build();
    }

    public void changeShopName(Long formItemId, String old, String newOne) {
        LbsShopPoExample shopExample = new LbsShopPoExample();
        shopExample.or().andShopNameEqualTo(old)
                .andFormItemIdEqualTo(formItemId);
        List<LbsShopPo> shopPos = lbsShopDao.selectByExample(shopExample);
        if (!CollectionUtils.isEmpty(shopPos)) {
            LbsShopPo shopPo = shopPos.get(0);
            shopPo.setShopName(newOne);
            shopPo.setMtime(new Timestamp(System.currentTimeMillis()));
            lbsShopDao.updateByPrimaryKeySelective(shopPo);
        }
    }

    public void addShop(String shopName, String shopAddress, String lng, String lat) {
        lbsShopDao.insertUpdateSelective(LbsShopPo.builder()
                .cityCode("0").lng(new BigDecimal(lng))
                .lat(new BigDecimal(lat)).product("aa")
                .formItemId(999L).provinceCode("0")
                .shopName(shopName).shopAddress(shopAddress).build());
    }
}
