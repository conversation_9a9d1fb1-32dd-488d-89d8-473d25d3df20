package com.bilibili.mgk.platform.biz.service;

import com.bilibili.bjcom.mock.BeanTestUtils;
import com.bilibili.bjcom.mock.MockitoDefaultTest;
import com.bilibili.mgk.platform.api.landing_page.dto.LandingPageConfigDto;
import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageService;
import com.bilibili.mgk.platform.biz.dao.ext.ExtMgkLandingPageDao;
import com.bilibili.mgk.platform.biz.soa.SoaLandingPageServiceImpl;
import com.bilibili.mgk.platform.common.LandingPageStatusEnum;
import com.bilibili.mgk.platform.common.MgkConstants;
import com.bilibili.mgk.platform.common.MgkJumpTypeEnum;
import com.bilibili.mgk.platform.common.page_bean.MgkLandingPageBean;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2018/1/23
 **/
public class SoaLandingPageServiceImplTest extends MockitoDefaultTest {

    @Mock
    private ExtMgkLandingPageDao extMgkLandingPageDao;

    @Mock
    private IMgkLandingPageService mgkLandingPageService;

    @InjectMocks
    private SoaLandingPageServiceImpl landingPageService;

    @Before
    public void setUp() throws Exception {
        when(extMgkLandingPageDao.selectPageIdsByExample(any())).thenReturn(Lists.newArrayList(1L));
    }

    @Test
    public void getPublishedLandingPageIdsTest() {
        List<Long> publishedPageIds = landingPageService.getPublishedLandingPageIds(Collections.singletonList(1L));
        assertEquals(1, publishedPageIds.size());
    }

    @Test
    public void validatePageIdAndGetLandingPage() {
        when(mgkLandingPageService.getLandingPageConfigDtoByPageId(anyLong()))
                .thenReturn(BeanTestUtils.initSimpleFields(LandingPageConfigDto.builder()
                        .status(LandingPageStatusEnum.PUBLISHED.getCode())
                        .build()));
        MgkLandingPageBean mgkLandingPageBean = landingPageService.validatePageIdAndGetLandingPage(MgkJumpTypeEnum.PAGE_ID.getCode(), "1");
        assertTrue(mgkLandingPageBean.getLaunchUrl().startsWith(MgkConstants.HTTPS_SCHEME));
    }

    @Test
    public void testGetAdVersionControlIdByPageId() {
        mgkLandingPageService.getAdVersionControlIdByPageId(1L);
    }

}