package com.bilibili.mgk.platform.biz.soa;

import com.bilibili.bjcom.mock.BeanTestUtils;
import com.bilibili.mgk.platform.api.hot_video.dto.QueryHotVideoDto;
import com.bilibili.mgk.platform.api.hot_video.service.IHotVideoService;
import com.bilibili.mgk.platform.biz.BaseMockitoTest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

/**
 * @file: SoaHotAdsServiceImplTest
 * @author: gaoming
 * @date: 2021/01/15
 * @version: 1.0
 * @description:
 **/
public class SoaHotAdsServiceImplTest extends BaseMockitoTest {

    @InjectMocks
    private SoaHotVideoServiceImpl soaHotVideoService;

    @Mock
    private IHotVideoService hotVideoService;

    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void testGetHotAdsDtos() {
        QueryHotVideoDto queryHotVideoDto = BeanTestUtils.initSimpleFields(QueryHotVideoDto.builder().build());
        soaHotVideoService.getHotVideoDtos(operator, queryHotVideoDto);
    }
}