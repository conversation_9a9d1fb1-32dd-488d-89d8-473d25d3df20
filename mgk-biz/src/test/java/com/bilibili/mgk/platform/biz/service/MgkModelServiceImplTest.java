package com.bilibili.mgk.platform.biz.service;

import com.bilibili.adp.common.util.Page;
import com.bilibili.mgk.platform.api.landing_page.dto.NewLandingPageDto;
import com.bilibili.mgk.platform.api.model.dto.*;
import com.bilibili.mgk.platform.biz.BaseMockitoTest;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.redisson.RedissonLock;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.sql.Timestamp;
import java.util.concurrent.locks.Lock;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2020/08/10
 **/
public class MgkModelServiceImplTest extends BaseMockitoTest {
    @InjectMocks
    private MgkModelServiceImpl mgkModelService;
    @Mock
    private ModelServiceDelegate modelServiceDelegate;
    @Mock
    private MgkBaseService mgkBaseService;
    @Mock
    private RedissonClient redissonClient;
    @Mock
    private RedissonLock redissonLock;


    public void setUp() throws Exception {
    }
//
//    @Test
//    public void testGetModelDtos() {
//        QueryModelParamDto paramDto = QueryModelParamDto.builder()
//                .accountIds(Lists.newArrayList(1))
//                .isDeleted(1)
//                .modelIds(Lists.newArrayList(1L))
//                .pageIds(Lists.newArrayList(1L))
//                .orderBy("")
//                .tradeIds(Lists.newArrayList(1L))
//                .statusList(Lists.newArrayList(1))
//                .nameLike("")
//                .modelStyles(Lists.newArrayList(1))
//                .modelTypes(Lists.newArrayList(1))
//                .build();
//        mgkModelService.getModelDtos(paramDto);
//    }

    @Test
    public void testTestGetModelDtos() {
        QueryModelParamDto paramDto = QueryModelParamDto.builder()
                .accountIds(Lists.newArrayList(1))
                .isDeleted(1)
                .modelIds(Lists.newArrayList(1L))
                .pageIds(Lists.newArrayList(1L))
                .orderBy("")
                .tradeIds(Lists.newArrayList(1L))
                .statusList(Lists.newArrayList(1))
                .nameLike("")
                .modelStyles(Lists.newArrayList(1))
                .modelTypes(Lists.newArrayList(1))
                .build();
        mgkModelService.getModelDtos(paramDto, Page.valueOf(1, 15));
    }

    @Test
    public void testCreate() {
        NewModelDto dto = NewModelDto.builder()
                .modelVersion("0x0x0x")
                .newLandingPageDto(NewLandingPageDto.builder()
                        .pageVersion("0x0x0x")
                        .modelId(1L)
                        .isModel(1)
                        .formIds(Lists.newArrayList(1L))
                        .formId(1L)
                        .showUrls(Lists.newArrayList(""))
                        .appPackageId(1)
                        .avIds(Lists.newArrayList(1L))
                        .type(1)
                        .config("")
                        .effectiveStartTime(new Timestamp(System.currentTimeMillis()))
                        .effectiveEndTime(new Timestamp(System.currentTimeMillis()))
                        .templateStyle(1)
                        .title("1")
                        .name("1")
                        .accountId(1)
                        .originPageId(1L)
                        .appPackageIds(Lists.newArrayList(1))
                        .build())
                .tradeIds(Lists.newArrayList(1L))
                .remark("")
                .coverUrl("www.bilibili.com")
                .modelType(1)
                .modelStyle(1)
                .modelName("1")
                .accountId(1)
                .build();
        mgkModelService.create(operator, dto);
    }

    @Test
    public void testGetTradeDtos() {
        QueryTradeParamDto paramDto = QueryTradeParamDto.builder()
                .isDeleted(1)
                .tradeIds(Lists.newArrayList(1L))
                .nameLike("")
                .level(Lists.newArrayList(1))
                .parentTradeIds(Lists.newArrayList(1L))
                .orderBy("")
                .build();
        mgkModelService.getTradeDtos(paramDto);
    }

    @Test
    public void testGetModelTradeMappingDtos() {
        QueryModelTradeMappingParamDto paramDto = QueryModelTradeMappingParamDto.builder()
                .isDeleted(1)
                .modelIds(Lists.newArrayList(1L))
                .tradeIds(Lists.newArrayList(1L))
                .build();
        mgkModelService.getModelTradeMappingDtos(paramDto);
    }

    @Test
    public void testCreateTrade() {
        NewTradeDto dto = NewTradeDto.builder()
                .tradeId(1L)
                .parentTradeId(1L)
                .tradeLevel(1)
                .tradeName("12")
                .build();
        mgkModelService.createTrade(operator, dto);
    }

    @Test
    public void testUpdate() {
        UpdateModelDto dto = UpdateModelDto.builder()
                .modelId(1L)
                .coverUrl("www.bilibili.com")
                .remark("")
                .tradeIds(Lists.newArrayList(1L))
                .modelName("")
                .build();
        when(mgkBaseService.getLock(any(), any())).thenReturn(redissonLock);
        mgkModelService.update(operator, dto);
    }

    @Test
    public void testPublish() {
        when(mgkBaseService.getLock(any(), any())).thenReturn(redissonLock);
        mgkModelService.publish(operator, 1L);
    }

    @Test
    public void testDownline() {
        when(mgkBaseService.getLock(any(), any())).thenReturn(redissonLock);
        mgkModelService.downline(operator, 1L);
    }

    @Test
    public void testBatchDisable() {
        when(mgkBaseService.getLock(any(), any())).thenReturn(redissonLock);
        mgkModelService.batchDisable(operator, Lists.newArrayList(1L));
    }

    @Test
    public void testHasRights() {
        when(mgkBaseService.getLock(any(), any())).thenReturn(redissonLock);
        mgkModelService.hasRights(operator);
    }

    @Test
    public void testGetModelUsed() {
        mgkModelService.getModelUsed(operator);
    }
}