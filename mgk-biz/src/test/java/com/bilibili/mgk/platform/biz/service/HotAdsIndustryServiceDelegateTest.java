package com.bilibili.mgk.platform.biz.service;

import com.bilibili.mgk.platform.biz.BaseMockitoTest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

/**
 * @file: HotAdsIndustryServiceDelegateTest
 * @author: gaoming
 * @date: 2021/01/15
 * @version: 1.0
 * @description:
 **/
public class HotAdsIndustryServiceDelegateTest extends BaseMockitoTest {

    @InjectMocks
    private HotAdsIndustryServiceDelegate hotAdsIndustryServiceDelegate;

    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void testGetIndustry() {
        hotAdsIndustryServiceDelegate.getIndustry();
    }
}