CREATE TABLE `mgk_material_landing_page`
(
    `id`                   int(11) unsigned    NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `account_id`           bigint(11) unsigned NOT NULL DEFAULT '0' COMMENT '账号ID',
    `page_id`              bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '页面ID',
    `name`                 varchar(32)         NOT NULL DEFAULT '' COMMENT '落地页名称',
    `title`                varchar(16)         NOT NULL DEFAULT '' COMMENT '页面标题',
    `template_style`       int(11) unsigned    NOT NULL DEFAULT '0' COMMENT '模板样式：1-浮层样式 2-图文样式 3-橱窗样式 4-视频样式 100-自定义  201-全屏图片 202-半屏视频  203-全屏视频  204半屏视频',
    `effective_start_time` timestamp           NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '生效开始时间',
    `effective_end_time`   timestamp           NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '生效结束时间',
    `status`               tinyint(4)          NOT NULL DEFAULT '0' COMMENT '状态: 1-未发布 2-已发布 3-已下线 4-管理员驳回 5-已删除',
    `creator`              varchar(256)        NOT NULL DEFAULT '' COMMENT '创建人',
    `reason`               varchar(255)        NOT NULL DEFAULT '' COMMENT '删除或下线原因',
    `is_deleted`           tinyint(4)          NOT NULL DEFAULT '0' COMMENT '软删除: 0-有效 1-删除',
    `ctime`                timestamp           NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
    `mtime`                timestamp           NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `type`                 tinyint(4)          NOT NULL DEFAULT '1' COMMENT '落地页类型1:H5 2:原生 3:自定义原生 4:支持小程序',
    `form_id`              bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '表单ID,已废弃，不要使用',
    `is_model`             int(11)             NOT NULL DEFAULT '0' COMMENT '落地页模型 0-普通落地页 1-落地页模板 2-创意联投模板 3-去除视频副本 4-评论区暗投',
    `model_id`             bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '模型id',
    `page_version`         varchar(32)         NOT NULL DEFAULT '' COMMENT '落地页版本',
    `video_duration`       int(11) unsigned    NOT NULL DEFAULT '0' COMMENT '落地页视频时长（第一个）',
    `is_pc`                tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '是否支持pc 0-不支持 1-支持',
    `header`               tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '页面是否含有header 0-没有 1-有',
    `has_transition`       tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '是否需要转场 0-不需要 1-需要',
    `transition`           varchar(128)        NOT NULL DEFAULT '' COMMENT '转场信息',
    `page_cover`           varchar(128)        NOT NULL DEFAULT '' COMMENT '封面地址',
    `page_bg_color`        varchar(45)         NOT NULL DEFAULT '' COMMENT '落地页背景颜色',
    `page_bg_url`          varchar(128)        NOT NULL DEFAULT '' COMMENT '落地页背景地址',
    `is_form_scroll`       tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '落地页是否包含滚动信息 0-不包含 1-包含',
    `has_dpa_goods`        tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '是否包含动态商品组件 0-不包含 1-包含',
    `is_video_page`        tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '是否视频落地页 0-不是 1-是',
    `avid`                 bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '稿件avid',
    `version`              bigint(20)          NOT NULL DEFAULT '0' COMMENT '建站落地页编辑版本',
    `cvr_deal_status`      tinyint(4)          NOT NULL DEFAULT '0' COMMENT '是否经过cvr处理 0-未处理 1-已处理',
    `is_game_form_reserve` tinyint(4)          NOT NULL DEFAULT '0' COMMENT '是否是表单预约落地页 0-否 1-是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_page_id` (`page_id`),
    KEY `ix_account_id` (`account_id`),
    KEY `ix_effective_end_time` (`effective_end_time`),
    KEY `ix_mtime` (`mtime`),
    KEY `ix_form_id` (`form_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 26863
  DEFAULT CHARSET = utf8 COMMENT ='落地页信息表';