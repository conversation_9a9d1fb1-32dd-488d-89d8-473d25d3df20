CREATE TABLE advertisement_analysis
(
    id UInt64,
    title String,
    light_point String,
    pain_point String,
    media_app String,
    ad_category String,
    ad_target String,
    avid UInt64,
    bvid String,
    cover_img_url String,
    vertical_type String,
    frame_analysis_img_url_1 String,
    frame_analysis_text_1 String,
    frame_analysis_img_url_2 String,
    frame_analysis_text_2 String,
    frame_analysis_img_url_3 String,
    frame_analysis_text_3 String,
    first_industry_id String,
    first_industry_name String,
    second_industry_id String,
    second_industry_name String,
    first_item_category String,
    second_item_category String,
    data_agg_day_type String,
    pv String,
    ctr String,
    ctr_rank String,
    vv UInt64,
    vv_rank String,
    conversion_rate String,
    vv_incr UInt64,
    conversion_rate_incr String,
    delivery_time DateTime,
    ctime DateTime,
    mtime DateTime,
    log_date String
) ENGINE = MergeTree()
 ORDER BY (id);





insert into advertisement_analysis
( id,title,light_point
,pain_point,media_app,ad_category
,ad_target,avid,bvid
,cover_img_url,vertical_type,frame_analysis_img_url_1
,frame_analysis_text_1,frame_analysis_img_url_2,frame_analysis_text_2
,frame_analysis_img_url_3,frame_analysis_text_3,first_industry_id
,first_industry_name,second_industry_id,second_industry_name
,first_item_category,second_item_category,data_agg_day_type
,pv,ctr,ctr_rank
,vv,vv_rank,conversion_rate
,vv_incr,conversion_rate_incr,delivery_time
,ctime,mtime,log_date
)
values ( 1 , 'title_test' , 'light_test', 'paint_test', 'app_test', 'add_tst', 'target_tets', 1, 1 , 'img_url_test',
        'vertical', 'ana_img_1', 'ana_img_text_1' ,'ana_img_1', 'ana_img_text_1' ,'ana_img_1', 'ana_img_text_1' ,
        'ind_id', 'ind_name', 'ind_id2', 'ind_name2', 'item_cate_1' , 'item_cate_2', '3d', '1', '1', '1', 1, '1','1',
        1, '1' ,  '2024-01-01 10:00:00', '2024-01-01 10:00:00', '2024-01-01 10:00:00' , '20240101')
