CREATE TABLE `mgk_material_dynamic_schedule_job`
(
    `id`                            bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增Id',

    # 基础信息
    `uid`                  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '用户id',
    `title`                varchar(200)         NOT NULL DEFAULT '' COMMENT '标题',
    `content`              varchar(2000)         NOT NULL DEFAULT '' COMMENT '内容',
    `pics_raw_text`           text COMMENT '图片信息',

     # 调度结果
    `err_code`  int(11)  NOT NULL DEFAULT 0 COMMENT '错误码',
    `err_msg`                varchar(200)         NOT NULL DEFAULT '' COMMENT '错误信息',
    `dyn_id`                  bigint(20)  NOT NULL DEFAULT 0 COMMENT '动态id',
    `dyn_rid`                 bigint(20)  NOT NULL DEFAULT 0 COMMENT '动态rid',
    `dyn_type`                bigint(20)  NOT NULL DEFAULT 0 COMMENT '动态类型',

    # 调度信息
    `schedule_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '调度时间',
    `schedule_status` varchar(100) NOT NULL DEFAULT '' COMMENT '调度状态',

    `ctime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `mtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',


    PRIMARY KEY (`id`),
    KEY `idx_uid` (`uid`) USING BTREE,
    KEY `idx_schedule_status` (`schedule_status`) USING BTREE,
    KEY `idx_schedule_time` (`schedule_time`) USING BTREE,
    KEY `ix_mtime` (`mtime`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='图文动态定时发布任务';


