package com.bilibili.mgk.platform.portal.converter;

import com.bilibili.mgk.platform.common.page_bean.GameDto;
import com.bilibili.mgk.platform.portal.webapi.game.vo.GameVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/21 21:27
 */
@Mapper
public interface GameConverter {
    GameConverter MAPPER = Mappers.getMapper(GameConverter.class);

    GameDto toGameDto(GameVo vo);

    GameVo toGameVo(GameDto dto);

    List<GameVo> toGameVo(List<GameDto> dtoList);
    List<GameDto> toGameDto(List<GameVo> voList);
}
