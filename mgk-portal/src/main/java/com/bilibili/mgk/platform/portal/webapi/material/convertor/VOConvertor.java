package com.bilibili.mgk.platform.portal.webapi.material.convertor;

import com.bilibili.adp.launch.api.creative.dto.LauQualificationDto;
import com.bilibili.adp.passport.api.dto.GameDto;
import com.bilibili.mgk.material.center.service.asset.model.MaterialDynamicInfo;
import com.bilibili.mgk.material.center.service.asset.vo.RichTextDTO;
import com.bilibili.mgk.material.center.service.creative.model.MaterialInspirationCase;
import com.bilibili.mgk.material.center.service.creative.model.RisingFastVideo;
import com.bilibili.mgk.material.center.service.creative.vo.CreativeMaterialQuery;
import com.bilibili.mgk.material.center.service.creative.vo.CreativeMaterialQuery.IndustrySubConditions;
import com.bilibili.mgk.material.center.service.creative.vo.DateAggregation;
import com.bilibili.mgk.material.center.util.JsonUtil;
import com.bilibili.mgk.platform.api.app_package.dto.ResAppPackageDto;
import com.bilibili.mgk.platform.portal.webapi.material.vo.CreativeMaterialPageQueryVO;
import com.bilibili.mgk.platform.portal.webapi.material.vo.CreativeQualificationVo;
import com.bilibili.mgk.platform.portal.webapi.material.vo.DynamicBluelinkResolveContentDetailDTO;
import com.bilibili.mgk.platform.portal.webapi.material.vo.DynamicBluelinkResolveContentDetailDTO.AppPackageForLinkDTO;
import com.bilibili.mgk.platform.portal.webapi.material.vo.DynamicBluelinkResolveContentDetailDTO.GameInfoForLinkDTO;
import com.bilibili.mgk.platform.portal.webapi.material.vo.InspirationCaseDetailVO;
import com.bilibili.mgk.platform.portal.webapi.material.vo.InspirationCaseVO;
import com.bilibili.mgk.platform.portal.webapi.material.vo.MaterialDynamicDetailInfo;
import com.bilibili.mgk.platform.portal.webapi.material.vo.RichTextDetailDTO;
import com.bilibili.mgk.platform.portal.webapi.material.vo.RisingFastVideoDetailVO;
import com.bilibili.mgk.platform.portal.webapi.material.vo.RisingFastVideoVO;
import com.fasterxml.jackson.core.type.TypeReference;
import io.vavr.control.Try;
import java.util.Map;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/6
 */
@Mapper
public interface VOConvertor {

    VOConvertor convertor = Mappers.getMapper(VOConvertor.class);


    InspirationCaseVO toVO(MaterialInspirationCase inspirationCase);

    InspirationCaseDetailVO toDetail(MaterialInspirationCase inspirationCase);


    RisingFastVideoVO toVO(RisingFastVideo bo);

    RisingFastVideoDetailVO toDetailVO(RisingFastVideo bo);


    RichTextDetailDTO toDetail(RichTextDTO richTextDTO);

    DynamicBluelinkResolveContentDetailDTO toDetail(DynamicBluelinkResolveContentDetailDTO bluelink);

    MaterialDynamicDetailInfo toDetail(MaterialDynamicInfo bluelink);


    @Named(value = "deserializeIndustryFilters")
    static Map<String, IndustrySubConditions> deserializeIndustryFilters(String industryFilters) {
        return Try.of(() -> JsonUtil.readValue(industryFilters,
                new TypeReference<Map<String, IndustrySubConditions>>() {
                })).getOrNull();
    }

    @Named(value = "deserializeDayType")
    static DateAggregation deserializeDayType(String dayType) {
        return DateAggregation.fromParam(dayType);
    }

    @Mapping(source = "industryFilters", target = "industryFilters", qualifiedByName = "deserializeIndustryFilters")
    @Mapping(source = "dayType", target = "dayType", qualifiedByName = "deserializeDayType")
    CreativeMaterialQuery toQuery(CreativeMaterialPageQueryVO query);

    AppPackageForLinkDTO toDTO(ResAppPackageDto app);

    GameInfoForLinkDTO toDTO(GameDto game);

    CreativeQualificationVo toDTO(LauQualificationDto qua);

}
