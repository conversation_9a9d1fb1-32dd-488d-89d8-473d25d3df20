package com.bilibili.mgk.platform.portal.openapi.form.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @file: encryptedPhoneVo
 * @author: gaoming
 * @date: 2021/11/02
 * @version: 1.0
 * @description:
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class EncryptedPhoneVo {

    @ApiModelProperty(value = "来源app请求中的appkey参数")
    private String origin;

    @ApiModelProperty(value = "运营商 移动：mobile 联通：unicom 电信：telecom")
    private String carrier;

    @ApiModelProperty(value = "设备类型 用于区分pad和phone使用同一个appkey的场景")
    private String device;

    @ApiModelProperty(value = "设备相关参数 和buvid保持一致")
    private String localId;

    @ApiModelProperty(value = "设备相关参数")
    private String buvid;

    @ApiModelProperty(value = "电信必传")
    private String authCode;

    @ApiModelProperty(value = "用户授权临时凭证 对应电信的access_code参数")
    private String token;

    @ApiModelProperty(value = "来源页面地址 非必须")
    private String fromUrl;

    @ApiModelProperty(value = "用户mid 高数据分析价值 非必须")
    private String mid;

    @ApiModelProperty(value = "APP版本号")
    private String build;

    @ApiModelProperty(value = "联通运营商从小沃升级到了联通在线,客户端/服务端所有接入都不通用,都需要升级 " +
            "所以如果是新的客户端SDK取到的号， 一定要告诉我们carrier_ver=v2， 我们才能正确路由服务端逻辑 老的SDK可以不传carrier_ver参数")
    private String carrierVer;
}
