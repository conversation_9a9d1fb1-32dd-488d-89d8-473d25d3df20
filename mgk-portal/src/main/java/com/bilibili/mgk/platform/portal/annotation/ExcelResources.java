package com.bilibili.mgk.platform.portal.annotation;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * @ClassName ExcelResources
 * <AUTHOR>
 * @Date 2022/6/19 4:31 下午
 * @Version 1.0
 **/
@Retention(RetentionPolicy.RUNTIME)
public @interface ExcelResources {
    /**
     * 属性的标题名称
     * @return
     */
    String title();
    /**
     * 在excel的顺序
     * @return
     */
    int order() default 9999;

    /**
     * 当该列为空时跳过它
     */
    boolean skipIfEmpty() default true;
}
