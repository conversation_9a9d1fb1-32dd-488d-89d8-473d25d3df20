package com.bilibili.mgk.platform.portal.openapi.wechat.vo;


import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OpenAdVo {

    @ApiModelProperty(value = "track_id")
    private String track_id;

    @ApiModelProperty(value = "assembly_track_id")
    private String assembly_track_id;

}
