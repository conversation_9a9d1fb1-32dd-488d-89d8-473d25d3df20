package com.bilibili.mgk.platform.portal.webapi.collage.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/11/14
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PatternVo {

    @ApiModelProperty("模版id")
    private Integer id;

    @ApiModelProperty("模版名称")
    private String name;

    @ApiModelProperty("模版状态")
    private Integer status;

    @ApiModelProperty("模板版本")
    private Integer edition;

    @ApiModelProperty(value = "模板Id")
    private Long pattern_id;

    @ApiModelProperty("模版状态描述")
    private String status_desc;

    @ApiModelProperty("尺寸id")
    private Integer collage_size_id;

    @ApiModelProperty("尺寸name")
    private String collage_size_name;

    @ApiModelProperty("尺寸width")
    private Integer collage_width;

    @ApiModelProperty("尺寸height")
    private Integer collage_height;

    @ApiModelProperty("行业id")
    private String industry_ids;

    @ApiModelProperty("行业name")
    private String[] industry_names;

    @ApiModelProperty("渲染图片")
    private String render_image;

    @ApiModelProperty("创建人")
    private String creator;

    private Long mtime;

    private String mtime_desc;

    private List<LayerVo> layers;

    private List<LayerVo> custom_layers;

    @ApiModelProperty("尺寸比例")
    private Integer pattern_radio;

    @ApiModelProperty(value = "模板封面")
    private String pattern_cover;
}
