package com.bilibili.mgk.platform.portal.backdoor;

import com.bilibili.mgk.platform.api.conversion.MgkSecretService;
import com.bilibili.mgk.platform.api.conversion.bos.MgkSecretBo;
import com.bilibili.mgk.platform.portal.common.BaseController;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.exception.WebApiExceptionCode;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/backdoor/secret")
public class SecretController extends BaseController {
    @Value("${mgk.backdoor.enabled:false}")
    private Boolean enabled;

    private final MgkSecretService mgkSecretService;

    public SecretController(MgkSecretService mgkSecretService) {
        this.mgkSecretService = mgkSecretService;
    }

    @PostMapping("/generate")
    public Response<List<MgkSecretBo>> generateThenList(@RequestParam("aids") List<Integer> accountIds) {
        if (!enabled) return Response.failWithDetail(WebApiExceptionCode.NO_PERMISSION, null);
        final List<MgkSecretBo> secrets = mgkSecretService.insertOrIgnore(accountIds);
        return Response.SUCCESS(secrets);
    }

    @PostMapping("/refresh")
    public Response<List<MgkSecretBo>> refreshThenList(@RequestParam("aids") List<Integer> accountIds) {
        if (!enabled) return Response.failWithDetail(WebApiExceptionCode.NO_PERMISSION, null);
        return Response.SUCCESS(mgkSecretService.refresh(accountIds));
    }
}
