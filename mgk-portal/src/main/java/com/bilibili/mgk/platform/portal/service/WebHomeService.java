package com.bilibili.mgk.platform.portal.service;

import com.bilibili.mgk.platform.api.es.dto.MgkHomeDataDto;
import com.bilibili.mgk.platform.api.es.dto.PointDto;
import com.bilibili.mgk.platform.portal.util.UnitConversionUtils;
import com.bilibili.mgk.platform.portal.webapi.home.vo.MgkHomeDataVo;
import com.bilibili.mgk.platform.portal.webapi.home.vo.PointVo;
import org.springframework.stereotype.Service;

import java.util.stream.Collectors;

/**
 * @file: WebHomeService
 * @author: gaoming
 * @date: 2021/07/06
 * @version: 1.0
 * @description:
 **/
@Service
public class WebHomeService {
    public MgkHomeDataVo convertHomeDataDto2Vo(MgkHomeDataDto homeDataDto) {
        if (homeDataDto == null) {
            return null;
        }
        return MgkHomeDataVo.builder()
                .pv_week(UnitConversionUtils.thousandth(homeDataDto.getPvWeek()))
                .pv_week_rate(UnitConversionUtils.convertPercentage(homeDataDto.getPvWeekRate()))
                .pv_week_trend(homeDataDto.getPvWeekTrend())
                .pv_yesterday(UnitConversionUtils.thousandth(homeDataDto.getPvYesterday()))
                .pv_yesterday_rate(UnitConversionUtils.convertPercentage(homeDataDto.getPvYesterdayRate()))
                .pv_yesterday_trend(homeDataDto.getPvYesterdayTrend())
                .ctr_week(UnitConversionUtils.thousandth(homeDataDto.getCtrWeek()))
                .ctr_week_rate(UnitConversionUtils.convertPercentage(homeDataDto.getCtrWeekRate()))
                .ctr_week_trend(homeDataDto.getCtrWeekTrend())
                .ctr_yesterday(UnitConversionUtils.thousandth(homeDataDto.getCtrYesterday()))
                .ctr_yesterday_rate(UnitConversionUtils.convertPercentage(homeDataDto.getCtrYesterdayRate()))
                .ctr_yesterday_trend(homeDataDto.getCtrYesterdayTrend())
                .pvVo(homeDataDto.getPvDto().stream().map(this::convertPointDto2Vo).collect(Collectors.toList()))
                .ctrVo(homeDataDto.getCtrDto().stream().map(this::convertPointDto2Vo).collect(Collectors.toList()))
                .build();
    }

    private PointVo convertPointDto2Vo(PointDto pointDto) {
        return PointVo.builder()
                .time(pointDto.getTime())
                .value(pointDto.getValue())
                .value_format(UnitConversionUtils.thousandth(pointDto.getValue()))
                .build();
    }
}
