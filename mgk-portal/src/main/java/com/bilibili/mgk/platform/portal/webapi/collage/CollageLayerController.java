package com.bilibili.mgk.platform.portal.webapi.collage;

import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.service.WebCollageService;
import com.bilibili.mgk.platform.portal.webapi.collage.vo.CollageLayerCategoryVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/11/22
 * 图层种类
 **/
@RestController
@RequestMapping("/web_api/v1/collage/layer_category")
@Api(value = "/collage/layer_category", description = "拼贴艺术-图层种类管理")
public class CollageLayerController extends BasicController {

    @Autowired
    private WebCollageService webCollageService;

    @ApiOperation(value = "查询图层种类列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    public Response<List<CollageLayerCategoryVo>> getList() throws Exception {
        return Response.SUCCESS(webCollageService.collageLayerCategory2vo());
    }
}
