package com.bilibili.mgk.platform.portal.webapi.home.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @file: MgkHomeDataVo
 * @author: gaoming
 * @date: 2021/07/06
 * @version: 1.0
 * @description:
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MgkHomeDataVo {

    @ApiModelProperty(value = "七日pv数")
    private String pv_week;

    @ApiModelProperty(value = "七日pv数比率")
    private String pv_week_rate;

    @ApiModelProperty(value = "七日pv数上升下降趋势")
    private Integer pv_week_trend;

    @ApiModelProperty(value = "昨日pv数")
    private String pv_yesterday;

    @ApiModelProperty(value = "昨日pv数比率")
    private String pv_yesterday_rate;

    @ApiModelProperty(value = "昨日pv数上升下降趋势")
    private Integer pv_yesterday_trend;

    @ApiModelProperty(value = "七日ctr数")
    private String ctr_week;

    @ApiModelProperty(value = "七日ctr数比率")
    private String ctr_week_rate;

    @ApiModelProperty(value = "七日ctr数上升下降趋势")
    private Integer ctr_week_trend;

    @ApiModelProperty(value = "昨日ctr数")
    private String ctr_yesterday;

    @ApiModelProperty(value = "昨日ctr数比率")
    private String ctr_yesterday_rate;

    @ApiModelProperty(value = "昨日ctr数上升下降趋势")
    private Integer ctr_yesterday_trend;

    @ApiModelProperty(value = "pv数据")
    private List<PointVo> pvVo;

    @ApiModelProperty(value = "ctr数据")
    private List<PointVo> ctrVo;
}
