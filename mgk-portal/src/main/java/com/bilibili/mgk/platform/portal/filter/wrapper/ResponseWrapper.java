package com.bilibili.mgk.platform.portal.filter.wrapper;

import org.apache.commons.io.output.TeeOutputStream;

import javax.servlet.ServletOutputStream;
import javax.servlet.WriteListener;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * @ClassName ResponseWrapper
 * <AUTHOR>
 * @Date 2023/2/13 9:54 下午
 * @Version 1.0
 **/
public class ResponseWrapper extends HttpServletResponseWrapper {

    private ByteArrayOutputStream output;
    private ServletOutputStream filterOutput;

    public ResponseWrapper(HttpServletResponse response) {
        super(response);
        output = new ByteArrayOutputStream();
    }

    @Override
    public ServletOutputStream getOutputStream() throws IOException {
        if (filterOutput == null) {
            filterOutput = new ServletOutputStream() {

                private TeeOutputStream teeOutputStream = new TeeOutputStream(ResponseWrapper.super.getOutputStream(),output);

                @Override
                public void write(int b) throws IOException {
                    teeOutputStream.write(b);
                }

                @Override
                public boolean isReady() {
                    return false;
                }

                @Override
                public void setWriteListener(WriteListener writeListener) {
                }
            };
        }
        return filterOutput;
    }

    public byte[] toByteArray() {
        return output.toByteArray();
    }
}
