package com.bilibili.mgk.platform.portal.webapi.form.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/9/27
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MgkFormLbsVo {

    @ApiModelProperty(value = "lbs地址json")
    private String option;

    @ApiModelProperty(value = "lbs表单项id")
    private Long formItemId;

    @ApiModelProperty(value = "复制的时候 原有的lbs表单项id")
    private Long oldFormItemId;

}
