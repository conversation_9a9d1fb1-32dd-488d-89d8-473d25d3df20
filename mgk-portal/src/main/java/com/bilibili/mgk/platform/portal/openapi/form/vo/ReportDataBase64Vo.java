package com.bilibili.mgk.platform.portal.openapi.form.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2018/9/27
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReportDataBase64Vo {
    @ApiModelProperty(value = "Base64加密的表单提交数据")
    private String form_datas;

    @ApiModelProperty(value = "APPKEY")
    private String appkey;
}
