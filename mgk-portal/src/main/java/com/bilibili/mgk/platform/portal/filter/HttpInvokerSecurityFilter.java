package com.bilibili.mgk.platform.portal.filter;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @ClassName HttpInvokerSecurityFilter
 * <AUTHOR>
 * @Date 2022/6/19 3:54 下午
 * @Version 1.0
 **/
@Slf4j
public class HttpInvokerSecurityFilter implements Filter {
    private String HTTP_INVOKER_API_URL;

    public String getHTTP_INVOKER_API_URL() {
        return HTTP_INVOKER_API_URL;
    }

    public void setHTTP_INVOKER_API_URL(String HTTP_INVOKER_API_URL) {
        this.HTTP_INVOKER_API_URL = HTTP_INVOKER_API_URL;
    }

    private List<String> HTTP_INVOKER_DOMAIN;

    public List<String> getHTTP_INVOKER_DOMAIN() {
        return HTTP_INVOKER_DOMAIN;
    }

    public void setHTTP_INVOKER_DOMAIN(List<String> HTTP_INVOKER_DOMAIN) {
        this.HTTP_INVOKER_DOMAIN = HTTP_INVOKER_DOMAIN;
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        if (null == HTTP_INVOKER_API_URL) {
            HTTP_INVOKER_API_URL = "/service";
        }
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        if (httpRequest.getRequestURI().contains(HTTP_INVOKER_API_URL)) {
            if (!HTTP_INVOKER_DOMAIN.contains(httpRequest.getServerName())) {
                log.error("HttpInvokerSecurityFilter.url {}", httpRequest.getServerName());
                httpResponse.setStatus(HttpServletResponse.SC_FORBIDDEN);
                return;
            }
        }
        chain.doFilter(request, response);
    }

    @Override
    public void destroy() {

    }
}
