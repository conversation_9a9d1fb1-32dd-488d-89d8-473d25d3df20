package com.bilibili.mgk.platform.portal.filter;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.passport.api.dto.AuthInfoDto;
import com.bilibili.adp.passport.biz.service.PassportService;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.exception.WebApiExceptionCode;
import com.bilibili.mgk.platform.portal.openapi.bean.OpenApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class UpperOpenApiSecurityFilter implements Filter {

    private final PassportService passportService;

    private static final List<String> IGNORE_URL = new ArrayList<>();

    static {
        IGNORE_URL.add("/backdoor");
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) servletRequest;
        HttpServletResponse httpResponse = (HttpServletResponse) servletResponse;
        for (String ignore : IGNORE_URL) {
            if (httpRequest.getRequestURI().indexOf(ignore) > 0) {
                chain.doFilter(httpRequest, httpResponse);
                return;
            }
        }
        log.info("UpperOpenApiSecurityFilter begin");
        OpenApiResponse<String> error = securityCheck(httpRequest);
        if (null != error) {
            log.error("securityCheck 302 error != null error={}", error);
            httpResponse.setStatus(HttpServletResponse.SC_FORBIDDEN);
            httpResponse.setContentType("application/json");
            httpResponse.setCharacterEncoding("UTF-8");
            httpResponse.getWriter().write(JSON.toJSONString(error));
            httpResponse.getWriter().flush();
            return;
        }
        chain.doFilter(httpRequest, servletResponse);
    }

    private OpenApiResponse<String> securityCheck(HttpServletRequest request){
        String cookie = request.getHeader("Cookie");

        Context context;
        try {
            if (StringUtils.isEmpty(cookie)) {
                  return OpenApiResponse.FAIL(WebApiExceptionCode.UNAUTHORIZED);
            }
            AuthInfoDto authInfoDto = this.passportService.validateCookie(cookie);
            context = Context.builder()
                    .mid(authInfoDto.getMid())
                    .bilibiliUserName("")
                    .build();

            request.setAttribute("context", context);
        }catch (Exception e){
            log.error("securityCheck", e);
            return OpenApiResponse.FAIL(WebApiExceptionCode.UNAUTHORIZED);
        }
        return null;
    }

    @Override
    public void destroy() {

    }
}
