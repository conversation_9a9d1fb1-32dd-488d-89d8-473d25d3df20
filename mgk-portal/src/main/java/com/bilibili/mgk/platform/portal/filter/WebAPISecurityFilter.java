package com.bilibili.mgk.platform.portal.filter;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.AssertCheckUtils;
import com.bilibili.adp.common.util.TokenUtil;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.passport.api.dto.BidAuthInfoDto;
import com.bilibili.adp.passport.api.service.IPassportService;
import com.bilibili.commercialorder.api.common.enums.PickupOperatorType;
import com.bilibili.commercialorder.api.session.service.bid.bean.UpstreamContext;
import com.bilibili.commercialorder.soa.session.service.ISoaSessionService;
import com.bilibili.mgk.platform.api.jwt.IJwtService;
import com.bilibili.mgk.platform.common.enums.jwt.JwtParam;
import com.bilibili.mgk.platform.common.enums.jwt.TokenState;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.exception.WebApiExceptionCode;
import com.dianping.cat.Cat;
import com.google.common.base.Strings;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.crypto.MACVerifier;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Stream;
import javax.annotation.Resource;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.HttpMethod;
import lombok.Setter;
import org.apache.commons.lang.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

@Component
public class WebAPISecurityFilter implements Filter {

    public static final String HTTP_ACCESS_TOKEN = "HTTP-ACCESS-TOKEN";
    public static final String HTTP_SECRET_KEY = "HTTP-SECRET-KEY";
    public static final String HTTP_TOKEN = "_cm";
    public static final String EFFECT_AD_TOKEN = "_effectAd";
    public static final String CLUE_PASS_TOKEN = "_cluePass";
    public static final String PICKUP_TOKEN = "_pickup";
    public static final String BUSINESS_TOOL_TOKEN = "_busTool";
    public static final String COOKIE = "Cookie";
    public static final String WEB_API = "/web_api/v1";
//    public static final String BID_GREY = "bid-grey";
    private static final List<PickupOperatorType> PICKUP_SECOND_AGENT_OPERATOR_TYPES =
            Arrays.asList(PickupOperatorType.AGENT_LAUNCH_ADMIN, PickupOperatorType.SECONDARY_AGENT);

    private static final List<String> IGNORE_URL = new ArrayList<>();


    private SalesType salesType;

    public SalesType getSalesType() {
        return salesType;
    }

    public void setSalesType(SalesType salesType) {
        this.salesType = salesType;
    }

    private final static Logger logger = LoggerFactory.getLogger(WebAPISecurityFilter.class);
    private static MACVerifier verifier = null;

    @Setter
    private IPassportService passportService;
    @Setter
    private ISoaSessionService soaPickupSessionService;
    @Autowired
    private IJwtService jwtService;

    @Resource
    private LoginFreePatternRegistry loginFreePatternRegistry;

    @Value("${mgk-platform.interceptor.not.filter.url:}")
    private String NOT_FILTER_URL;

    static {
        SecretKey secretKeySpec = new SecretKeySpec(TokenUtil.getSecretKey(), "AES");
        try {
            verifier = new MACVerifier(secretKeySpec);
        } catch (JOSEException e) {
            logger.error("new MACVerifier.error", e);
            System.exit(0);
        }
        IGNORE_URL.add("/login");
        IGNORE_URL.add("/sale");
        IGNORE_URL.add("api-docs");
        IGNORE_URL.add("/register");
        IGNORE_URL.add("/password/reset");
        IGNORE_URL.add("/captcha");
        IGNORE_URL.add("/logout");
    }


    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException,
            ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        if (HttpMethod.OPTIONS.equals(httpRequest.getMethod())) {
            return;
        }
        for (String ignore : IGNORE_URL) {
            if (httpRequest.getRequestURI().indexOf(ignore) > 0) {
                chain.doFilter(request, response);
                return;
            }
        }

        Response<String> error = securityCheck(httpRequest);

        if (null != error) {

            if (loginFreePatternRegistry.validateLoginFreeUrl(httpRequest)) {
                chain.doFilter(request, response);
                return;
            }

            logger.error("securityCheck 302 error != null");
            httpResponse.setStatus(HttpServletResponse.SC_FORBIDDEN);
            httpResponse.setContentType("application/json");
            httpResponse.setCharacterEncoding("UTF-8");
            httpResponse.getWriter().write(JSON.toJSONString(error));
            httpResponse.getWriter().flush();
            return;
        }
        this.addCatLogEvent(httpRequest);
        chain.doFilter(request, response);
    }



    private void addCatLogEvent(HttpServletRequest httpRequest) {
        try {
            Context context = (Context) httpRequest.getAttribute("context");
            if (OperatorType.BILIBILIER.getCode() == context.getType()
                    && !Strings.isNullOrEmpty(context.getProxyName())) {
                Cat.logEvent("SYCP_" + context.getProxyName(), httpRequest.getRequestURI());
            }
        } catch (Exception e) {
            logger.error("addCatLogEvent failed", e);
        }
    }

    private Response<String> securityCheck(HttpServletRequest request) {
        String pickupToken = getTokenFromCookieByName(request, PICKUP_TOKEN);
        if (!StringUtils.isEmpty(pickupToken)) {
            Context context = buildContextByPickupToken(pickupToken);
            if (Objects.nonNull(context)) {
                request.setAttribute("context", context);
            }
            return null;
        }

        String busToolToken = getTokenFromCookieByName(request, BUSINESS_TOOL_TOKEN);
        if (!StringUtils.isEmpty(busToolToken)) {
            Map<String, Object> resultMap=jwtService.validToken(busToolToken);
            TokenState state=TokenState.getTokenState((String)resultMap.get(JwtParam.STATE.getCode()));
            switch (state) {
                case VALID:
                    Map<String, Object> dataMap = (Map<String, Object>) resultMap.get(JwtParam.DATA.getCode());
                    Context context = buildContextByBusinessToolToken(dataMap);
                    if (Objects.nonNull(context)) {
                        request.setAttribute("context", context);
                    }
                    return null;
                case EXPIRED:
                    return Response.FAIL(WebApiExceptionCode.TOKEN_EXPIRE);
                case INVALID:
                    return Response.FAIL(WebApiExceptionCode.UNAUTHORIZED);
            }
        }


        String token = request.getHeader(HTTP_ACCESS_TOKEN);
        if (StringUtils.isEmpty(token)) {
            token = this.getTokenFromCookie(request);
        }
        // 尝试获取线索通cookie
        if (StringUtils.isEmpty(token)) {
            token = this.getCluePassTokenFromCookie(request);
        }
        if (StringUtils.isEmpty(token)) {
            return Response.FAIL(WebApiExceptionCode.BAD_REQUEST);
        }
        BidAuthInfoDto bidAuthInfoDto;
        try {
            bidAuthInfoDto = withBidGreyCheck(request);
        } catch (Exception e) {
            return Response.FAIL(WebApiExceptionCode.UNAUTHORIZED);
        }
        SignedJWT signedJWT;
        try {
            signedJWT = SignedJWT.parse(token);
        } catch (ParseException e) {
            logger.error("SignedJWT.parse.error", e);
            return Response.FAIL(WebApiExceptionCode.SYSTEM_ERROR);
        }
        try {
            if (!signedJWT.verify(verifier)) {
                return Response.FAIL(WebApiExceptionCode.UNAUTHORIZED);
            }
        } catch (JOSEException e) {
            logger.error("signedJWT.verify.error", e);
            return Response.FAIL(WebApiExceptionCode.SYSTEM_ERROR);
        }

        try {
            if (Utils.getNow().getTime() > signedJWT.getJWTClaimsSet().getExpirationTime().getTime()) {
                logger.info("token is expire token{}", token);
                return Response.FAIL(WebApiExceptionCode.TOKEN_EXPIRE);
            }
            Context context = new Context(
                    Integer.valueOf(signedJWT.getJWTClaimsSet().getJWTID()),
                    signedJWT.getJWTClaimsSet().getIssuer(),
                    salesType.getCode(),
                    signedJWT.getJWTClaimsSet().getIntegerClaim(TokenUtil.PROXY_ID),
                    signedJWT.getJWTClaimsSet().getStringClaim(TokenUtil.PROXY_NAME),
                    signedJWT.getJWTClaimsSet().getIntegerClaim(TokenUtil.TYPE),
                    signedJWT.getJWTClaimsSet().getLongClaim(TokenUtil.MID));
            try {
                checkAndDecorateWithBid(bidAuthInfoDto, signedJWT.getJWTClaimsSet(), context);
            } catch (Exception e) {
                return Response.FAIL(WebApiExceptionCode.BID_LOGIN_INVALID);
            }
            request.setAttribute("context", context);
        } catch (ParseException e) {
            logger.error("signedJWT.getJWTClaimsSet().error", e);
            return Response.FAIL(WebApiExceptionCode.SYSTEM_ERROR);
        }

        return null;
    }

    public void test(String busToolToken){
        Map<String, Object> resultMap=jwtService.validToken(busToolToken);
        TokenState state=TokenState.getTokenState((String)resultMap.get("state"));
        Map<String, Object> dataMap = (Map<String, Object>) resultMap.get(JwtParam.DATA.getCode());
        Context context = buildContextByBusinessToolToken(dataMap);
        System.out.println(context);
    }

    private void checkAndDecorateWithBid(BidAuthInfoDto bidAuthInfoDto, JWTClaimsSet claimsSet, Context context) throws ParseException {
        if (bidAuthInfoDto == null){
            //非灰度，不需要校验
            return;
        }
        Long bid = claimsSet.getLongClaim(TokenUtil.BID);
        AssertCheckUtils.notNull(bid, "商业账号登录失效，请重新登录");
        AssertCheckUtils.isTrue(ObjectUtils.nullSafeEquals(bid, bidAuthInfoDto.getBid()), "商业账号登录失效，请重新登录");
        context.setBid(bid);
    }

    private BidAuthInfoDto withBidGreyCheck(HttpServletRequest httpRequest) throws ServiceException {
//        String headerHasBid = httpRequest.getHeader(BID_GREY);
//        String queryHasBid = httpRequest.getParameter(BID_GREY);
//        boolean hasBidGrey = Objects.nonNull(headerHasBid) || Objects.nonNull(queryHasBid);
//        logger.info("withBidGreyCheck with hasBid={}", headerHasBid);
//        if (!hasBidGrey){
//            return null;
//        }
        //内网过滤
        if (null != NOT_FILTER_URL) {
            String[] urls = NOT_FILTER_URL.split(",");
            List<String> urlList = Arrays.asList(urls);

            if (urlList.contains(httpRequest.getServerName())) {
                return null;
            }
        }

        final String cookie = httpRequest.getHeader("Cookie");
        BidAuthInfoDto bidAuthInfoDto = passportService.validateBidCookie(cookie);
        logger.info("withBidGreyCheck with bidAuthInfoDto={}", bidAuthInfoDto);
        AssertCheckUtils.isTrue(bidAuthInfoDto != null && bidAuthInfoDto.getBid() != null, WebApiExceptionCode.UNAUTHORIZED);
        return bidAuthInfoDto;
    }

    @Override
    public void destroy() {

    }

    private Context buildContextByPickupToken(String pickupToken) {
        if (StringUtils.isEmpty(pickupToken)) {
            return null;
        }
        UpstreamContext pickUpContext = soaPickupSessionService.parseAndBuildContext(pickupToken);
        if (Objects.isNull(pickUpContext)) {
            return null;
        }
        PickupOperatorType pickupOperatorType = pickUpContext.getOperatorType();
        Integer accountId = pickUpContext.getUserId().intValue();
        OperatorType operatorType = OperatorType.ADVERTISERS;
        if (PICKUP_SECOND_AGENT_OPERATOR_TYPES.contains(pickupOperatorType)) {
            operatorType = OperatorType.AGENT;
            accountId = pickUpContext.getPayload().getAccountId();
        }
        if (PickupOperatorType.AGENT.equals(pickupOperatorType)) {
            operatorType = OperatorType.AGENT;
        }

        Context context = new Context(
                accountId,
                pickUpContext.getUsername(),
                salesType.getCode(),
                0,
                pickUpContext.getUsername(),
                operatorType.getCode(),
                pickUpContext.getMid());
        context.setBid(pickUpContext.getBid());
        return context;
    }

    private Context buildContextByBusinessToolToken(Map<String, Object> resultMap) {
        if (CollectionUtils.isEmpty(resultMap)) {
            return null;
        }
        return Context.builder().accountId(Integer.valueOf(resultMap.getOrDefault(JwtParam.ACCOUNT_ID.getCode(),
                "0").toString()))
                .username(resultMap.getOrDefault(JwtParam.ACCOUNT_NAME.getCode(), "").toString())
                .salesType(SalesType.CPC.getCode()).proxyId(0).proxyName("").bid(0L).type(OperatorType.BILIBILIER.getCode())
                .mid(Long.valueOf(resultMap.getOrDefault(JwtParam.MID.getCode(), "0").toString()))
                .build();
    }

    private String getTokenFromCookieByName(HttpServletRequest request, String tokenName) {
        Cookie[] cookies = request.getCookies();

        if(ArrayUtils.isEmpty(cookies)) {
            return "";
        }
        return Stream.of(cookies)
                .filter(c -> c.getName().equals(tokenName))
                .findFirst()
                .map(Cookie::getValue)
                .orElse("");
    }

    private String getTokenFromCookie(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();

        if (ArrayUtils.isEmpty(cookies)) {
            return "";
        }
        //优先取 _effectAd, 代表新版登录cookie
        String token = Stream.of(cookies)
                .filter(c -> c.getName().equals(EFFECT_AD_TOKEN))
                .findFirst()
                .map(Cookie::getValue)
                .orElse("");
        if (!StringUtils.isEmpty(token)){
            logger.info("getTokenFromCookie in {} ", EFFECT_AD_TOKEN);
            return token;
        }
        return Stream.of(cookies)
                .filter(c -> c.getName().equals(HTTP_TOKEN))
                .findFirst()
                .map(Cookie::getValue)
                .orElse("");
    }

    private String getCluePassTokenFromCookie(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();

        if (ArrayUtils.isEmpty(cookies)) {
            return "";
        }
        // 线索通cookie
        return Stream.of(cookies)
                .filter(c -> c.getName().equals(CLUE_PASS_TOKEN))
                .findFirst()
                .map(Cookie::getValue)
                .orElse("");
    }

}
