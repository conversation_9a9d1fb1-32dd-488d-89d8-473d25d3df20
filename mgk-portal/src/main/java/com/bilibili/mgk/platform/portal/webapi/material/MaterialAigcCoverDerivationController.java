package com.bilibili.mgk.platform.portal.webapi.material;

import com.bapis.ad.pandora.core.Operator;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.mgk.material.center.service.aigc.AigcDerivationImageDeliveryService;
import com.bilibili.mgk.material.center.service.aigc.dto.AIDerivationImageDeleteReq;
import com.bilibili.mgk.material.center.service.aigc.dto.AIDerivationImageDeleteResult;
import com.bilibili.mgk.material.center.service.aigc.dto.AIDerivationImageDeliverReq;
import com.bilibili.mgk.material.center.service.aigc.dto.AIDerivationImgDeliveryResult;
import com.bilibili.mgk.material.center.service.aigc.dto.CoverAIDerivationDeliveryRecordPageQuery;
import com.bilibili.mgk.material.center.service.aigc.dto.CreativeAiCoverDerivationImgPageQuery;
import com.bilibili.mgk.material.center.service.aigc.model.AIDerivationImageDeliveryRecord;
import com.bilibili.mgk.material.center.service.aigc.model.CreativeCoverAIDerivationImage;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialSortBy;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialSortOrder;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.List;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/18
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/material/aigc/cover/derivation")
@Api(value = "/material/aigc", tags = "material-center")
public class MaterialAigcCoverDerivationController extends BasicController {

    @Resource
    private AigcDerivationImageDeliveryService derivationCoverDeliveryService;
    @Resource
    private SystemType systemType;


    @ApiOperation("封面衍生-待选择的衍生图列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Response<Pagination<List<CreativeCoverAIDerivationImage>>> listCoverDerivation(

            @ApiIgnore Context context,

            @ApiParam("页号") @RequestParam(value = "pn", defaultValue = "1") Integer pn,
            @ApiParam("页大小") @RequestParam(value = "ps", defaultValue = "20") Integer ps,

            @ApiParam("排序方式 ctime") @RequestParam(value = "sort_by", defaultValue = "ctime") MaterialSortBy sortBy,
            @ApiParam("升序降序 asc desc") @RequestParam(value = "order", defaultValue = "desc") MaterialSortOrder order

    ) {

        return Response.SUCCESS(
                derivationCoverDeliveryService.pageCoverDerivationImgCandidateForDelivery(
                        new CreativeAiCoverDerivationImgPageQuery()
                                .setAccountId(Optional.ofNullable(context).map(Context::getAccountId)
                                        .map(Integer::longValue).orElse(null))
                                .setPn(pn)
                                .setPs(ps)
                ));
    }


    @ApiOperation("封面衍生-待选择的衍生图列表删除并加入黑名单")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public Response<AIDerivationImageDeleteResult> listCoverDerivation(

            @ApiIgnore Context context,

            @ApiParam("创意") @RequestParam(value = "creative_id") Long creativeId,
            @ApiParam("版本") @RequestParam(value = "version") Long version

    ) {

        return Response.SUCCESS(
                derivationCoverDeliveryService.deleteDerivationImage(
                        new AIDerivationImageDeleteReq()
                                .setAccountId(Optional.ofNullable(context).map(Context::getAccountId)
                                        .map(Integer::longValue).orElse(null))
                                .setVersion(version)
                                .setCreativeId(creativeId)
                ));
    }


    @ApiOperation("封面衍生-投放AI衍生图")
    @RequestMapping(value = "/deliver", method = RequestMethod.POST)
    public Response<AIDerivationImgDeliveryResult> deliverAIDerivationImage(
            @ApiIgnore Context context,
            @RequestBody AIDerivationImageDeliverReq deliverReq

    ) {

        return Response.SUCCESS(derivationCoverDeliveryService.deliverCoverAIDerivationImg(

                deliverReq.setOperator(getPandoraOperator(context))
                        .setAccountId(Optional.ofNullable(context)
                                .map(Context::getAccountId)
                                .map(Integer::longValue).orElse(null))
        ));

    }

    public Operator getPandoraOperator(Context context) {
        return Operator.newBuilder()
                .setOperatorId(context.getAccountId())
                .setOperatorName(context.getUsername())
                .setOperatorType(OperatorType.getByCode(context.getType()).getCode())
                .setSystemType(systemType.getCode())
                .setBilibiliUserName(context.getProxyId() > 0 ?
                        context.getProxyName() + "(" + context.getProxyId() + ")"
                        : context.getProxyName())
                .build();
    }


    @ApiOperation("封面衍生-衍生图自动投放记录列表")
    @RequestMapping(value = "/delivery/record/list", method = RequestMethod.GET)
    public Response<Pagination<List<AIDerivationImageDeliveryRecord>>> listDerivationImageDeliveryRecord(

            @ApiIgnore Context context,
            @ApiParam("页号") @RequestParam(value = "pn", defaultValue = "1") Integer pn,
            @ApiParam("页大小") @RequestParam(value = "ps", defaultValue = "20") Integer ps,

            @ApiParam("排序方式 ctime") @RequestParam(value = "sort_by", defaultValue = "ctime") MaterialSortBy sortBy,
            @ApiParam("升序降序 asc desc") @RequestParam(value = "order", defaultValue = "desc") MaterialSortOrder order

    ) {

        return Response.SUCCESS(
                derivationCoverDeliveryService.pageDeliveryRecord(
                        new CoverAIDerivationDeliveryRecordPageQuery()
                                .setAccountId(Optional.ofNullable(context).map(Context::getAccountId)
                                        .map(Integer::longValue).orElse(null))
                                .setPn(pn)
                                .setPs(ps)
                ));
    }


}
