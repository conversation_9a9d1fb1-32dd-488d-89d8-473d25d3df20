package com.bilibili.mgk.platform.portal.webapi.wechat.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * @ClassName WechatAccountListVo
 * <AUTHOR>
 * @Date 2022/6/12 11:34 下午
 * @Version 1.0
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WechatAccountListVo {

    @ApiModelProperty(value = "微信号id")
    private Integer id;

    @ApiModelProperty(value = "微信号")
    private String wechat_account;

    @ApiModelProperty(value = "微信号名称")
    private String wechat_name;

    @ApiModelProperty(value = "微信号类型 0-个人号 1-公众号 2-企业微信 3-其他")
    private Integer type;

    @ApiModelProperty(value = "微信号类型描述")
    private String type_desc;

    @ApiModelProperty(value = "备注")
    private String info;

    @ApiModelProperty(value = "调起数")
    private Integer jump_count;

    @ApiModelProperty(value = "复制数")
    private Integer copy_count;

    @ApiModelProperty(value = "数据提交时间")
    private String recent_submit_time;

    @ApiModelProperty(value = "创建时间")
    private String ctime;

    @ApiModelProperty(value = "更新时间")
    private String mtime;
}
