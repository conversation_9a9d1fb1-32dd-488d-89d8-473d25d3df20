package com.bilibili.mgk.platform.portal.exception;

import com.bilibili.adp.common.exception.IExceptionCode;

/**
 * <AUTHOR>
 * @date 2020/08/05
 **/
public enum WebApiExceptionCode implements IExceptionCode {

    BAD_REQUEST(400, "请求格式错误"),
    UNAUTHORIZED(401, "TOKEN非法"),
    TOKEN_EXPIRE(402, "TOKEN已过期"),
    NO_PERMISSION(403, "没有权限访问"),
    NOT_FOUND(404, "数据为空"),
    SYSTEM_ERROR(500, "系统异常"),

    BID_LOGIN_INVALID(1019, "商业账号登录失效，请重新登录"),
    BLACK_ACCOUNT(1020, "当前账号命中了账号黑名单,请联系运营"),
    ;
    private final String message;
    private final Integer code;

    WebApiExceptionCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }
}

