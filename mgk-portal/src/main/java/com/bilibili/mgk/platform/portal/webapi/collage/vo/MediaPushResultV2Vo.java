package com.bilibili.mgk.platform.portal.webapi.collage.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName MediaPushResultV2Vo
 * <AUTHOR>
 * @Date 2024/9/9 11:39 上午
 * @Version 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MediaPushResultV2Vo {

    private List<MediaPushFailInfoVo> failList;

}
