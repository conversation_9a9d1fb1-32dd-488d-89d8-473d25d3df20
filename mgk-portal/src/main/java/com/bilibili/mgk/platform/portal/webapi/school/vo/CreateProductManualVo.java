package com.bilibili.mgk.platform.portal.webapi.school.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CreateProductManualVo {

    @ApiModelProperty(value = "类目层级")
    private Integer level;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "父类目id")
    @JSONField(name = "parent_id")
    private Long parentId;

    @ApiModelProperty(value = "排序")
    @JSONField(name = "order_num")
    private Integer orderNum;
}
