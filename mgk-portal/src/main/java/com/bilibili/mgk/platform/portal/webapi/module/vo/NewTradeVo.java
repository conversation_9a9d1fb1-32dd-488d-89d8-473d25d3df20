package com.bilibili.mgk.platform.portal.webapi.module.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/07/09
 **/
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NewTradeVo {
    @ApiModelProperty(value = "父级行业ID")
    private String parent_trade_id;
    @ApiModelProperty(value = "行业级别")
    private Integer level;
    @ApiModelProperty(value = "行业名称")
    private String name;
}
