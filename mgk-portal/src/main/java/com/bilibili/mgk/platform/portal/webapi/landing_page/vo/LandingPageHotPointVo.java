package com.bilibili.mgk.platform.portal.webapi.landing_page.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @Description
 * @date 2024/11/18
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LandingPageHotPointVo {

    @ApiModelProperty(value = "账号")
    private Integer account_id;

    @ApiModelProperty(value = "落地页类型")
    private Integer page_type;

    @ApiModelProperty(value = "落地页id")
    private Long page_id;

    @ApiModelProperty(value = "page_x值")
    private Double page_x;

    @ApiModelProperty(value = "page_y值")
    private Double page_y;

    @ApiModelProperty(value = "点击次数")
    private Long click_cnt;

    @ApiModelProperty(value = "日期")
    private String log_date;
}
