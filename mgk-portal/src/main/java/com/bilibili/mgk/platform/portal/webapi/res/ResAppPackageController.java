package com.bilibili.mgk.platform.portal.webapi.res;

import com.bilibili.adp.common.Constants;
import com.bilibili.adp.common.enums.AppPackagePlatformStatus;
import com.bilibili.adp.common.enums.AppPackageStatus;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.mgk.platform.api.app_package.dto.QueryResAppPackageDto;
import com.bilibili.mgk.platform.api.app_package.dto.ResAppPackageDto;
import com.bilibili.mgk.platform.api.app_package.service.IResAppPackageService;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.service.WebAppPackageService;
import com.bilibili.mgk.platform.portal.webapi.res.vo.ResAppPackageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName ResAppPackageController
 * <AUTHOR>
 * @Date 2022/6/25 1:55 下午
 * @Version 1.0
 **/
@RestController
@RequestMapping("/web_api/v1/resource/app")
@Api(value = "/resource/app", description = "app包相关")
public class ResAppPackageController extends BasicController {

    @Autowired
    private IResAppPackageService resAppPackageService;

    @ApiOperation(value = "查询有效的App包下拉列表")
    @RequestMapping(value = "/drop_box", method = RequestMethod.GET)
    @ResponseBody
    public Response<List<ResAppPackageVo>> queryValidAppPackageList(@ApiIgnore Context context) {

        QueryResAppPackageDto query = QueryResAppPackageDto
                .builder()
                .accountIds(Collections.singletonList(context.getAccountId()))
                .orderBy(Constants.MTIME_DESC)
                .status(AppPackageStatus.VALID.getCode())
                .platformStatus(AppPackagePlatformStatus.VALID.getCode())
                .isNewFly(0)
                .build();
        List<ResAppPackageDto> result = resAppPackageService.queryList(query);

        return Response.SUCCESS(WebAppPackageService.dtosToVos(result));
    }

}
