package com.bilibili.mgk.platform.portal.webapi.collage.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2018/11/19
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryFontLibraryVo {

    @ApiModelProperty("字体库名称")
    private String name;

    private Integer page;

    private Integer size;

    private Integer status;

    @ApiModelProperty("版本隔离 0-旧版本 1-新版本")
    private Integer edition;
}
