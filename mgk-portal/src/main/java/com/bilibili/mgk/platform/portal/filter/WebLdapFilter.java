package com.bilibili.mgk.platform.portal.filter;

import com.bilibili.bjcom.sso.SSOFilter;
import com.bilibili.mgk.platform.common.MgkConstants;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.HttpMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;

/**
 * @ClassName WebLdapFilter
 * <AUTHOR>
 * @Date 2022/6/19 3:50 下午
 * @Version 1.0
 **/
@Slf4j
public class WebLdapFilter extends SSOFilter implements Filter {

    private String NOT_FILTER_URL;

    public static final String HEADER_KEY = "open-ad-account-id";

    @Value("${ldap.multi.domain.access.suffix:@}")
    private String multiDomainAccessSuffix;

    @Value("#{'${origin.filter.urls:@}'.split(',')}")
    private List<String> originFilterUrlList;


    @Resource
    private LoginFreePatternRegistry loginFreePatternRegistry;


    public static final String HTTP_ACCESS_TOKEN = "HTTP-ACCESS-TOKEN";
    private static final List<String> NOT_FILTER_SUFFIXS = new ArrayList<>();

    static {
        NOT_FILTER_SUFFIXS.add("/login/check");
        NOT_FILTER_SUFFIXS.add("/business/tool/check");
        NOT_FILTER_SUFFIXS.add("/business/tool/login");
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        super.init(filterConfig);
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        if (HttpMethod.OPTIONS.equals(httpRequest.getMethod())) {
            return;
        }
        //热门广告的过滤

        if (loginFreePatternRegistry.validateLoginFreeUrl(httpRequest)) {
            chain.doFilter(request, response);
            return;
        }

        //内网过滤
        if (null != NOT_FILTER_URL) {
            String[] urls = NOT_FILTER_URL.split(",");
            List<String> urlList = Arrays.asList(urls);

            if (urlList.contains(request.getServerName())
                    || request.getServerName().endsWith(multiDomainAccessSuffix)) {
                log.info("NOT_FILTER_URL{}, server name{}", NOT_FILTER_URL, request.getServerName());
                chain.doFilter(request, response);
                return;
            }
        }
        //登陆校验 /login/check
        for (String suffix : NOT_FILTER_SUFFIXS) {
            if (StringUtils.isNotBlank(suffix) && ((HttpServletRequest) request).getRequestURI().endsWith(suffix)) {
                chain.doFilter(request, response);
                return;
            }
        }

        String origin = httpRequest.getHeader("Origin");
        String token = httpRequest.getHeader(HTTP_ACCESS_TOKEN);
        log.info("origin is {},token is {}", origin, token);
        if (StringUtils.isNotBlank(origin)) {
            for (String origin_url : originFilterUrlList) {
                if (origin.contains(origin_url) && StringUtils.isNotBlank(token)) {
                    chain.doFilter(request, response);
                    return;
                }
            }
        }

        //过滤开放平台请求
        String accountId = ((HttpServletRequest) request).getHeader(HEADER_KEY);
        if (!org.springframework.util.StringUtils.isEmpty(accountId)) {
            chain.doFilter(request, response);
            return;
        }

        //过滤号经营平台请求
        Cookie[] cookies = ((HttpServletRequest) request).getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (MgkConstants.BUSINESS_TOOL_TOKEN.equals(cookie.getName())) {
                    chain.doFilter(request, response);
                    return;
                }
            }
        }

        super.doFilter(request, response, chain);
    }



    @Override
    public void destroy() {
        super.destroy();
    }

    public String getNOT_FILTER_URL() {
        return NOT_FILTER_URL;
    }

    public void setNOT_FILTER_URL(String NOT_FILTER_URL) {
        this.NOT_FILTER_URL = NOT_FILTER_URL;
    }


}
