package com.bilibili.mgk.platform.portal.webapi.commercialorder;

import com.bapis.ad.component.*;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.collage.api.dto.CollageEnterpriseVideoDto;
import com.bilibili.collage.api.dto.CollageEnterpriseVideoQueryDto;
import com.bilibili.collage.api.service.ICommercialOrderService;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.mgk.platform.biz.bean.NativeArchiveBo;
import com.bilibili.mgk.platform.biz.service.natives.NativeService;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Pagination;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.service.WebCollageService;
import com.bilibili.mgk.platform.portal.webapi.enterprise.vo.CollageArchiveVideoVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;

import java.util.*;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/web_api/v1/commercialorder")
@Api(value = "/commercialorder", description = "商单视频相关")
@Slf4j
public class CommercialOrderVideoController extends BasicController {

    @Autowired
    private ICommercialOrderService commercialOrderService;

    @Autowired
    private WebCollageService webCollageService;

    @Autowired
    private NativeService nativeService;

    @Autowired
    private CommentComponentServiceGrpc.CommentComponentServiceBlockingStub commentComponentServiceBlockingStub;

    @ApiOperation(value = "商单稿件列表")
    @RequestMapping(value = "/video/list", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Pagination<List<CollageArchiveVideoVo>>> getLandingPageList(@ApiIgnore Context context,
                                                                         @ApiParam("投放模式") @RequestParam(value = "advertising_mode", defaultValue = "0") Integer advertisingMode,
                                                                         @ApiParam("是否仅仅带货稿件 -1所有 1带货") @RequestParam(value = "only_goods", required = false, defaultValue = "-1") int onlyGoods,
                                                                         @ApiParam("排序") @RequestParam(value = "orderBy", required = false) String orderBy,
                                                                         @ApiParam("页码") @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                                         @ApiParam("每页大小") @RequestParam(value = "size", defaultValue = "15") Integer pageSize,
                                                                         @ApiParam("avid, 不适用avid过滤时，可不传") @RequestParam(value = "avid", required = false) String avid

    ) {
        CollageEnterpriseVideoQueryDto queryDto = CollageEnterpriseVideoQueryDto.builder()
                .accountId(context.getAccountId())
                .advertisingMode(advertisingMode)
                .page(page)
                .pageSize(pageSize)
                .onlyGoods(onlyGoods)
                .orderBy(orderBy)
                .avid(avid)
                .build();
        // 一次查1000个，可以认为是全量查
        PageResult<CollageEnterpriseVideoDto> pageResult = commercialOrderService.getCommercialOrderVideos(queryDto);
        if (pageResult.getTotal() == 0 || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return Response.SUCCESS(new Pagination<>(page, pageResult.getTotal(), Collections.emptyList()));
        }

        List<Long> avids = pageResult.getRecords().stream().map(CollageEnterpriseVideoDto::getAid)
                .collect(Collectors.toList());

        //添加原生稿件状态
        List<NativeArchiveBo> nativeArchiveBos = nativeService.queryNativeArchive(avids);

        // 之前这个方法按是否原生稿件，进行了一次排序。这次去掉这个排序逻辑
        NativeService.fillGeneralNativeStatus(pageResult.getRecords(), nativeArchiveBos);

        List<CollageArchiveVideoVo> resultArchiveVideoList = webCollageService.convertVideoDtos2Vos(pageResult.getRecords());

        processHasCommentComponent(resultArchiveVideoList);
        return Response.SUCCESS(new Pagination<>(page, pageResult.getTotal(), resultArchiveVideoList));
    }

    public void processHasCommentComponent(List<CollageArchiveVideoVo> collageArchiveVideoVos) {

        List<Long> avids = collageArchiveVideoVos.stream().map(CollageArchiveVideoVo::getAid).map(Long::parseLong).collect(Collectors.toList());

        ComponentsReq componentsReq = ComponentsReq.newBuilder()
                .addAllAid(avids)
                .setPn(1)
                .setPs(20)
                .build();

        ComponentsReply components = commentComponentServiceBlockingStub.components(componentsReq);
        Set<Long> hasCommentComponentArchive = components.getComponentsList().stream()
                .filter(commentComponent -> !Objects.equals(commentComponent.getStatus(), CommentComponentStatus.DELETED))
                .map(CommentComponent::getAid)
                .collect(Collectors.toSet());
        for (CollageArchiveVideoVo collageArchiveVideoVo : collageArchiveVideoVos) {
            String avidStr = collageArchiveVideoVo.getAid();
            long avid = Long.parseLong(avidStr);
            if (hasCommentComponentArchive.contains(avid)) {
                collageArchiveVideoVo.setHasBindComment(IsValid.TRUE.getCode());
            } else {
                collageArchiveVideoVo.setHasBindComment(IsValid.FALSE.getCode());
            }
        }
    }

}
