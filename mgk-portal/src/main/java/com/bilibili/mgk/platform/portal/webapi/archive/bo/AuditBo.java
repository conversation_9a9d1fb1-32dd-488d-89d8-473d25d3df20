/*
 * Copyright (c) 2015-2021 BiliBili Inc.
 */

package com.bilibili.mgk.platform.portal.webapi.archive.bo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AuditBo {
    private Integer key;
    private String value;
    private String reason;
    private Long pubTime;
}
