package com.bilibili.mgk.platform.portal.webapi.mock;

import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/web_api/v1/config_paladin")
@Slf4j
public class ConfigPaladinController extends BasicController {

    @Value("${config.paladin.test:test123}")
    private String configPaladin;

    @GetMapping("/debug/refreshDebug")
    @ResponseBody
    public Response<String> refreshDebug(@ApiIgnore Context context) {
        return Response.SUCCESS(configPaladin);
    }


}
