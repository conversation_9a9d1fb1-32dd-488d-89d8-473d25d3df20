package com.bilibili.mgk.platform.portal.service;

import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.launch.api.creative.dto.SimpleCreativeDto;
import com.bilibili.adp.launch.api.soa.ISoaCreativeService;
import com.bilibili.mgk.platform.api.data.dto.EncryptedPhoneDto;
import com.bilibili.mgk.platform.api.data.dto.FormItemDataDto;
import com.bilibili.mgk.platform.api.data.dto.NewReportDataDto;
import com.bilibili.mgk.platform.api.data.dto.ReportDataDto;
import com.bilibili.mgk.platform.api.form.dto.*;
import com.bilibili.mgk.platform.api.landing_page.dto.MgkLandingPageDto;
import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageService;
import com.bilibili.mgk.platform.api.landing_page.service.INewMgkLandingPageService;
import com.bilibili.mgk.platform.api.personal_mgk.IPerMgkAutoCreateAccService;
import com.bilibili.mgk.platform.biz.utils.DateUtils;
import com.bilibili.mgk.platform.common.*;
import com.bilibili.mgk.platform.portal.annotation.ExcelResources;
import com.bilibili.mgk.platform.portal.openapi.form.vo.EncryptedPhoneVo;
import com.bilibili.mgk.platform.portal.openapi.form.vo.FormItemDataVo;
import com.bilibili.mgk.platform.portal.openapi.form.vo.NewReportDataVo;
import com.bilibili.mgk.platform.portal.openapi.form.vo.ReportDataVo;
import com.bilibili.mgk.platform.portal.webapi.form.vo.*;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.mysema.commons.lang.Pair;
import com.vdurmont.emoji.EmojiParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/1/22
 **/
@Service
@Slf4j
public class WebFormService {

    @Autowired
    private ISoaCreativeService soaCreativeService;
    @Autowired
    private IMgkLandingPageService mgkLandingPageService;
    @Autowired
    private INewMgkLandingPageService newMgkLandingPageService;
    @Autowired
    private IPerMgkAutoCreateAccService mgkAutoCreateAccService;

    public List<FormDropboxVo> convertDropboxs(List<MgkFormDto> formDtos) {
        if (CollectionUtils.isEmpty(formDtos)) {
            return Collections.emptyList();
        }
        return formDtos.stream().sorted((f1, f2) -> f2.getCtime().compareTo(f1.getCtime()))
                .map(dto -> FormDropboxVo.builder()
                        .id(String.valueOf(dto.getFormId()))
                        .name(dto.getName())
                        .form_type(dto.getFormType())
                        .build()).collect(Collectors.toList());
    }


    public MgkFormVo convertFormDto2Vo(MgkFormDto formDto) {
        return MgkFormVo.builder()
                .id(String.valueOf(formDto.getFormId()))
                .name(formDto.getName())
                .record_count(formDto.getFormDataCount())
                .ctime(Utils.getTimestamp2String(formDto.getCtime(), "yyyy-MM-dd HH:mm:ss"))
                .recent_data_submit_time(formDto.getRecentDataSubmitTime() == null ? "--" : Utils.getTimestamp2String(formDto.getRecentDataSubmitTime(), "yyyy-MM-dd HH:mm:ss"))
                .status(formDto.getStatus())
                .status_desc(FormStatusEnum.getByCode(formDto.getStatus()).getDesc())
                .items(this.convertFormItemDtos2Vos(formDto.getItems()))
                .customize(formDto.getCustomize())
                .form_type(formDto.getFormType())
                .form_data(convertSubmitInfoDtos2Vos(formDto.getFormData()))
                .page_title(formDto.getPageTitle())
                .build();
    }

    private List<MgkFormItemVo> convertFormItemDtos2Vos(List<MgkFormItemDto> itemDtos) {
        if (CollectionUtils.isEmpty(itemDtos)) {
            return Collections.emptyList();
        }

        return itemDtos.stream()
                .map(this::convertFormItemDto2Vo)
                .collect(Collectors.toList());
    }

    public MgkFormItemVo convertFormItemDto2Vo(MgkFormItemDto itemDto) {
        return MgkFormItemVo.builder()
                .id(String.valueOf(itemDto.getFormItemId()))
                .label(itemDto.getLabel())
                .type(FormItemTypeEnum.getById(itemDto.getType()).getCode())
                .options_val(itemDto.getOptionsVal())
                .radio_checkbox_val(itemDto.getRadioCheckboxVal())
                .min_address(itemDto.getMinAddress())
                .is_detail_address(itemDto.getIsDetailAddress())
                .is_allow_empty(itemDto.getIsAllowEmpty())
                .is_unique(itemDto.getIsUnique())
                .is_submit_validate(itemDto.getIsSubmitValidate())
                .required(itemDto.getRequired())
                .placeholder(itemDto.getPlaceholder())
                .item_default(itemDto.getItemDefault())
                .max_length(itemDto.getMaxLength())
                .prefix(itemDto.getPrefix())
                .auto_fill_link(itemDto.getAutoFillLink())
                .auto_fill_text(itemDto.getAutoFillText())
                .show_auto_fill(itemDto.getShowAutoFill())
                .sub_type(itemDto.getSubType())
                .show_rule(itemDto.getShowRule())
                .range_left(itemDto.getRangeLeft())
                .range_right(itemDto.getRangeRight())
                .options(itemDto.getOptions())
                .build();
    }

    public NewMgkFormDto newMgkFormVo2Dto(Integer accountId, NewMgkFormVo mgkFormVo) {
        return NewMgkFormDto.builder()
                .accountId(accountId)
                .name(mgkFormVo.getName())
                .formType(mgkFormVo.getForm_type() == null ? MgkFormTypeEnum.COMMON_FORM.getCode() : mgkFormVo.getForm_type())
                .items(this.convertNewMgkFormItemVos2Dtos(mgkFormVo.getItems()))
                .pageTitle(mgkFormVo.getPage_title())
                .build();
    }

    private List<NewMgkFormItemDto> convertNewMgkFormItemVos2Dtos(List<NewMgkFormItemVo> itemVos) {
        if (CollectionUtils.isEmpty(itemVos)) {
            return Collections.emptyList();
        }

        return itemVos.stream().map(this::convertNewMgkFormItemVo2Dto).collect(Collectors.toList());
    }

    private NewMgkFormItemDto convertNewMgkFormItemVo2Dto(NewMgkFormItemVo itemVo) {
        return NewMgkFormItemDto.builder()
                .label(itemVo.getLabel())
                .type(FormItemTypeEnum.getByCode(itemVo.getType()).getId())
                .sortNumber(0)
                .optionsVal(Strings.isNullOrEmpty(itemVo.getOptions_val()) ? "[]" : itemVo.getOptions_val())
                .radioCheckboxVal(Strings.isNullOrEmpty(itemVo.getRadio_checkbox_val()) ? "[]" : itemVo.getRadio_checkbox_val())
                .minAddress(itemVo.getMin_address())
                .isDetailAddress(itemVo.getIs_detail_address())
                .isAllowEmpty(itemVo.getIs_allow_empty() == null ? WhetherEnum.YES.getCode() : itemVo.getIs_allow_empty())
                .isUnique(itemVo.getIs_unique() == null ? WhetherEnum.NO.getCode() : itemVo.getIs_unique())
                .isSubmitValidate(itemVo.getIs_submit_validate() == null ? WhetherEnum.NO.getCode() : itemVo.getIs_submit_validate())
                .required(itemVo.getRequired() == null ? WhetherEnum.NO.getCode() : itemVo.getRequired())
                .placeholder(itemVo.getPlaceholder() == null ? FromItemPlaceholderDefaultEnum.getByFormItemTypeEnum(FormItemTypeEnum.getByCode(itemVo.getType())).getPlaceholder() : itemVo.getPlaceholder())
                .itemDefault(itemVo.getItem_default() == null ? FromItemPlaceholderDefaultEnum.getByFormItemTypeEnum(FormItemTypeEnum.getByCode(itemVo.getType())).getItemDefault() : itemVo.getItem_default())
                .maxLength(itemVo.getMax_length() == null ? 0 : itemVo.getMax_length())
                .prefix(itemVo.getPrefix() == null ? "+86" : itemVo.getPrefix())
                .autoFillLink(itemVo.getAuto_fill_link())
                .autoFillText(itemVo.getAuto_fill_text())
                .showAutoFill(itemVo.getShow_auto_fill() == null ? 0 : itemVo.getShow_auto_fill())
                .keyboardType(itemVo.getKeyboard_type())
                .subType(itemVo.getSub_type())
                .showRule(itemVo.getShow_rule() == null ? 0 : itemVo.getShow_rule())
                .rangeLeft(itemVo.getRange_left() == null ? 0 : itemVo.getRange_left())
                .rangeRight(Optional.ofNullable(itemVo.getRange_right()).orElse(0))
                .options(Optional.ofNullable(itemVo.getOptions()).orElse(""))
                .lbsId(itemVo.getLbs_id() == null? null : Long.parseLong(itemVo.getLbs_id()))
                .build();
    }

    public UpdateMgkFormDto convertUpdateFormVo2Dto(UpdateMgkFormVo mgkFormVo) {
        return UpdateMgkFormDto.builder()
                .formId(Long.valueOf(mgkFormVo.getId()))
                .name(mgkFormVo.getName())
                .items(this.convertUpdateMgkFormItemVos2Dtos(mgkFormVo.getItems()))
                .formType(mgkFormVo.getForm_type() == null ? 0 : mgkFormVo.getForm_type())
                .pageTitle(mgkFormVo.getPage_title())
                .build();
    }

    private List<UpdateMgkFormItemDto> convertUpdateMgkFormItemVos2Dtos(List<MgkFormItemVo> itemVos) {
        if (CollectionUtils.isEmpty(itemVos)) {
            return Collections.emptyList();
        }

        return itemVos.stream().map(this::convertUpdateMgkFormItemVo2Dto).collect(Collectors.toList());
    }

    private UpdateMgkFormItemDto convertUpdateMgkFormItemVo2Dto(MgkFormItemVo itemVo) {
        return UpdateMgkFormItemDto.builder()
                .formItemId(Strings.isNullOrEmpty(itemVo.getId()) ? 0L : Long.parseLong(itemVo.getId()))
                .label(itemVo.getLabel() == null ? "" : itemVo.getLabel())
                .type(FormItemTypeEnum.getByCode(itemVo.getType()).getId())
                .sortNumber(0)
                .optionsVal(Strings.isNullOrEmpty(itemVo.getOptions_val()) ? "[]" : itemVo.getOptions_val())
                .radioCheckboxVal(Strings.isNullOrEmpty(itemVo.getRadio_checkbox_val()) ? "[]" : itemVo.getRadio_checkbox_val())
                .minAddress(itemVo.getMin_address() == null ? 0 : itemVo.getMin_address())
                .isDetailAddress(itemVo.getIs_detail_address() == null ? 0 : itemVo.getIs_detail_address())
                .isAllowEmpty(itemVo.getIs_allow_empty() == null ? WhetherEnum.YES.getCode() : itemVo.getIs_allow_empty())
                .isUnique(itemVo.getIs_unique() == null ? WhetherEnum.NO.getCode() : itemVo.getIs_unique())
                .isSubmitValidate(itemVo.getIs_submit_validate() == null ? WhetherEnum.NO.getCode() : itemVo.getIs_submit_validate())
                .required(itemVo.getRequired() == null ? WhetherEnum.NO.getCode() : itemVo.getRequired())
                .placeholder(itemVo.getPlaceholder() == null ? FromItemPlaceholderDefaultEnum.getByFormItemTypeEnum(FormItemTypeEnum.getByCode(itemVo.getType())).getPlaceholder() : itemVo.getPlaceholder())
                .itemDefault(itemVo.getItem_default() == null ? FromItemPlaceholderDefaultEnum.getByFormItemTypeEnum(FormItemTypeEnum.getByCode(itemVo.getType())).getItemDefault() : itemVo.getItem_default())
                .maxLength(itemVo.getMax_length() == null ? 0 : itemVo.getMax_length())
                .prefix(itemVo.getPrefix() == null ? "+86" : itemVo.getPrefix())
                .autoFillLink(itemVo.getAuto_fill_link() == null ? "" : itemVo.getAuto_fill_link())
                .autoFillText(itemVo.getAuto_fill_text() == null ? "" : itemVo.getAuto_fill_text())
                .showAutoFill(itemVo.getShow_auto_fill() == null ? 0 : itemVo.getShow_auto_fill())
                .keyboardType(itemVo.getKeyboard_type() == null ? 0 : itemVo.getKeyboard_type())
                .subType(itemVo.getSub_type() == null ? 0 : itemVo.getSub_type())
                .showRule(itemVo.getShow_rule() == null ? 0 : itemVo.getShow_rule())
                .rangeLeft(itemVo.getRange_left() == null ? 0 : itemVo.getRange_left())
                .rangeRight(Optional.ofNullable(itemVo.getRange_right()).orElse(0))
                .options(Optional.ofNullable(itemVo.getOptions()).orElse(""))
                .lbsId(Strings.isNullOrEmpty(itemVo.getLbs_id()) ? 0L : Long.parseLong(itemVo.getLbs_id()))
                .build();
    }

    public List<MgkFormVo> convertFormDtos2Vos(List<MgkFormDto> records) {
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }

        return records.stream().map(this::convertFormDto2Vo).collect(Collectors.toList());
    }

    public ReportDataDto collectingDataFromH5(Long landingPageId, ReportDataVo vo) {
        Assert.notNull(landingPageId, "页面ID不可为空");
        Assert.notNull(vo, "上报数据不可为空");
        Assert.notNull(vo.getForm_id(), "表单ID不可为空");

        return ReportDataDto.builder()
                .pageId(landingPageId)
                .formId(Long.valueOf(vo.getForm_id()))
                .formData(this.converFormDataVos2Dtos(vo.getForm_datas()))
                .customize(vo.getCustomize())
                .build();
    }

    public ReportDataDto initPersonalMgkData(Long landingPageId, ReportDataVo vo) {
        Assert.notNull(landingPageId, "页面ID不可为空");
        Assert.notNull(vo, "上报数据不可为空");
        Assert.notNull(vo.getForm_id(), "表单ID不可为空");

        return ReportDataDto.builder()
                .pageId(landingPageId)
                .formId(Long.valueOf(vo.getForm_id()))
                .formData(this.converFormDataVos2Dtos(vo.getForm_datas()))
                .customize(vo.getCustomize())
                .creativeId(0L).avid(0L)
                .buvid("").trackId("").sourceId(0).os(3)
                .allowLbs(0).allowHistory(0).ctime(Utils.getNow())
                .deviceId("").adType("").imei("").eventSourceType(EventSourceType.OUTER.getCode())
                .build();
    }


    private List<FormItemDataDto> converFormDataVos2Dtos(List<FormItemDataVo> vos) {
        if (CollectionUtils.isEmpty(vos)) {
            return Collections.emptyList();
        }

        return vos.stream().map(vo -> FormItemDataDto.builder()
                .id(Long.valueOf(vo.getId()))
                .label(Strings.isNullOrEmpty(vo.getLabel()) ? "" : vo.getLabel().trim())
                .code(Strings.isNullOrEmpty(vo.getCode()) ? "" : vo.getCode().trim())
                .value(Strings.isNullOrEmpty(vo.getValue()) ? "" : EmojiParser.parseToAliases(vo.getValue().trim()))
                .extraValue(Strings.isNullOrEmpty(vo.getExtra_value()) ? "" : vo.getExtra_value().trim())
                .phoneChannel(vo.getPhone_channel())
                .encryptedPhoneDto(convertEncryptedVo2Dto(vo.getEncrypted_phone_vo()))
                .build()).collect(Collectors.toList());
    }

    private EncryptedPhoneDto convertEncryptedVo2Dto(EncryptedPhoneVo phoneVo) {
        if (phoneVo == null) {
            return null;
        }
        EncryptedPhoneDto encryptedPhoneDto = new EncryptedPhoneDto();
        BeanUtils.copyProperties(phoneVo, encryptedPhoneDto);
        return encryptedPhoneDto;
    }

    public List<FormDataVo> convertFormDataDtos2Vos(List<ReportDataDto> records) {
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }

        Map<Integer, Set<Long>> salesType2CreativeIdSet = records.stream()
                .collect(Collectors.groupingBy(ReportDataDto::getSalesType,
                        Collectors.mapping(ReportDataDto::getCreativeId, Collectors.toSet())));
        List<Integer> effectAdCreativeIds = salesType2CreativeIdSet.keySet().stream().filter(st -> SalesType.CPT.getCode() != st)
                .map(salesType2CreativeIdSet::get).flatMap(Collection::stream)
                .filter(cid -> Long.valueOf(Integer.MAX_VALUE).compareTo(cid) >= 0)
                .map(Long::intValue)
                .distinct()
                .collect(Collectors.toList());

        Map<Integer, SimpleCreativeDto> creativeId2CreativeMap = this.getCreativeId2CreativeMap(effectAdCreativeIds);

        List<Long> mgkPageIds = records.stream().map(ReportDataDto::getPageId).distinct().collect(Collectors.toList());
        Map<Long, MgkLandingPageDto> landingPageId2DtoMap = mgkLandingPageService.getAllLandingPageId2DtoMapForInner(mgkPageIds);

        //营销助手自动开户逻辑
        AtomicReference<Boolean> containAutoCreateAccPage = new AtomicReference<>(false);
        mgkPageIds.forEach(id->{
            if(newMgkLandingPageService.isPersonalMgkAutoCreateAccPage(id)){
                containAutoCreateAccPage.set(true);
            }
        });
        Map<Long, Long> dataId2Uid = new HashMap<>();
        if(containAutoCreateAccPage.get()){
            List<Long> dataIds = records.stream().map(ReportDataDto::getReportDatId).distinct().collect(Collectors.toList());
            dataId2Uid = mgkAutoCreateAccService.getDataId2Uid(dataIds);
        }

        SimpleCreativeDto defaultCreativeDto = SimpleCreativeDto.builder()
                .creativeId(0)
                .title("--")
                .unitId(0)
                .unitName("--")
                .campaignId(0)
                .campaignName("--")
                .salesType(0)
                .build();
        Map<Long, Long> finalDataId2Uid = dataId2Uid;
        return records.stream().map(dto -> {
            SimpleCreativeDto simpleCreativeDto;
            if(Utils.isPositive(dto.getCreativeId())){
                simpleCreativeDto =  creativeId2CreativeMap.getOrDefault(dto.getCreativeId().intValue(), defaultCreativeDto);
            }else {
                simpleCreativeDto = defaultCreativeDto;
            }
            MgkLandingPageDto pageDto = landingPageId2DtoMap.getOrDefault(dto.getPageId(),
                    MgkLandingPageDto.builder().name("--").build());
            //评论区暗投落地页产生的转换数据在前台不展现暗投落地页信息
            if(!IsModelEnum.PAGE.getCode().equals(pageDto.getIsModel())){
                dto.setPageId(0L);
                pageDto.setName("--");
            }
            //经营助手表单mid
            String uid = "0";
            if (Utils.isPositive(dto.getPageId()) && newMgkLandingPageService.isPersonalMgkAutoCreateAccPage(dto.getPageId())) {
                uid = null == finalDataId2Uid.get(dto.getReportDatId()) ? dto.getMid().toString() : finalDataId2Uid.get(dto.getReportDatId()).toString();
            }
            return FormDataVo.builder()
                    .creative_id(Utils.isPositive(dto.getCreativeId()) ? String.valueOf(dto.getCreativeId()) : "--")
                    .creative_title(simpleCreativeDto.getTitle())
                    .unit_id(Utils.isPositive(simpleCreativeDto.getUnitId()) ? String.valueOf(simpleCreativeDto.getUnitId()) : "--")
                    .unit_name(simpleCreativeDto.getUnitName())
                    .campaign_id(Utils.isPositive(simpleCreativeDto.getCampaignId()) ? String.valueOf(simpleCreativeDto.getCampaignId()) : "--")
                    .campaign_name(simpleCreativeDto.getCampaignName())
//                    .sales_type_desc(getSalesTypeDesc(dto.getSalesType()))
                    .page_id(Utils.isPositive(dto.getPageId()) ? String.valueOf(dto.getPageId()) : "--")
                    .page_name(pageDto.getName())
                    .form_id(String.valueOf(dto.getFormId()))
                    .item_datas(this.formItemDataDtos2Vos(dto.getFormData()))
                    .submit_date(Utils.getTimestamp2String(dto.getCtime(), "yyyy-MM-dd HH:mm:ss"))
                    .avid(Utils.isPositive(dto.getAvid()) ? String.valueOf(dto.getAvid()) : "--")
                    .form_name(dto.getFormName())
                    .form_type_desc(dto.getFormTypeDesc())
                    .ctime(Utils.getTimestamp2String(dto.getFormCreateTime(), "yyyy-MM-dd HH:mm:ss"))
                    .uid(uid)
                    .build();
        }).collect(Collectors.toList());
    }

    private String getSalesTypeDesc(Integer salesType) {
        if (Utils.isPositive(salesType)) {
            try {
                return SalesType.getByCode(salesType).getDesc();
            } catch (Exception e) {
                return "--";
            }
        }
        return "--";
    }

    private Map<Integer, SimpleCreativeDto> getCreativeId2CreativeMap(List<Integer> effectAdCreativeIds) {
        try {
            List<SimpleCreativeDto> simpleCreativeDtos = soaCreativeService.getSimpleCreativeDtosInCreativeIds(effectAdCreativeIds);
            return simpleCreativeDtos.stream().collect(Collectors.toMap(SimpleCreativeDto::getCreativeId, Function.identity()));
        } catch (Exception e) {
            log.error("getCreativeId2CreativeMap failed", e);
            return Collections.emptyMap();
        }
    }

    private List<MgkFormItemDataVo> formItemDataDtos2Vos(List<FormItemDataDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyList();
        }

        return dtos.stream().filter(Objects::nonNull).map(dto -> MgkFormItemDataVo.builder()
                .id(String.valueOf(dto.getId()))
                .code(dto.getCode())
                .label(dto.getLabel())
                .value(dto.getValue())
                .build()).collect(Collectors.toList());
    }

    /**
     * 产生6位随机数(000000-999999)
     *
     * @return 6位随机数
     */
    public String getSixRandom() {
        Random random = new Random();
        StringBuilder fourRandom = new StringBuilder(random.nextInt(1000000) + "");
        int randLength = fourRandom.length();
        if (randLength < 6) {
            for (int i = 1; i <= 6 - randLength; i++) {
                fourRandom.insert(0, "0");
            }
        }
        return fourRandom.toString();
    }

    public NewReportDataDto convertNewReportDataVo2NewReportDataDto(Long landingPageId, NewReportDataVo newReportDataVo) {
        Assert.notNull(landingPageId, "页面ID不可为空");
        Assert.notNull(newReportDataVo, "上报数据不可为空");
        List<ReportDataDto> newReportDataDto = newReportDataVo.getReportDataVos().stream()
                .map(reportDataVo -> collectingDataFromH5(landingPageId, reportDataVo)).collect(Collectors.toList());
        return NewReportDataDto.builder()
                .reportDataDtoList(newReportDataDto)
                .build();
    }

    private MgkFormSubmitInfoVo convertPageSubmitInfoDto2Vo(MgkPageFormSubmitInfoDto infoDto) {
        return MgkFormSubmitInfoVo.builder()
                .name(convertName(infoDto.getName()))
                .phone(convertPhone(infoDto.getPhone()))
                .time_desc(infoDto.getTime_desc())
                .build();
    }

    /**
     * 随机表单数据
     * 小于10随机补到10
     * 随机规则，时间范围：当前时间->当前时间-24h
     * 随机数据和真实数据一起排序
     *
     * @param submitInfoDtos
     * @return
     */
    public List<MgkFormSubmitInfoVo> convertSubmitInfoDtos2Vos(List<MgkFormSubmitInfoDto> submitInfoDtos) {

        if (CollectionUtils.isEmpty(submitInfoDtos)) {
            return Collections.emptyList();
        }

        return submitInfoDtos.stream().map(this::convertSubmitInfoDto2Vo).collect(Collectors.toList());
    }


    private MgkFormSubmitInfoVo convertSubmitInfoDto2Vo(MgkFormSubmitInfoDto infoDto) {
        return MgkFormSubmitInfoVo.builder()
                .name(convertName(infoDto.getName()))
                .phone(convertPhone(infoDto.getPhone()))
                .time_desc(DateUtils.scrollTimeShow(infoDto.getTime()))
                .build();
    }

    public String convertName(String name) {
        if (Strings.isNullOrEmpty(name)) {
            return "***";
        }
        return name.charAt(0) + "**";
    }

    public String convertPhone(String phone) {
        if (Strings.isNullOrEmpty(phone) || phone.length() != 11) {
            return "";
        }
        String prefix = phone.substring(0, 3);
        String suffix = phone.substring(7, 11);

        return prefix + "****" + suffix;
    }

    public Map<String, Pair<List<String>, List<List<String>>>> convergeFormData(Map<Long, MgkFormDto> formDtosMap,
                   Map<String, List<FormDataVo>> dataMap){
        Map<String, Pair<List<String>, List<List<String>>>> sheet2HeaderWithData = new HashMap<>();
        AtomicReference<Integer> inc = new AtomicReference<>(1);
        dataMap.forEach((formIdStr,data)->{
            MgkFormDto mgkFormDto = formDtosMap.get(Long.parseLong(formIdStr));
            List<List<String>> singleData = this.getExcelDatas(data, CollectionUtils.isEmpty(mgkFormDto.getItems()) ? new ArrayList<>()
                    : mgkFormDto.getItems().stream().map(MgkFormItemDto::getFormItemId).collect(Collectors.toList()));

            List<String> itemHeaders = mgkFormDto.getItems().stream()
                    .filter(item -> !FormItemTypeEnum.BUTTON.getId().equals(item.getType()))
                    .map(MgkFormItemDto::getLabel)
                    .collect(Collectors.toList());
            List<String> headers = this.buildExcelHeaders(FormDataVo.class, itemHeaders);

            sheet2HeaderWithData.put(mgkFormDto.getName()+ "_" + inc,Pair.of(headers, singleData));
            inc.getAndSet(inc.get() + 1);
        });
        return sheet2HeaderWithData;
    }

    private List<List<String>> getExcelDatas(List<FormDataVo> reportDataVos, List<Long> sortedFormItemIds) {
        if (CollectionUtils.isEmpty(reportDataVos)) {
            return Collections.emptyList();
        }
        Field[] fields = FormDataVo.class.getDeclaredFields();
        List<List<String>> datas = Lists.newArrayList();
        for (FormDataVo formDataVo : reportDataVos) {
            List<MgkFormItemDataVo> itemDatas = formDataVo.getItem_datas();
            if (CollectionUtils.isEmpty(itemDatas)) {
                continue;
            }
            List<String> rowData = Lists.newArrayList();
            for (Field field : fields) {
                if (field.isAnnotationPresent(ExcelResources.class)) {
                    try {
                        PropertyDescriptor pd = new PropertyDescriptor(field.getName(), FormDataVo.class);
                        Method getMethod = pd.getReadMethod();
                        Object result = getMethod.invoke(formDataVo, null);
                        rowData.add(String.valueOf(result));
                    } catch (IntrospectionException | IllegalAccessException | InvocationTargetException e) {
                        log.error("getExcelDatas failed", e);
                        throw new IllegalStateException(e);
                    }
                }
            }

            Map<String, String> formId2ValueMap = itemDatas.stream().collect(Collectors.toMap(MgkFormItemDataVo::getId, MgkFormItemDataVo::getValue));
            sortedFormItemIds.forEach(fid -> rowData.add(formId2ValueMap.getOrDefault(String.valueOf(fid), "")));
            datas.add(rowData);
        }
        return datas;
    }

    private List<String> buildExcelHeaders(Class clazz, List<String> itemLabels) {
        List<String> headers = Lists.newArrayList();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(ExcelResources.class)) {
                ExcelResources excelResources = field.getAnnotation(ExcelResources.class);
                headers.add(excelResources.title());
            }
        }

        if (!CollectionUtils.isEmpty(itemLabels)) {
            headers.addAll(itemLabels);
        }
        return headers;
    }
}