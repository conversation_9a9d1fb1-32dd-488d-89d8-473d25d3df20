package com.bilibili.mgk.platform.portal.webapi.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/07/10
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpdateModelVo {

    @ApiModelProperty(value = "模板ID")
    private String model_id;

    @ApiModelProperty(value = "模板名称")
    private String name;

    @ApiModelProperty(value = "模板封面地址")
    private String cover_url;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "模板行业")
    private List<String> trade_ids;

    @ApiModelProperty(value = "一级模板行业")
    private List<String> parent_trade_ids;

    @ApiModelProperty(value = "模块样式id")
    private Integer module_style_id;

    @ApiModelProperty(value = "模块内容id")
    private Integer module_content_id;

    @ApiModelProperty(value = "模块高度")
    private Integer module_height;

    @ApiModelProperty(value = "模块权重")
    private Integer module_weight;

}
