package com.bilibili.mgk.platform.portal.webapi.material;

import com.bilibili.mgk.material.center.service.creative.InspirationCaseService;
import com.bilibili.mgk.material.center.service.creative.vo.AuditStatus;
import com.bilibili.mgk.material.center.service.creative.vo.InspirationCaseQuery;
import com.bilibili.mgk.material.center.service.creative.vo.InspirationCaseQuery.IndustrySubConditions;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.bilibili.mgk.material.center.util.JsonUtil;
import com.bilibili.mgk.platform.portal.annotation.FreeLogin;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.webapi.material.convertor.VOConvertor;
import com.bilibili.mgk.platform.portal.webapi.material.vo.InspirationCaseDetailVO;
import com.bilibili.mgk.platform.portal.webapi.material.vo.InspirationCaseVO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Splitter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.vavr.control.Try;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * 精选案例，灵感案例
 * <AUTHOR>
 * @desc
 * @date 2024/3/6
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/material/inspiration_case")
@Api(value = "/material/inspiration_case", tags = "material-center")
/**
 * TODO 目前FreeLogin 存在的坑很多
 * 1. 必须类和方法都添加注解
 * 2. 加了注解的同时还要必须添加配置passport.free.login.url.method.map{@link com.bilibili.mgk.platform.portal.filter.PassportInterceptor} 这里产生了耦合，而且实际上这个还重复冗余与webLdapFilter重合了  总之是filter、interceptor、aspect各种混乱
 * 3. 还需要注意 请求只能使用RequestMapping 而不能用GetMapping PostMapping
 * 4. 还必须在com.bilibili.mgk.platform.portal.webapi包下
 * 5. webApiSecurityFilter中LOGIN_FREE_URL_PATTERN 每个url只支持一个method
 */
@FreeLogin
public class InspirationCaseQueryController extends BasicController {

    @Resource
    private InspirationCaseService inspirationCaseService;


    private final DateTimeFormatter deliveryTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM");


    @ApiOperation(value = "创意素材中心-创意案例-列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @FreeLogin
    public Response<Pagination<List<InspirationCaseVO>>> list(

            @ApiParam("行业过滤项，目前行业不需要子过滤项，格式参考{'行业A':{}, '行业B':{}}")
            @RequestParam(value = "industry_filters", required = false) String industryFilters,
            @ApiParam("稿件类型， e.g 单人剧情 情景剧 单人口播 产品展示 素材混剪 动画, 支持批量逗号分割，")
            @RequestParam(value = "arch_type", required = false) String archType,
            @ApiParam("投放时间 起始时间 e.g 2024-03 yyyy-MM格式")
            @RequestParam(value = "delivery_time_from", required = false) String deliveryTimeFrom,
            @ApiParam("投放时间 结束时间 e.g 2024-04 yyyy-MM格式")
            @RequestParam(value = "delivery_time_to", required = false) String deliveryTimeTo,
            @ApiParam("是否竖屏， 0 横屏 1竖屏")
            @RequestParam(value = "is_vertical_screen", required = false) Integer isVerticalScreen,
            @ApiParam("页号") @RequestParam(value = "pn", defaultValue = "1") Integer pn,
            @ApiParam("页大小") @RequestParam(value = "ps", defaultValue = "20") Integer ps,
            @ApiParam("标题搜索") @RequestParam(value = "title", required = false) String title,
            @ApiParam("id搜索") @RequestParam(value = "id", required = false) String id

    ) {

        return Response.SUCCESS(inspirationCaseService
                .list(new InspirationCaseQuery()
                        .setAuditStatus(Stream.of(AuditStatus.PASS)
                                .map(AuditStatus::getCode)
                                .collect(Collectors.toList()))
                        .setDeleted(false)
                        .setIndustryFilters(Try.of(() -> {
                            return JsonUtil.readValue(industryFilters,
                                    new TypeReference<Map<String, IndustrySubConditions>>() {
                                    });
                        }).getOrElse(new HashMap<>()))
                        .setArchTypes(Optional.ofNullable(archType)
                                .filter(StringUtils::isNotBlank)
                                .map(type -> Splitter.on(",")
                                        .splitToList(type))
                                .orElse(null))
                        .setDeliveryTimeFrom(Optional.ofNullable(deliveryTimeFrom).map(time -> {

                            // 2024-03
                            YearMonth monthDate = YearMonth.parse(time, deliveryTimeFormatter);

                            LocalDateTime startDateTimeOfMonth = LocalDateTime.of(monthDate.getYear(),
                                    monthDate.getMonth(), 1, 0, 0, 0);

                            // 2024-03-01 00:00:00
                            return new Date(startDateTimeOfMonth.toInstant(ZoneOffset.of("+8")).toEpochMilli());

                        }).orElse(null))
                        .setDeliveryTimeTo(Optional.ofNullable(deliveryTimeTo).map(time -> {
                            // 2024-04
                            YearMonth nextMonthDate = YearMonth.parse(time, deliveryTimeFormatter).plusMonths(1);

                            LocalDateTime endDateTimeOfMonth = LocalDateTime.of(nextMonthDate.getYear(),
                                    nextMonthDate.getMonth(), 1, 0, 0, 0);

                            // 2024-05-01 00:00:00 exclusive
                            return new Date(endDateTimeOfMonth.toInstant(ZoneOffset.of("+8")).toEpochMilli());
                        }).orElse(null))
                        .setIsVerticalScreen(isVerticalScreen)
                        .setPn(pn)
                        .setPs(ps)
                        .setId(id)
                        .setTitle(title)
                )
                .map(list -> {
                    return list.stream().map(VOConvertor.convertor::toVO)
                            .collect(Collectors.toList());
                }));
    }


    @ApiOperation(value = "创意素材中心-创意案例-详情")
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    @FreeLogin
    public Response<InspirationCaseDetailVO> detail(
            @ApiParam("案例id") @RequestParam("id") Long id) {

        return Response.SUCCESS(VOConvertor.convertor.toDetail(
                inspirationCaseService.query(id)
        ));
    }

}
