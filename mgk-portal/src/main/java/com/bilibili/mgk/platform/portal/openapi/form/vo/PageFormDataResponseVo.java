package com.bilibili.mgk.platform.portal.openapi.form.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageFormDataResponseVo {

    @ApiModelProperty("提交后跳转url")
    private String next_page_url;

    @ApiModelProperty("表单数据id")
    private Long form_data_id;

}
