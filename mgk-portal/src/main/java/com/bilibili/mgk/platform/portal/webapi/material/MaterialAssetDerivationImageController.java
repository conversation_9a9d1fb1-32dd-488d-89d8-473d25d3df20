package com.bilibili.mgk.platform.portal.webapi.material;

import com.bilibili.mgk.material.center.service.asset.DerivationImageAssetService;
import com.bilibili.mgk.material.center.service.asset.model.DerivationImageGroup;
import com.bilibili.mgk.material.center.service.asset.vo.DerivationImgGroupListReq;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 我的资产-动态图文
 *
 * <AUTHOR>
 * @desc
 * @date 2024/6/18
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/material/asset/derivation_img")
@Api(value = "/material/asset/", tags = "创意素材中心")
public class MaterialAssetDerivationImageController extends BasicController {

    @Resource
    private DerivationImageAssetService derivationImageAssetService;


    @ApiOperation(value = "获取 mid 下动态列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Response<List<DerivationImageGroup>> midDynamics(
            @ApiIgnore Context context) {

        return Response.SUCCESS(
                derivationImageAssetService.listTopHotDerivationImgGroup(
                        new DerivationImgGroupListReq()
                                .setAccountId(Optional.ofNullable(context.getAccountId())
                                        .map(a -> (long) a)
                                        .orElse(null))
                )
        );
    }


}
