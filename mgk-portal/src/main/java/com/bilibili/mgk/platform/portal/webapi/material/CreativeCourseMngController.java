package com.bilibili.mgk.platform.portal.webapi.material;

import com.bilibili.mgk.material.center.service.course.CreativeCourseMngService;
import com.bilibili.mgk.material.center.service.course.model.CourseDirectory;
import com.bilibili.mgk.material.center.service.course.model.CreativeCourse;
import com.bilibili.mgk.material.center.service.course.vo.CourseAddReq;
import com.bilibili.mgk.material.center.service.course.vo.CourseDeleteReq;
import com.bilibili.mgk.material.center.service.course.vo.CoursePageReq;
import com.bilibili.mgk.material.center.service.course.vo.CourseUpdateReq;
import com.bilibili.mgk.material.center.service.course.vo.DirectoryAddReq;
import com.bilibili.mgk.material.center.service.course.vo.DirectoryDeleteReq;
import com.bilibili.mgk.material.center.service.course.vo.DirectoryListReq;
import com.bilibili.mgk.material.center.service.course.vo.DirectoryUpdateReq;
import com.bilibili.mgk.material.center.service.course.vo.SubDirectoryListReq;
import com.bilibili.mgk.platform.portal.annotation.FreeLogin;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Response;
import com.biz.common.doc.tree.common.Pagination;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * TODO 临时写在这里示意，实际赢刚在mng中
 *
 * <AUTHOR>
 * @desc
 * @date 2024/11/18
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/material/mng/course")
@Api(value = "/material/course", tags = "material-center")
@FreeLogin
public class CreativeCourseMngController extends BasicController {

    @Resource
    private CreativeCourseMngService creativeCourseMngService;


    /**
     * 查询所有二级目录
     *
     * @param req
     * @return
     */
    @ApiOperation("展示目录")
    @RequestMapping(value = "/directory/list", method = RequestMethod.POST)
    @FreeLogin
    public Response<List<CourseDirectory>> listAllTwoLevelDirectoryForMng(
            @ApiIgnore Context context,
            @RequestBody(required = false) DirectoryListReq req) {


        return Response.SUCCESS(creativeCourseMngService.listAllTwoLevelDirectoryForMng(req));
    }


    @ApiOperation("查询子目录")
    @RequestMapping(value = "/directory/sub/list", method = RequestMethod.POST)
    @FreeLogin
    public Response<List<CourseDirectory>> listSubDirectory(
            @ApiIgnore Context context,
            @RequestBody SubDirectoryListReq req) {

        return Response.SUCCESS(creativeCourseMngService.listSubDirectory(req));
    }

    /**
     * @param directoryAddReq
     * @return
     */
    @ApiOperation("添加目录")
    @RequestMapping(value = "/directory/add", method = RequestMethod.POST)
    @FreeLogin
    public Response<CourseDirectory> addDirectory(
            @ApiIgnore Context context,
            @RequestBody DirectoryAddReq directoryAddReq) {

        return Response.SUCCESS(creativeCourseMngService.addDirectory(directoryAddReq));
    }


    /**
     * @param directoryDeleteReq
     * @return
     */
    @ApiOperation("删除目录")
    @RequestMapping(value = "/directory/delete", method = RequestMethod.POST)
    @FreeLogin
    public Response<CourseDirectory> deleteDirectory(
            @ApiIgnore Context context,
            @RequestBody DirectoryDeleteReq directoryDeleteReq) {

        return Response.SUCCESS(creativeCourseMngService.deleteDirectory(directoryDeleteReq));
    }


    /**
     * @param directoryAddReq
     * @return
     */
    @ApiOperation("更新目录")
    @RequestMapping(value = "/directory/update", method = RequestMethod.POST)
    @FreeLogin
    public Response<CourseDirectory> updateDirectory(
            @ApiIgnore Context context,
            @RequestBody DirectoryUpdateReq directoryAddReq) {

        return Response.SUCCESS(creativeCourseMngService.updateDirectory(directoryAddReq));
    }


    /**
     * 分页查询课程
     *
     * @param coursePageReq
     * @return
     */
    @ApiOperation("分页查询课程")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @FreeLogin
    public Response<Pagination<List<CreativeCourse>>> pageCourseForMng(
            @ApiIgnore Context context,
            @RequestBody CoursePageReq coursePageReq) {

        return Response.SUCCESS(creativeCourseMngService.pageCourseForMng(coursePageReq));
    }


    /**
     * @param courseAddReq
     * @return
     */
    @ApiOperation("添加课程")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @FreeLogin
    public Response<CreativeCourse> addCourse(
            @ApiIgnore Context context,
            @RequestBody CourseAddReq courseAddReq) {

        return Response.SUCCESS(creativeCourseMngService.addCourse(courseAddReq));
    }

    /**
     * @param courseDeleteReq
     * @return
     */
    @ApiOperation("删除课程")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @FreeLogin
    public Response<CreativeCourse> deleteCourse(
            @ApiIgnore Context context,
            @RequestBody CourseDeleteReq courseDeleteReq) {

        return Response.SUCCESS(creativeCourseMngService.deleteCourse(courseDeleteReq));
    }


    /**
     * @param courseDeleteReq
     * @return
     */
    @ApiOperation("更新课程")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @FreeLogin
    public Response<CreativeCourse> updateCourse(
            @ApiIgnore Context context,
            @RequestBody CourseUpdateReq courseDeleteReq) {

        return Response.SUCCESS(creativeCourseMngService.updateCourse(courseDeleteReq));
    }


}
