package com.bilibili.mgk.platform.portal.openapi.form.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2020/07/07
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SignDataVo {
    @ApiModelProperty(value = "Base64加密的表单提交数据")
    private String device_param;

    @ApiModelProperty(value = "APPKEY")
    private String appkey;

    @ApiModelProperty(value = "mgk_page_id")
    private String mgk_page_id;
}
