package com.bilibili.mgk.platform.portal.webapi.material;

import com.bilibili.collage.api.dto.CollageMediaDto;
import com.bilibili.collage.api.service.ICollageMediaService;
import com.bilibili.mgk.material.center.service.aigc.AigcImg2ImgGenerationService;
import com.bilibili.mgk.material.center.service.aigc.dto.AIGeneratedImageDeletedReq;
import com.bilibili.mgk.material.center.service.aigc.dto.DislikeReasonsResp;
import com.bilibili.mgk.material.center.service.aigc.dto.GeneratedImageThumbupAction;
import com.bilibili.mgk.material.center.service.aigc.dto.HighCostCoverPageQuery;
import com.bilibili.mgk.material.center.service.aigc.dto.ImageAttitudeUpdateReq;
import com.bilibili.mgk.material.center.service.aigc.dto.ImageGenerationRecordDeletedReq;
import com.bilibili.mgk.material.center.service.aigc.dto.ImageToImageAuditAdviceResp;
import com.bilibili.mgk.material.center.service.aigc.dto.ImageToImageHistoryPageQuery;
import com.bilibili.mgk.material.center.service.aigc.dto.ImageToImageProgressQueryReq;
import com.bilibili.mgk.material.center.service.aigc.dto.ImageToImageProgressResp;
import com.bilibili.mgk.material.center.service.aigc.dto.ImageToImageSubmitReq;
import com.bilibili.mgk.material.center.service.aigc.dto.ImageToTextReq;
import com.bilibili.mgk.material.center.service.aigc.dto.ImageToTextResp;
import com.bilibili.mgk.material.center.service.aigc.model.CreativeHighCostCover;
import com.bilibili.mgk.material.center.service.aigc.model.Img2ImgGenerationHistory;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialSortBy;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialSortOrder;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.bilibili.mgk.platform.common.CollageMediaOriginEnum;
import com.bilibili.mgk.platform.portal.annotation.FreeLogin;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.service.WebCollageService;
import com.bilibili.mgk.platform.portal.webapi.collage.vo.CollageMediaVo;
import com.bilibili.mgk.platform.portal.webapi.material.vo.GeneratedImgSaveReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.vavr.control.Try;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.imageio.ImageIO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

/**
 * AIGC 图生图
 *
 * <AUTHOR>
 * @desc
 * @date 2024/7/18
 */

@Slf4j
@RestController
@RequestMapping("/web_api/v1/material/aigc/image")
@Api(value = "/material/aigc", tags = "material-center")
public class MaterialAigcImage2ImageController extends BasicController {


    @Resource
    private WebCollageService webCollageService;

    @Autowired
    private ICollageMediaService collageMediaService;

    @Resource
    private AigcImg2ImgGenerationService materialAigcImageService;


    private DateTimeFormatter imageNameTimeFormatter = DateTimeFormatter.ofPattern("yyyMMddHHmmss");

    @Value("${material.aigc.img2img.save-img-tpl:衍生图片-%sx%s-%s}")
    private String saveImgTpl;


    /**
     * https://cm-mng.bilibili.co/mgk/api/web_api/v1/collage/media
     * <p>
     * 此处仅开放新的接口，但是不做新的实现
     * <p>
     * TODO 增加一个图片去重功能
     *
     * @return
     */
    @ApiOperation(value = "本地上传媒体")
    @RequestMapping(value = "/upload_img", method = RequestMethod.POST)
    public Response<CollageMediaVo> mediaUpload(@ApiIgnore Context context,
            @ApiParam("图片文件 表单传送") @RequestParam("file") MultipartFile multipartFile) throws IOException {

        File file = webCollageService.multipartToFile(multipartFile);
        String url = webCollageService.uploadWithName(file);
        CollageMediaVo collageMediaVo = webCollageService.convertFile2Vo(file, url, 0);
        return Response.SUCCESS(collageMediaVo);
    }


    @ApiOperation(value = "本地上传媒体")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public Response<Void> mediaSave(
            @ApiIgnore Context context,
            @RequestBody GeneratedImgSaveReq req
    ) throws IOException {

        req.validate();

        List<CollageMediaDto> collageMedias = req.getSaveImgs().stream().map(img -> {
            BufferedImage bufferedImage = Try.of(() -> ImageIO.read(new URL(img.getImgUrl())))
                    .getOrElseThrow(t -> {
                        return new IllegalArgumentException("图片地址不正确");
                    });

            return CollageMediaDto.builder()
                    .mediaName(String.format(saveImgTpl,
                            bufferedImage.getWidth(), bufferedImage.getHeight(),
                            imageNameTimeFormatter.format(LocalDateTime.now())))
                    .worksId(null)
                    .patternId(null)
                    .mediaUrl(img.getImgUrl())
                    // ai生成目前不存在gif
                    .mediaType(1)
                    .mediaOrigin(CollageMediaOriginEnum.AI_DERIVATED.getCode())
                    .height(bufferedImage.getHeight())
                    .width(bufferedImage.getWidth())
                    .mediaRatio(bufferedImage.getWidth() * 10000 / bufferedImage.getHeight())
                    .mediaMd5(img.getImgMd5())
                    .mediaSize((long) bufferedImage.getData().getDataBuffer().getSize())
                    .build();

        }).collect(Collectors.toList());

        collageMediaService.insert(getOperator(context), collageMedias);
        return Response.SUCCESS(null);


    }


    @ApiOperation("图生文本")
    @RequestMapping(value = "/img2txt", method = RequestMethod.GET)
    public Response<ImageToTextResp> img2txt(
            @ApiIgnore Context context,
            @ApiParam("img_url") @RequestParam(value = "img_url") String imgUrl) {

        return Response.SUCCESS(materialAigcImageService.img2txt(new ImageToTextReq()
                .setImgUrl(imgUrl))
        );

    }


    @ApiOperation("图片检查")
    @RequestMapping(value = "/check", method = RequestMethod.GET)
    @FreeLogin
    public Response<ImageToImageAuditAdviceResp> imgCheck(
            @ApiIgnore Context context,
            @ApiParam("img_url") @RequestParam(value = "img_url") String imgUrl) {

        return Response.SUCCESS(materialAigcImageService.imgCheck(imgUrl));

    }


    // 同样入参的任务是否结果为不同，是，所以该接口不能做幂等；
    @ApiOperation("图生图-提交")
    @RequestMapping(value = "/img2img/submit", method = RequestMethod.GET)
    public Response<ImageToImageProgressResp> img2imgSubmit(
            @ApiIgnore Context context,
            @ApiParam("img_url") @RequestParam(value = "img_url") String imgUrl,
            @ApiParam("img_md5") @RequestParam(value = "img_md5") String imgMd5,
            @ApiParam("img_source, 1 本地上传， 2 素材库， 3 生成记录")
            @RequestParam(value = "img_source", defaultValue = "0") Integer imgSource,
            @ApiParam("相似度等级，级别1，2，3, 分别代表低中高") @RequestParam(value = "relevance_level") Integer relevanceLevel,
            @ApiParam("数量") @RequestParam(value = "quantity") Integer quantity,
            @ApiParam("提示次") @RequestParam(value = "prompt") String prompt,
            @ApiParam("是否保留文字") @RequestParam(value = "keep_text", required = false) Boolean keepText,
            @ApiParam("是否本地调度") @RequestParam(value = "schedule_at_local", defaultValue = "false") Boolean scheduleAtLocal,
            @ApiParam("输出图片的宽高比，直接使用目标图片宽高比的约分后的的参数值，并用'-'拼接，当前支持的宽高比为1-1, 4-3, 16-9, 8-5(即16比10)")
            @RequestParam(value = "img_ratio_type", required = false) String imgRatioType,
            @ApiParam("输出图片的宽, 使用相对比例时，如果是16-10，直接传16") @RequestParam(value = "out_img_width", required = false) Integer outImgWidth,
            @ApiParam("输出图片的高, 使用相对比例时，如果是16-10，直接传10") @RequestParam(value = "out_img_height", required = false) Integer outImgHeight
    ) {

        return Response.SUCCESS(materialAigcImageService.img2imgSubmit(
                new ImageToImageSubmitReq()
                        .setAccountId(Optional.ofNullable(context).map(Context::getAccountId)
                                .map(Integer::longValue).orElse(null))
                        .setImgUrl(imgUrl)
                        .setImgMd5(imgMd5)
                        .setQuantity(quantity)
                        .setImgSource(imgSource)
                        .setRelevanceLevel(relevanceLevel)
                        .setPrompt(prompt)
                        .setKeepText(keepText)
                        .setOutAbsoluteResolution(false)
                        .setOutImgWidth(outImgWidth)
                        .setOutImgHeight(outImgHeight)
                        .setImgRatioType(imgRatioType)
                        .setScheduleAtLocal(scheduleAtLocal)
        ));
    }


    @ApiOperation("图生图-进度查询")
    @RequestMapping(value = "/img2img/progress", method = RequestMethod.GET)
    public Response<ImageToImageProgressResp> img2imgProbe(

            @ApiIgnore Context context,
            @ApiParam("进度id") @RequestParam(value = "task_id") String taskId,
            @ApiParam("生成记录id") @RequestParam(value = "record_id") Long recordId

    ) {

        return Response.SUCCESS(
                materialAigcImageService.queryImg2ImgProgress(new ImageToImageProgressQueryReq()
                        .setTaskId(taskId)
                        .setRecordId(recordId)
                )
        );
    }


    @ApiOperation("图生图-历史")
    @RequestMapping(value = "/img2img/history/list", method = RequestMethod.GET)
    public Response<Pagination<List<Img2ImgGenerationHistory>>> listHistory(

            @ApiIgnore Context context,

            @ApiParam("页号") @RequestParam(value = "pn", defaultValue = "1") Integer pn,
            @ApiParam("页大小") @RequestParam(value = "ps", defaultValue = "20") Integer ps,

            @ApiParam("排序方式 ctime") @RequestParam(value = "sort_by", defaultValue = "ctime") MaterialSortBy sortBy,
            @ApiParam("升序降序 asc desc") @RequestParam(value = "order", defaultValue = "desc") MaterialSortOrder order

    ) {

        return Response.SUCCESS(
                materialAigcImageService.listImageGenerationHistory(
                        new ImageToImageHistoryPageQuery()
                                .setAccountId(Optional.ofNullable(context).map(Context::getAccountId)
                                        .map(Integer::longValue).orElse(null))
                                .setPn(pn)
                                .setPs(ps)
                                .setSortBy(sortBy)
                                .setOrder(order)
                ));
    }


    @ApiOperation("图生图-爆量素材列表推荐底图分页（当前推荐的排序口径为cost）")
    @RequestMapping(value = "/img2img/src_img/recommend/list", method = RequestMethod.GET)
    public Response<Pagination<List<CreativeHighCostCover>>> listRecommendSourceImgs(

            @ApiIgnore Context context,

            @ApiParam("页号") @RequestParam(value = "pn", defaultValue = "1") Integer pn,
            @ApiParam("页大小") @RequestParam(value = "ps", defaultValue = "20") Integer ps

    ) {

        return Response.SUCCESS(
                materialAigcImageService.listHighCostCover(
                        new HighCostCoverPageQuery()
                                .setAccountId(Optional.ofNullable(context).map(Context::getAccountId)
                                        .map(Integer::longValue).orElse(null))
                                .setPn(pn)
                                .setPs(ps)
                ));
    }


    @ApiOperation("图生图-删除记录")
    @RequestMapping(value = "/img2img/history/delete", method = RequestMethod.GET)
    public Response<Void> deleteHistoryRecord(

            @ApiIgnore Context context,
            @ApiParam("记录id") @RequestParam(value = "record_id") Long recordId

    ) {

        materialAigcImageService.deleteImageGenerationRecord(new ImageGenerationRecordDeletedReq()
                .setRecordId(recordId)
                .setAccountId(Optional.ofNullable(context).map(Context::getAccountId)
                        .map(Integer::longValue).orElse(null)));

        return Response.SUCCESS(null);

    }


    @ApiOperation("图生图-图片点赞点踩")
    @RequestMapping(value = "/img2img/img/thumbup", method = RequestMethod.GET)
    public Response<Void> deleteHistoryRecord(

            @ApiIgnore Context context,
            @ApiParam("记录id") @RequestParam(value = "record_id") Long recordId,
            @ApiParam("生成图片MD5") @RequestParam(value = "gen_img_md5") String imgMd5,
            @ApiParam("点赞点踩类型，like/dislike/cancel_like/cancel_dislike")
            @RequestParam(value = "action") GeneratedImageThumbupAction action,
            @ApiParam("点踩理由, 逗号,拼接 ") @RequestParam(value = "dislike_reasons", required = false) String dislikeReasons
    ) {
        materialAigcImageService.likeDislikeAIGeneratedImg(
                new ImageAttitudeUpdateReq()
                        .setRecordId(recordId)
                        .setAccountId(Optional.ofNullable(context).map(Context::getAccountId)
                                .map(Integer::longValue).orElse(null))
                        .setGenImgMd5(imgMd5)
                        .setAction(action)
                        .setDislikeReasons(dislikeReasons)
        );

        return Response.SUCCESS(null);

    }


    @ApiOperation("图生图-点踩理由")
    @RequestMapping(value = "/img2img/img/dislike_reasons", method = RequestMethod.GET)
    public Response<DislikeReasonsResp> dislikeReasons(@ApiIgnore Context context) {

        return Response.SUCCESS(materialAigcImageService.dislikeReasons());

    }


    @ApiOperation("图生图-删除记录中图片")
    @RequestMapping(value = "/img2img/history/img/delete", method = RequestMethod.GET)
    public Response<Void> deleteHistoryRecord(

            @ApiIgnore Context context,
            @ApiParam("记录id") @RequestParam(value = "record_id") Long recordId,
            @ApiParam("生成图片MD5") @RequestParam(value = "gen_img_md5") String imgMd5

    ) {

        materialAigcImageService.deleteGeneratedImage(new AIGeneratedImageDeletedReq()
                .setRecordId(recordId)
                .setGenImgMd5(imgMd5)
                .setAccountId(Optional.ofNullable(context).map(Context::getAccountId)
                        .map(Integer::longValue).orElse(null))
        );

        return Response.SUCCESS(null);

    }


}
