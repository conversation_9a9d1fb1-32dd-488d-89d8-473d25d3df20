package com.bilibili.mgk.platform.portal.webapi.home.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @file: PointVo
 * @author: gaoming
 * @date: 2021/07/06
 * @version: 1.0
 * @description:
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PointVo {

    @ApiModelProperty(value = "时间")
    private String time;
    @ApiModelProperty(value = "值")
    private Long value;
    @ApiModelProperty(value = "格式化")
    private String value_format;
}
