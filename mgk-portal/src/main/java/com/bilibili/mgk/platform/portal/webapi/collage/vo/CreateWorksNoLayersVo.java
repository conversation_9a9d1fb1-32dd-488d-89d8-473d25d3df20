package com.bilibili.mgk.platform.portal.webapi.collage.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @file: CreateWorksNoLayersVo
 * @author: gaoming
 * @date: 2020/11/25
 * @version: 1.0
 * @description: 创建无图层的作品，主要适配gif作品，视频作品
 * 和之前的图层渲染进行区分
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateWorksNoLayersVo {

    @ApiModelProperty(value = "作品名称")
    private String name;

    @ApiModelProperty(value = "尺寸Id")
    private Integer collage_size_id;

    @ApiModelProperty(value = "作品url")
    private String works_url;

    @ApiModelProperty(value = "作品md5")
    private String works_md5;

    @ApiModelProperty(value = "作品大小")
    private Long works_size;

    @ApiModelProperty(value = "总时长")
    private Integer total_duration;

    @ApiModelProperty(value = "每帧时长")
    private Integer duration_perFrame;

    @ApiModelProperty(value = "帧数")
    private Integer frames;

    @ApiModelProperty(value = "轮播次数")
    private Integer rounds_number;

    @ApiModelProperty(value = "封面")
    private CollageCoverVo cover;

}
