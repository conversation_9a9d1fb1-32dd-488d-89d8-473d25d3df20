package com.bilibili.mgk.platform.portal.service;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.passport.api.dto.ArchiveDetail;
import com.bilibili.adp.passport.biz.manager.ArchiveManager;
import com.bilibili.bjcom.mock.MockitoDefaultTest;
import com.bilibili.mgk.platform.api.landing_page.dto.LandingPageConfigDto;
import com.bilibili.mgk.platform.api.landing_page.dto.MgkLandingPageDto;
import com.bilibili.mgk.platform.portal.webapi.landing_page.vo.NewLandingPageVo;
import com.bilibili.mgk.platform.portal.webapi.landing_page.vo.UpdateLandingPageVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.sql.Timestamp;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2020/06/19
 **/
public class WebLandingPageServiceTest extends MockitoDefaultTest {
    @InjectMocks
    private WebLandingPageService webLandingPageService;

    @Mock
    private ArchiveManager archiveManager;

    public void setUp() throws Exception {
    }

    @Test
    public void testConvertNewLandingPageVo2Dto() {
        webLandingPageService.convertNewLandingPageVo2Dto(1000, mock(NewLandingPageVo.class));
    }

    @Test
    public void testConvertUpdateLandingPageVo2Dto() {
        webLandingPageService.convertUpdateLandingPageVo2Dto(mock(UpdateLandingPageVo.class));
    }

    @Test
    public void testConvertLandingPageDtos2Vos() throws ServiceException {
        Timestamp nowTime = new Timestamp(System.currentTimeMillis());
        when(archiveManager.getArchivesByAids(any())).thenReturn(Maps.newHashMap());
        webLandingPageService.convertLandingPageDtos2Vos(Lists.newArrayList(MgkLandingPageDto.builder()
                .templateStyle(301)
                .avIds(Lists.newArrayList(1L))
                .showUrls(Lists.newArrayList(""))
                .type(1)
                .mtime(nowTime)
                .reason("reason")
                .creator("SYS_TEST")
                .status(1)
                .effectiveEndTime(nowTime)
                .effectiveStartTime(nowTime)
                .title("title")
                .name("name")
                .pageId(1L)
                .accountId(10005)
                .id(1)
                .isModel(1)
                .modelId(1L)
                .modelName("model Name")
                .isPc(1)
                .shadowStatus(-1)
                .build()));
    }

    @Test
    public void testConvertLandingPageConfigDto2Vo(){
        Timestamp nowTime = new Timestamp(System.currentTimeMillis());
        webLandingPageService.convertLandingPageConfigDto2Vo(LandingPageConfigDto.builder()
                .status(1)
                .accountId(10005)
                .avIds(Lists.newArrayList(1L))
                .config("config")
                .creator("SYS_TEST")
                .effectiveEndTime(nowTime)
                .effectiveStartTime(nowTime)
                .id(1)
                .isModel(1)
                .isPc(1)
                .modelId(1L)
                .name("name")
                .pageId(1L)
                .reason("reason")
                .showUrls(Lists.newArrayList("http://www.baidu.com"))
                .templateStyle(301)
                .title("title")
                .type(1)
                .build());
    }

}