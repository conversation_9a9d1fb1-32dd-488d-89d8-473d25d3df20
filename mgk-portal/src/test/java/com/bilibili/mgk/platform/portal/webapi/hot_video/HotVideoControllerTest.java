package com.bilibili.mgk.platform.portal.webapi.hot_video;

import com.bilibili.mgk.platform.api.hot_video.service.IHotVideoBlackService;
import com.bilibili.mgk.platform.api.hot_video.service.IHotVideoCollectService;
import com.bilibili.mgk.platform.api.hot_video.service.IHotVideoService;
import com.bilibili.mgk.platform.common.MgkHotVideoCollectTypeEnum;
import com.bilibili.mgk.platform.common.MgkHotVideoDataTypeEnum;
import com.bilibili.mgk.platform.common.MgkHotVideoOrderByEnum;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.service.WebHotVideoService;
import com.bilibili.mgk.platform.portal.test.BaseMockitoTest;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Collections;

/**
 * @file: HotVideoControllerTest
 * @author: gaoming
 * @date: 2020/11/16
 * @version: 1.0
 * @description:
 **/
public class HotVideoControllerTest extends BaseMockitoTest {

    @InjectMocks
    private HotVideoController hotVideoController;

    @Mock
    private IHotVideoService hotVideoService;

    @Mock
    private WebHotVideoService webHotVideoService;

    @Mock
    private IHotVideoCollectService hotVideoCollectService;

    @Mock
    private IHotVideoBlackService hotVideoBlackService;

    private Context context;

    @Before
    public void setUp() throws Exception {
        context = new Context();
        context.setAccountId(10005);
        context.setProxyId(1);
        context.setUsername("双面妖姬");
        context.setSalesType(1);
        context.setType(1);
        context.setProxyName("代理");
    }

    @Test
    public void testGetList() {
        hotVideoController.getList(context,
                "",
                "电磁炮真是太可爱了",
                Lists.newArrayList("鬼畜"),
                Collections.emptyList(),
                1,
                MgkHotVideoDataTypeEnum.LAST_WEEK.getCode(),
                MgkHotVideoOrderByEnum.COIN_DAILY.getCode(),
                0,
                1,
                15);
    }

    @Test
    public void testGetHotVideoById() {
        hotVideoController.getHotVideoById(context, "100005", MgkHotVideoDataTypeEnum.LAST_WEEK.getCode());
    }

    @Test
    public void testGetBussInterest() {
        hotVideoController.getBussInterest(context);
    }

    @Test
    public void testGetTname() {
        hotVideoController.getTname(context);
    }

    @Test
    public void testDoCollect() {
        hotVideoController.doCollect(context, "10005");
    }

    @Test
    public void testCancelCollect() {
        hotVideoController.cancelCollect(context, "10005");
    }

    @Test
    public void testGetCollectList() {
        hotVideoController.getCollectList(context, "", Lists.newArrayList(1), 1, 15);
    }

    @Test
    public void testGetListWithBlack() {
        hotVideoController.getListWithBlack(context, "1", "title", Lists.newArrayList("1"), Collections.emptyList(), 1, 1, 1, 1, 15);
    }

    @Test
    public void testEnbale() {
        hotVideoController.enable(context, "1", 1);
    }

    @Test
    public void testDisable() {
        hotVideoController.disable(context, "1", 1);
    }
}