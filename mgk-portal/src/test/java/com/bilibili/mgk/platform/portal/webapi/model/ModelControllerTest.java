package com.bilibili.mgk.platform.portal.webapi.model;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.mgk.platform.api.model.service.IMgkModelService;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.service.WebModelService;
import com.bilibili.mgk.platform.portal.test.BaseMockitoTest;
import com.bilibili.mgk.platform.portal.webapi.landing_page.vo.NewLandingPageVo;
import com.bilibili.mgk.platform.portal.webapi.model.vo.NewModelVo;
import com.bilibili.mgk.platform.portal.webapi.model.vo.NewTradeVo;
import com.bilibili.mgk.platform.portal.webapi.model.vo.UpdateModelVo;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2020/08/10
 **/
public class ModelControllerTest extends BaseMockitoTest {

    @InjectMocks
    private ModelController modelController;
    @Mock
    private IMgkModelService mgkModelService;
    @Mock
    private WebModelService webModelService;
    @Mock
    private BasicController basicController;

    private Context context;

    @Before
    public void setUp() throws Exception {
        context = new Context();
        context.setAccountId(123);
        context.setType(1);
        context.setProxyId(1);
        context.setProxyName("12");
    }

    @Test
    public void testGetModelList() throws ServiceException {
        List<String> modelIds = Lists.newArrayList("1");
        String nameLike = "";
        List<Integer> modelStyles = Lists.newArrayList(1);
        List<Integer> modelTypes = Lists.newArrayList(1);
        List<Integer> statusList = Lists.newArrayList(1);
        List<String> tradeIds = Lists.newArrayList("1");
        Integer page = 1;
        Integer size = 15;
//        modelController.getModelList(context, modelIds, nameLike, modelStyles, modelTypes, statusList, tradeIds, page, size);
    }

    @Test
    public void testGetModel() throws ServiceException {
        String modelId = "123";
        modelController.getModel(context, modelId);
    }

    @Test
    public void testUpdateModelConfig() {
        UpdateModelVo vo = UpdateModelVo.builder()
                .trade_ids(Lists.newArrayList("12334"))
                .remark("")
                .cover_url("")
                .name("12")
                .model_id("1234")
                .build();
        when(basicController.getOperator(any())).thenReturn(Operator.builder()
                .operatorId(123)
                .operatorName("")
                .operatorType(OperatorType.BILIBILIER)
                .systemType(SystemType.CPT)
                .bilibiliUserName("")
                .build());
        modelController.updateModelConfig(context, vo);
    }

    @Test
    public void testCreate() throws ServiceException {
        List<Integer>list=new ArrayList<>();
        list.add(1);
        NewModelVo vo = NewModelVo.builder()
                .page(NewLandingPageVo.builder()
                        .app_package_id(1)
                        .app_package_ids(Lists.newArrayList(123))
                        .av_ids(Lists.newArrayList(1L))
                        .biz_ids(list)
                        .config("")
                        .effective_end_time(System.currentTimeMillis())
                        .effective_start_time(System.currentTimeMillis())
                        .form_id("1")
                        .form_ids(Lists.newArrayList("1"))
                        .is_model(1)
                        .model_id(123L)
                        .name("12")
                        .origin_page_id("123")
                        .page_type(1)
                        .page_version("0x0x0x")
                        .show_urls(Lists.newArrayList("www.bilibili.com"))
                        .template_style(1)
                        .title("title")
                        .build())
                .remark("")
                .cover_url("http://www.bilibili.com")
                .model_type(1)
                .model_style(1)
                .name("")
                .trade_ids(Lists.newArrayList("1"))
                .model_version("0x0x0x")
                .build();
        modelController.create(context, vo);
    }

    @Test
    public void testPublish() throws ServiceException {
        String modelId = "123";
        modelController.publish(context, modelId);
    }

    @Test
    public void testDownline() throws ServiceException {
        modelController.downline(context, "123");
    }

    @Test
    public void testBatchDelete() throws ServiceException {
        modelController.batchDelete(context, Lists.newArrayList("1"));
    }

    @Test
    public void testGetTrade() {
        List<String> parentTradeIds = Lists.newArrayList("1");
        List<String> tradeIds = Lists.newArrayList("1");
        List<Integer> level = Lists.newArrayList(1);
        String nameLike = "";
        modelController.getTrade(context, parentTradeIds, tradeIds, level, nameLike);
    }

    @Test
    public void testCreateTrade() throws ServiceException {
        NewTradeVo vo = NewTradeVo.builder()
                .name("")
                .level(1)
                .parent_trade_id("1")
                .build();
        modelController.createTrade(context, vo);
    }

    @Test
    public void testHasRights() throws ServiceException {
        modelController.hasRights(context);
    }

    @Test
    public void testGetModelUsed() {
        modelController.getModelUsed(context);
    }
}