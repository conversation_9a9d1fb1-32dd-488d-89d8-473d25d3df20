package com.bilibili.collage.biz.service;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.collage.api.dto.DrawingReqDto;
import com.bilibili.collage.api.dto.PatternDto;
import com.bilibili.collage.biz.BaseMockitoTest;
import com.bilibili.collage.biz.service.customer.DrawingCacheService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2019/4/2
 **/
@Slf4j
public class DrawingCacheServiceTest extends BaseMockitoTest {

    @InjectMocks
    private DrawingCacheService testObject;

    @Mock
    private SnowflakeIdWorker snowflakeIdWorker;

    @Mock
    private RedisTemplate commonRedisTemplate;

    @Mock
    private HashOperations redisOpt;

    @Test
    public void saveDrawingCache () {

        RedisTemplate redisTemplate = Mockito.mock(RedisTemplate.class);
        Mockito.when(redisTemplate.expire(Mockito.any(), Mockito.anyLong(), Mockito.any())).thenReturn(Boolean.TRUE);

        Mockito.when(redisOpt.getOperations()).thenReturn(redisTemplate);

        PageResult<PatternDto> matching = new PageResult();
        List<PatternDto> list = Lists.newArrayList(PatternDto.builder().id(1).build());
        matching.setRecords(list);
        testObject.saveDrawingCache(DrawingReqDto.builder().build(), matching);
    }

}
