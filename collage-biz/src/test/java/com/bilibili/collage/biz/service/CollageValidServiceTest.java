package com.bilibili.collage.biz.service;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.collage.api.dto.*;
import com.bilibili.collage.api.service.ICollageSizeService;
import com.bilibili.collage.api.service.IFontLibraryService;
import com.bilibili.collage.api.service.IPatternService;
import com.bilibili.collage.biz.BaseMockitoTest;
import com.bilibili.collage.biz.dao.MgkCollagePatternDao;
import com.bilibili.collage.biz.po.MgkCollagePatternPo;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;

import java.sql.Timestamp;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2020/09/18
 **/
public class CollageValidServiceTest extends BaseMockitoTest {

    @InjectMocks
    private CollageValidService collageValidService;

    @Mock
    private IPatternService patternService;

    @Mock
    private IFontLibraryService fontLibraryService;

    @Mock
    private ICollageSizeService collageSizeService;

    @Mock
    private CollageWorksServiceDelegate collageWorksServiceDelegate;

    @Mock
    private MgkCollagePatternDao mgkCollagePatternDao;

    private PatternDto patternDto;

    private CollageFontLibraryDto collageFontLibraryDto;

    private CollageSizeDto collageSizeDto;


    @Before
    public void setUp() throws Exception {

        patternDto = PatternDto.builder()
                .name("name")
                .collageSizeId(1)
                .tagCode("1")
                .layerDtos(Lists.newArrayList(LayerDto.builder().build()))
                .patternId(1L)
                .width(1)
                .sizeDesc("size")
                .height(1)
                .status(1)
                .renderImage("image")
                .mtime(new Timestamp(System.currentTimeMillis()))
                .isDeleted(IsDeleted.VALID.getCode())
                .creator("SYS_TEST")
                .tagId(1)
                .industryIds(new Integer[]{1})
                .edition(1)
                .id(1)
                .build();
        collageFontLibraryDto = CollageFontLibraryDto.builder()
                .status(1)
                .id(1)
                .url("http://bilibili.com")
                .name("name")
                .edition(1)
                .build();

        collageSizeDto = CollageSizeDto.builder()
                .isDeleted(IsDeleted.VALID.getCode())
                .height(1)
                .width(1)
                .status(0)
                .sketch("http://bilibili.com")
                .id(1)
                .desc("desc")
                .edition(1)
                .build();

    }

    @Test(expected = IllegalArgumentException.class)
    public void testValidCreatePattern() {
        collageValidService.validCreatePattern(patternDto);
    }

    @Test
    public void testValidEditLayer() {
        collageValidService.validEditLayer(patternDto);
    }

    @Test
    public void testValidEditBaseInfoPattern() {
        collageValidService.validEditBaseInfoPattern(patternDto);
    }

    @Test
    public void testValidUpdatePatternStatus() {
        when(patternService.getPatternBaseInfoById(any())).thenReturn(patternDto);
        collageValidService.validUpdatePatternStatus(1, 0);
    }

    @Test
    public void testValidFontLibrary() {
        collageValidService.validFontLibrary(collageFontLibraryDto);
    }

    @Test
    public void testValidCeateCollageSize() {
        collageValidService.validCeateCollageSize(collageSizeDto);
    }

    @Test
    public void testValidUpdateCollageSize() {
        collageValidService.validUpdateCollageSize(collageSizeDto);
    }

    @Test
    public void testValidUpdateSizeStatus() {
        when(collageSizeService.getCollageSizeById(any())).thenReturn(collageSizeDto);
        collageValidService.validUpdateSizeStatus(1, 1);
    }

    @Test
    public void testValidCreateWorks() {
        collageValidService.validCreateWorks(NewCollageWorksDto.builder()
                .name("name")
                .worksOrigin(1)
                .patternId(1)
                .layerDtos(Lists.newArrayList(LayerDto.builder().build()))
                .collageSizeId(1)
                .build());
    }

    @Test
    public void testValidEditWorksLayer() {
        collageValidService.validEditWorksLayer(operator, CollageEditWorksLayerDto.builder()
                .id(1)
                .layers(Lists.newArrayList(LayerDto.builder().build()))
                .build());
    }

    @Test
    public void testValidEditWorkName() {
        collageValidService.validEditWorkName(operator, 1, "name");
    }

    @Test
    public void testValidCreateWorksNoLayers() {
        collageValidService.validCreateWorksNoLayers(NewCollageWorksDto.builder()
                .roundsNumber(1)
                .frames(1)
                .durationPerFrame(1)
                .totalDuration(1)
                .cover(CollageCoverDto.builder()
                        .coverMd5("md5")
                        .coverSize(1)
                        .coverUrl("http://www.bilibili.com")
                        .ratio(1)
                        .height(1)
                        .width(1)
                        .id(1)
                        .objType(1)
                        .objId(1)
                        .coverId(1L)
                        .build())
                .worksSize(1L)
                .worksMd5("md5")
                .worksUrl("http://www.bilibili.com")
                .worksOrigin(1)
                .patternId(1)
                .name("name")
                .collageSizeId(1)
                .build());
    }

    @Test
    public void testValidMediaDtos() {
        collageValidService.validMediaDtos(operator, Lists.newArrayList(CollageMediaDto.builder()
                .cover(CollageCoverDto.builder()
                        .coverId(1L)
                        .objId(1)
                        .objType(1)
                        .id(1)
                        .width(1)
                        .height(1)
                        .ratio(1)
                        .coverUrl("http://www.bilibili.com")
                        .coverSize(1)
                        .coverMd5("md5")
                        .build())
                .roundsNumber(1)
                .frames(1)
                .durationPerFrame(1)
                .totalDuration(1)
                .mediaSize(1L)
                .mediaMd5("md5")
                .height(1)
                .width(1)
                .mediaRatio(1)
                .mediaUrl("http://wwww.bilibili.com")
                .mediaName("媒体名称")
                .mediaOrigin(1)
                .mediaId(1L)
                .accountId(1)
                .isDeleted(IsDeleted.VALID.getCode())
                .id(1)
                .worksId(1)
                .patternId(1)
                .mediaType(1)
                .build()));
    }

    @Test
    public void testValidCreateCover() {
        collageValidService.validCreateCover(CollageCoverDto.builder()
                .coverMd5("md5")
                .coverSize(1)
                .coverUrl("http://www.bilibili.com")
                .ratio(1)
                .height(1)
                .width(1)
                .id(1)
                .objType(1)
                .objId(1)
                .coverId(1L)
                .build());
    }
}