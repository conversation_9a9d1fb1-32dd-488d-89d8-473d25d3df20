<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.collage.biz.dao.ext.ExtMgkCollageLayerDao">

  <insert id="saveDuplicate" parameterType="com.bilibili.collage.biz.po.MgkCollageLayerPo">
    insert into mgk_collage_layer
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="xAxis != null">
        x_axis,
      </if>
      <if test="yAxis != null">
        y_axis,
      </if>
      <if test="width != null">
        width,
      </if>
      <if test="height != null">
        height,
      </if>
      <if test="rotate != null">
        rotate,
      </if>
      <if test="opacity != null">
        opacity,
      </if>
      <if test="seq != null">
        seq,
      </if>
      <if test="imageUrl != null">
        image_url,
      </if>
      <if test="imageMd5 != null">
        image_md5,
      </if>
      <if test="imageWidth != null">
        image_width,
      </if>
      <if test="imageHeight != null">
        image_height,
      </if>
      <if test="imageLock != null">
        image_lock,
      </if>
      <if test="bgColor != null">
        bg_color,
      </if>
      <if test="textVal != null">
        text_val,
      </if>
      <if test="fontFamilyId != null">
        font_family_id,
      </if>
      <if test="textColor != null">
        text_color,
      </if>
      <if test="textAlign != null">
        text_align,
      </if>
      <if test="fontStyle != null">
        font_style,
      </if>
      <if test="fontSize != null">
        font_size,
      </if>
      <if test="fontWeight != null">
        font_weight,
      </if>
      <if test="underline != null">
        underline,
      </if>
      <if test="linethrough != null">
        linethrough,
      </if>
      <if test="patternId != null">
        pattern_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="worksId != null">
        works_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="xAxis != null">
        #{xAxis,jdbcType=INTEGER},
      </if>
      <if test="yAxis != null">
        #{yAxis,jdbcType=INTEGER},
      </if>
      <if test="width != null">
        #{width,jdbcType=INTEGER},
      </if>
      <if test="height != null">
        #{height,jdbcType=INTEGER},
      </if>
      <if test="rotate != null">
        #{rotate,jdbcType=INTEGER},
      </if>
      <if test="opacity != null">
        #{opacity,jdbcType=TINYINT},
      </if>
      <if test="seq != null">
        #{seq,jdbcType=TINYINT},
      </if>
      <if test="imageUrl != null">
        #{imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="imageMd5 != null">
        #{imageMd5,jdbcType=VARCHAR},
      </if>
      <if test="imageWidth != null">
        #{imageWidth,jdbcType=INTEGER},
      </if>
      <if test="imageHeight != null">
        #{imageHeight,jdbcType=INTEGER},
      </if>
      <if test="imageLock != null">
        #{imageLock,jdbcType=TINYINT},
      </if>
      <if test="bgColor != null">
        #{bgColor,jdbcType=VARCHAR},
      </if>
      <if test="textVal != null">
        #{textVal,jdbcType=VARCHAR},
      </if>
      <if test="fontFamilyId != null">
        #{fontFamilyId,jdbcType=INTEGER},
      </if>
      <if test="textColor != null">
        #{textColor,jdbcType=VARCHAR},
      </if>
      <if test="textAlign != null">
        #{textAlign,jdbcType=VARCHAR},
      </if>
      <if test="fontStyle != null">
        #{fontStyle,jdbcType=VARCHAR},
      </if>
      <if test="fontSize != null">
        #{fontSize,jdbcType=TINYINT},
      </if>
      <if test="fontWeight != null">
        #{fontWeight,jdbcType=VARCHAR},
      </if>
      <if test="underline != null">
        #{underline,jdbcType=TINYINT},
      </if>
      <if test="linethrough != null">
        #{linethrough,jdbcType=TINYINT},
      </if>
      <if test="patternId != null">
        #{patternId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="worksId != null">
        #{worksId,jdbcType=INTEGER},
      </if>
    </trim>
    on duplicate key update
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="xAxis != null">
        x_axis = #{xAxis,jdbcType=INTEGER},
      </if>
      <if test="yAxis != null">
        y_axis = #{yAxis,jdbcType=INTEGER},
      </if>
      <if test="width != null">
        width = #{width,jdbcType=INTEGER},
      </if>
      <if test="height != null">
        height = #{height,jdbcType=INTEGER},
      </if>
      <if test="rotate != null">
        rotate = #{rotate,jdbcType=INTEGER},
      </if>
      <if test="opacity != null">
        opacity = #{opacity,jdbcType=TINYINT},
      </if>
      <if test="seq != null">
        seq = #{seq,jdbcType=TINYINT},
      </if>
      <if test="imageUrl != null">
        image_url = #{imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="imageMd5 != null">
        image_md5 = #{imageMd5,jdbcType=VARCHAR},
      </if>
      <if test="imageWidth != null">
        image_width = #{imageWidth,jdbcType=INTEGER},
      </if>
      <if test="imageHeight != null">
        image_height = #{imageHeight,jdbcType=INTEGER},
      </if>
      <if test="imageLock != null">
        image_lock = #{imageLock,jdbcType=TINYINT},
      </if>
      <if test="bgColor != null">
        bg_color = #{bgColor,jdbcType=VARCHAR},
      </if>
      <if test="textVal != null">
        text_val = #{textVal,jdbcType=VARCHAR},
      </if>
      <if test="fontFamilyId != null">
        font_family_id = #{fontFamilyId,jdbcType=INTEGER},
      </if>
      <if test="textColor != null">
        text_color = #{textColor,jdbcType=VARCHAR},
      </if>
      <if test="textAlign != null">
        text_align = #{textAlign,jdbcType=VARCHAR},
      </if>
      <if test="fontStyle != null">
        font_style = #{fontStyle,jdbcType=VARCHAR},
      </if>
      <if test="fontSize != null">
        font_size = #{fontSize,jdbcType=TINYINT},
      </if>
      <if test="fontWeight != null">
        font_weight = #{fontWeight,jdbcType=VARCHAR},
      </if>
      <if test="underline != null">
        underline = #{underline,jdbcType=TINYINT},
      </if>
      <if test="linethrough != null">
        linethrough = #{linethrough,jdbcType=TINYINT},
      </if>
      <if test="patternId != null">
        pattern_id = #{patternId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="worksId != null">
        works_id = #{worksId,jdbcType=INTEGER}
      </if>
  </insert>
</mapper>