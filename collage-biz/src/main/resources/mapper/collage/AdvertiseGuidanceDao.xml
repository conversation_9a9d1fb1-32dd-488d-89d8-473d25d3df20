<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.collage.biz.dao.AdvertiseGuidanceDao">
  <resultMap id="BaseResultMap" type="com.bilibili.collage.biz.po.AdvertiseGuidancePo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="image" jdbcType="VARCHAR" property="image" />
    <result column="industry_id" jdbcType="BIGINT" property="industryId" />
    <result column="industry_name" jdbcType="VARCHAR" property="industryName" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_num, image, industry_id, industry_name, is_deleted, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.collage.biz.po.AdvertiseGuidancePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from advertise_guidance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from advertise_guidance
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from advertise_guidance
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.collage.biz.po.AdvertiseGuidancePoExample">
    delete from advertise_guidance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.collage.biz.po.AdvertiseGuidancePo">
    insert into advertise_guidance (id, order_num,
      image, industry_id, industry_name, 
      is_deleted, ctime, mtime
      )
    values (#{id,jdbcType=BIGINT}, #{orderNum,jdbcType=INTEGER},
      #{image,jdbcType=VARCHAR}, #{industryId,jdbcType=BIGINT}, #{industryName,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.collage.biz.po.AdvertiseGuidancePo">
    insert into advertise_guidance
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderNum != null">
        order_num,
      </if>
      <if test="image != null">
        image,
      </if>
      <if test="industryId != null">
        industry_id,
      </if>
      <if test="industryName != null">
        industry_name,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orderNum != null">
        #{orderNum,jdbcType=INTEGER},
      </if>
      <if test="image != null">
        #{image,jdbcType=VARCHAR},
      </if>
      <if test="industryId != null">
        #{industryId,jdbcType=BIGINT},
      </if>
      <if test="industryName != null">
        #{industryName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.collage.biz.po.AdvertiseGuidancePoExample" resultType="java.lang.Long">
    select count(*) from advertise_guidance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update advertise_guidance
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderNum != null">
        order_num = #{record.orderNum,jdbcType=INTEGER},
      </if>
      <if test="record.image != null">
        image = #{record.image,jdbcType=VARCHAR},
      </if>
      <if test="record.industryId != null">
        industry_id = #{record.industryId,jdbcType=BIGINT},
      </if>
      <if test="record.industryName != null">
        industry_name = #{record.industryName,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update advertise_guidance
    set id = #{record.id,jdbcType=BIGINT},
      order_num = #{record.orderNum,jdbcType=INTEGER},
      image = #{record.image,jdbcType=VARCHAR},
      industry_id = #{record.industryId,jdbcType=BIGINT},
      industry_name = #{record.industryName,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.collage.biz.po.AdvertiseGuidancePo">
    update advertise_guidance
    <set>
      <if test="orderNum != null">
        order_num = #{orderNum,jdbcType=INTEGER},
      </if>
      <if test="image != null">
        image = #{image,jdbcType=VARCHAR},
      </if>
      <if test="industryId != null">
        industry_id = #{industryId,jdbcType=BIGINT},
      </if>
      <if test="industryName != null">
        industry_name = #{industryName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.collage.biz.po.AdvertiseGuidancePo">
    update advertise_guidance
    set order_num = #{orderNum,jdbcType=INTEGER},
      image = #{image,jdbcType=VARCHAR},
      industry_id = #{industryId,jdbcType=BIGINT},
      industry_name = #{industryName,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>