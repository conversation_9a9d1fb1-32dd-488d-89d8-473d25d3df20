package com.bilibili.collage.biz.service.school.doctree;

import com.bilibili.collage.biz.service.school.doctree.model.SanlianDocNode;
import com.bilibili.collage.biz.service.school.doctree.model.SanlianDocTreeNode;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodeBatchGetReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodeCreateReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodeGetReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodeListReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodeMoveReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodePageReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodeUpdateReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianSubTreeReq;
import com.biz.common.doc.tree.common.Pagination;
import com.biz.common.doc.tree.service.vo.NodeDeleteReq;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/12/18
 */
public interface SanlianSchoolDocTreeMngService {

    /**
     * 新建节点
     * @param req
     * @return
     */
    SanlianDocNode newNode(SanlianNodeCreateReq req);

    /**
     * 更新节点
     *
     * @param req
     * @return
     */
    SanlianDocNode updateNode(SanlianNodeUpdateReq req);

    /**
     * 删除节点
     * @param req
     * @return
     */

    SanlianDocNode deleteNode(NodeDeleteReq req);

    /**
     * 移动节点
     *
     * @param req
     * @return
     */
    SanlianDocNode moveNode(SanlianNodeMoveReq req);

    /**
     * 获取子树,管理员视图可看到不可见节点
     *
     * @param req
     * @return
     */
    SanlianDocTreeNode subTreeForMng(SanlianSubTreeReq req);


    /**
     * 获取子节点,管理员视图可看到不可见节点
     *
     * @param req
     * @return
     */
    List<SanlianDocNode> listChildrenForMng(SanlianNodeListReq req);

    /**
     * 分页获取子节点,管理员视图可看到不可见节点
     *
     * @param req
     * @return
     */
    Pagination<List<SanlianDocNode>> pageChildrenForMng(SanlianNodePageReq req);


    /**
     * 批量获取节点,管理员视图可看到不可见节点
     *
     * @param req
     * @return
     */
    List<SanlianDocNode> batchGetNodeForMng(SanlianNodeBatchGetReq req);

    /**
     * 获取节点,管理员视图可看到不可见节点
     *
     * @param req
     * @return
     */
    SanlianDocNode getNodeForMng(SanlianNodeGetReq req);
}
