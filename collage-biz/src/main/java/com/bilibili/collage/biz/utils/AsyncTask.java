package com.bilibili.collage.biz.utils;

import lombok.Builder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Callable;

@Builder
public class AsyncTask implements Callable<AsyncResult> {
    private static final Logger log = LoggerFactory.getLogger(AsyncTask.class);
    String name;
    AsyncTaskType taskType;
    String message;
    Execution<Object> execution;

    @Override
    public AsyncResult call() throws Exception {
        AsyncResult asyncResult = new AsyncResult();

        try {
            asyncResult.name = name;
            asyncResult.taskType = taskType;
            asyncResult.ret = execution.apply();
        } catch (Exception e) {
            log.error("AsyncResult Exception={}", message);
            asyncResult.ex = e;
        }
        return asyncResult;
    }

    public enum AsyncTaskType {
        /**
         *
         */
        DEFAULT,
        /**
         *
         */
        SUPPORT_ADVERTISING_FLOW,
        SUPPORT_ADVERTISING_ARC;
    }


    @FunctionalInterface
    public interface Execution<R> {
        /**
         * apply
         * @return
         */
        R apply();
    }

}
