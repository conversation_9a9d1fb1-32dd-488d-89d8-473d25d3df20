package com.bilibili.collage.biz.service.school;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.collage.api.dto.CommercialInfoDto;
import com.bilibili.collage.api.dto.SchoolArticleDto;
import com.bilibili.collage.api.service.ISchoolHomepageService;
import com.bilibili.collage.biz.entity.ArticleListQuery;
import com.bilibili.collage.biz.po.CommercialInfoPo;
import com.bilibili.collage.biz.po.CommonQuestionPo;
import com.bilibili.collage.biz.repo.CommercialInfoRepo;
import com.bilibili.collage.biz.repo.CommonQuestionRepo;
import com.bilibili.collage.biz.service.EsSearchService;
import com.bilibili.collage.biz.utils.Es5Utils;
import com.bilibili.collage.biz.utils.EsV2Utils;
import com.bilibili.collage.biz.utils.SearchQueryInfo;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
@Deprecated
public class SchoolHomepageService implements ISchoolHomepageService {
    @Autowired
    private CommercialInfoRepo commercialInfoRepo;
    @Autowired
    private CommonQuestionRepo commonQuestionRepo;
    @Value("${mgk.es.token:sDRF2mzwJ6iyQ3wnl7viGInO5ZkmDegWlNN3ALQkISM}")
    private String esSearchToken;
    @Autowired
    private EsSearchService esSearchService;
    @Resource
    private SchoolConfigService schoolConfigService;

    @Override
    public List<CommercialInfoDto> getCommercialInfoDto() {
        List<CommercialInfoDto> commercialInfoDtoList = new ArrayList<>();
        List<CommercialInfoPo>  commercialInfoPoList = commercialInfoRepo.getCommercialInfoList();
        for(CommercialInfoPo commercialInfoPo : commercialInfoPoList){
            CommercialInfoDto commercialInfoDto = new CommercialInfoDto();
            commercialInfoDto.setId(commercialInfoPo.getId());
            commercialInfoDto.setTitle(commercialInfoPo.getTitle());
            commercialInfoDto.setJumpUrl(commercialInfoPo.getJumpUrl());
            commercialInfoDto.setCtime(Utils.getTimestamp2StringBySecond(new Timestamp(commercialInfoPo.getCtime().getTime())));
            commercialInfoDto.setMtime(Utils.getTimestamp2StringBySecond(new Timestamp(commercialInfoPo.getMtime().getTime())));
            commercialInfoDtoList.add(commercialInfoDto);
        }
        return commercialInfoDtoList;
    }

    @Override
    public List<SchoolArticleDto> getCommonQuestionList() {
        List<CommonQuestionPo> commonQuestionList = commonQuestionRepo.getAllCommonQuestionList();
        SearchQueryInfo searchQueryInfo = null;
        ArticleListQuery articleListQuery = new ArticleListQuery();
        articleListQuery.setIsDeleted((byte) IsDeleted.VALID.getCode());
        articleListQuery.setIsShow((byte)1);
        articleListQuery.setArticleIdList(commonQuestionList.stream().map(CommonQuestionPo::getRelateId).collect(Collectors.toList()));
        articleListQuery.setSortRules(Lists.newArrayList("orderNum asc","ctime desc"));
        searchQueryInfo = EsV2Utils.build(articleListQuery, ArticleListQuery.class, esSearchToken);
        SearchSourceBuilder searchSourceBuilder = Es5Utils.buildSearchSourceBuilderWithPage(searchQueryInfo,0,100);
        PageInfo<SchoolArticleDto> schoolArticleDtoPageInfo = null;
        schoolArticleDtoPageInfo = esSearchService.getEsList(searchSourceBuilder,"mgk-portal", SchoolArticleDto.class);
        if(CollectionUtils.isEmpty(schoolArticleDtoPageInfo.getList())){
            return schoolArticleDtoPageInfo.getList();
        }
        List<SchoolArticleDto> schoolArticleDtoList = new ArrayList<>();
        Map<Long , SchoolArticleDto> schoolArticleDtoMap = schoolArticleDtoPageInfo.getList().stream().collect(Collectors.toMap(v -> v.getArticleId(), v -> v, (v1, v2) -> v1));
        for(CommonQuestionPo commonQuestionPo : commonQuestionList){
            if(schoolArticleDtoMap.containsKey(commonQuestionPo.getRelateId())){
                schoolArticleDtoList.add(schoolArticleDtoMap.get(commonQuestionPo.getRelateId()));
            }
        }
        return schoolArticleDtoList;
    }

//    @Override
//    public List<CommonQuestionDto> getCommonQuestionListForSanlian(Integer size) {
//        return schoolConfigService.getCommonQuestionListForSanlian(size);
//    }
//
//    @Override
//    public List<SanlianLatestUpdateDto> getLatestUpdateListForSanlian() {
//        return schoolConfigService.getLatestUpdateListForSanlian();
//    }

}
