package com.bilibili.collage.biz.service.school.doctree.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/26
 */
@Getter
@RequiredArgsConstructor
public enum SanlianSchoolNodeType {


    manual_dir("产品手册目录"),

    manual_article("产品手册文章"),

    ad_guide_dir("广告指导目录"),

    ad_guide_article("广告指导文章"),


    // link 也可以认为就是目录
    common_question_link("常见问题目录"),

    commercial_info_link("商业资讯"),


    latest_info_link("平台动态，最近更新")



    ;


    private final String desc;


    public static SanlianSchoolNodeType fromName(String value) {
        for (SanlianSchoolNodeType nodeType : SanlianSchoolNodeType.values()) {
            if (nodeType.name().equalsIgnoreCase(value)) {
                return nodeType;
            }
        }
        return null;
    }


    public static SanlianSchoolNodeType fromTreeType(SanlianSchoolTreeType treeType, Boolean hasDoc) {

        switch (treeType) {
            case product_manual: {
                return hasDoc ? manual_article : manual_dir;
            }
            case ad_guidance: {
                return hasDoc ? ad_guide_article : ad_guide_dir;
            }
            case common_question: {
                return common_question_link;
            }
            case commercial_info: {
                return commercial_info_link;
            }
            case latest_info: {
                return latest_info_link;
            }
            default: {
                throw new IllegalArgumentException("unknown treeType: " + treeType);
            }
        }
    }
}
