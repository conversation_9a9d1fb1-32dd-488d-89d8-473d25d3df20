package com.bilibili.collage.biz.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.collage.api.dto.*;
import com.bilibili.collage.api.service.ICollageSizeService;
import com.bilibili.collage.api.service.ICollageWorksService;
import com.bilibili.collage.api.service.IFontLibraryService;
import com.bilibili.collage.api.service.IPatternService;
import com.bilibili.collage.biz.po.MgkCollageWorksPo;
import com.bilibili.mgk.platform.common.CollageCoverTypeEnum;
import com.bilibili.mgk.platform.common.CollagePatternStatusEnum;
import com.bilibili.mgk.platform.common.MgkConstants;
import com.bilibili.mgk.platform.common.collage.CollageSizeStatusEnum;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/11/19
 **/
@Service
@Slf4j
public class CollageValidService {

    @Autowired
    private IPatternService patternService;

    @Autowired
    private IFontLibraryService fontLibraryService;

    @Autowired
    private ICollageSizeService collageSizeService;

    @Autowired
    private CollageWorksServiceDelegate collageWorksServiceDelegate;

    @Autowired
    private ICollageWorksService collageWorksService;

    public void validCreatePattern(PatternDto patternDto) {

        Assert.hasText(patternDto.getName(), "模版命名不能为空");
        Assert.isTrue(patternDto.getName().length() < 32, "模版命名长度不能超过32个字");
        Assert.notNull(patternDto.getCollageSizeId(), "模版尺寸不能为空");
        Assert.notEmpty(patternDto.getIndustryIds(), "模版行业不能为空");

        List<PatternDto> resultList = patternService.getPatternByName(patternDto.getName());
        if (!CollectionUtils.isEmpty(resultList)) {
            throw new IllegalArgumentException("此模版名称[" + patternDto.getName() + "]已存在，无法创建");
        }
    }

    public void validEditLayer(PatternDto patternDto) {
        Assert.notNull(patternDto.getId(), "模版id不能为空");
        patternService.getPatternBaseInfoById(patternDto.getId());
    }

    public void validEditBaseInfoPattern(PatternDto patternDto) {
        Assert.notNull(patternDto.getId(), "模版id不能为空");
        patternService.getPatternBaseInfoById(patternDto.getId());
        List<PatternDto> resultList = patternService.getPatternByName(patternDto.getName());
        resultList = resultList.stream()
                .filter(item -> item.getName().equals(patternDto.getName()))
                .filter(item -> !item.getId().equals(patternDto.getId()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(resultList)) {
            throw new IllegalArgumentException("此模版名称[" + patternDto.getName() + "]已存在，无法创建");
        }
    }

    public void validUpdatePatternStatus(Integer patternId, Integer status) {
        Assert.notNull(patternId, "模版id不能为空");
        PatternDto patternDto = patternService.getPatternBaseInfoById(patternId);
        if (patternDto.getIsDeleted().equals(IsDeleted.DELETED.getCode())) {
            throw new IllegalArgumentException("此模版已无效，无法更新状态");
        }
        CollagePatternStatusEnum toStatus = CollagePatternStatusEnum.getByCode(status);
        CollagePatternStatusEnum curStatus = CollagePatternStatusEnum.getByCode(patternDto.getStatus());
        if (!curStatus.validateStatus(toStatus)) {
            throw new IllegalArgumentException("当前模版状态为: [" + curStatus.getDesc() + "], 无法更新为: [" + toStatus.getDesc() + "]");
        }
    }

    public void validFontLibrary(CollageFontLibraryDto fontLibraryDto) {
        String fontName = fontLibraryDto.getName();
        Assert.hasText(fontName, "字体库名称不能为空");
        Assert.isTrue(fontName.length() <= 32, "字体库名称长度小于32");
        Assert.hasText(fontLibraryDto.getUrl(), "字体库url不能为空");
        Assert.notNull(fontLibraryDto.getEdition(), "字体库版本不可为空");
        if (!CollectionUtils.isEmpty(fontLibraryService.getFontLibraryByName(fontName))) {
            throw new IllegalArgumentException("此字体库名称[" + fontName + "]已存在，无法创建");
        }
    }

    public void validCeateCollageSize(CollageSizeDto collageSizeDto) {

        this.validCollageSize(collageSizeDto);
    }

    public void validUpdateCollageSize(CollageSizeDto collageSizeDto) {

        Assert.notNull(collageSizeDto.getId(), "尺寸id不能为空");
        this.validCollageSize(collageSizeDto);
    }

    private void validCollageSize(CollageSizeDto collageSizeDto) {
        Assert.notNull(collageSizeDto.getWidth(), "尺寸宽度不能为空");
        Assert.notNull(collageSizeDto.getHeight(), "尺寸高度不能为空");
        Assert.notNull(collageSizeDto.getSketch(), "尺寸示意图不能为空");
    }

    public void validUpdateSizeStatus(Integer sizeId, Integer status) {

        Assert.notNull(sizeId, "尺寸id不能为空");
        CollageSizeDto collageSizeDto = collageSizeService.getCollageSizeById(sizeId);
        if (collageSizeDto.getIsDeleted().equals(IsDeleted.DELETED.getCode())) {
            throw new IllegalArgumentException("此模版已无效，无法更新状态");
        }
        CollageSizeStatusEnum toStatus = CollageSizeStatusEnum.getByCode(status);
        CollageSizeStatusEnum curStatus = CollageSizeStatusEnum.getByCode(collageSizeDto.getStatus());
        if (!curStatus.validateStatus(toStatus)) {
            throw new IllegalArgumentException("当前尺寸状态为: [" + curStatus.getDesc() + "], 无法更新为: [" + toStatus.getDesc() + "]");
        }
    }

    public void validCreateWorks(NewCollageWorksDto worksDto) {
        Assert.hasText(worksDto.getName(), "作品名称不可为空");
        Assert.isTrue(worksDto.getName().length() <= 32, "作品名称长度不可超过32");
        Assert.notNull(worksDto.getCollageSizeId(), "作品尺寸不可为空");
        Assert.notEmpty(worksDto.getLayerDtos(), "图层不可为空");
    }

    public void validCreateWorksNoLayers(NewCollageWorksDto dto) {
        Assert.hasText(dto.getName(), "作品名称不可为空");
        Assert.isTrue(dto.getName().length() <= 32, "作品名称长度不可超过32");
        Assert.notNull(dto.getCollageSizeId(), "作品尺寸不可为空");
        Assert.notNull(dto.getWorksMd5(), "作品md5不可为空");
        Assert.notNull(dto.getWorksSize(), "作品大小不可为空");
        Assert.notNull(dto.getWorksUrl(), "作品地址不可为空");
        Assert.notNull(dto.getCover(), "封面不可为空");
    }

    public void validEditWorksLayer(Operator operator, CollageEditWorksLayerDto worksDto) {
        Assert.notNull(worksDto.getId(), "作品Id不可为空");
        Assert.isTrue(Utils.isPositive(worksDto.getId()), "作品Id不合法");
        List<MgkCollageWorksPo> pos = collageWorksServiceDelegate.getWorksPoById(Lists.newArrayList(worksDto.getId()));
        Assert.isTrue(!CollectionUtils.isEmpty(pos), "作品不存在");
        Assert.isTrue(operator.getOperatorId().equals(pos.get(0).getAccountId()), "您不能操作不属于您的作品");
    }

    public void validEditWorkName(Operator operator, Integer id, String name) {
        Assert.isTrue(!Strings.isNullOrEmpty(name), "名称不能为空");
        Assert.isTrue(name.length() <= MgkConstants.COLLAGE_WORKS_NAME_LENGTH, "名称长度不能超过32");
        validEditWork(operator, id);
    }

    public void validEditWork(Operator operator, Integer id){
        Assert.isTrue(Utils.isPositive(id), "作品Id不合法");
        List<MgkCollageWorksPo> pos = collageWorksServiceDelegate.getWorksPoById(Lists.newArrayList(id));
        Assert.isTrue(!CollectionUtils.isEmpty(pos), "作品不存在");
        Assert.isTrue(operator.getOperatorId().equals(pos.get(0).getAccountId()), "您不能操作不属于您的作品");
    }

    public void validMediaDtos(Operator operator, List<CollageMediaDto> collageMediaDtos) {
        Assert.notNull(operator, "用户不可为空");
        Assert.notNull(collageMediaDtos, "媒体不可为空");
        Assert.isTrue(!CollectionUtils.isEmpty(collageMediaDtos), "媒体数量不可为空");
        collageMediaDtos.forEach(dto -> {
            Assert.isTrue(Utils.isPositive(dto.getHeight()), "媒体高度不合法");
            Assert.isTrue(Utils.isPositive(dto.getWidth()), "媒体宽度不合法");
            Assert.isTrue(!Strings.isNullOrEmpty(dto.getMediaName()), "名称不可为空");
            Assert.isTrue(dto.getMediaName().length() <= MgkConstants.COLLAGE_MEDIA_LENGTH, String.format("图片[%s]长度超过限制%d", dto.getMediaName(), 100));
        });
    }

    public void validCreateCover(CollageCoverDto dto) {
        CollageCoverTypeEnum.getByCode(dto.getObjType()); // 验证类型
        Assert.notNull(dto.getObjId(), "封面所属对象不可为空");
        Assert.notNull(dto.getWidth(), "封面宽度不可为空");
        Assert.notNull(dto.getHeight(), "封面高度不可为空");
        Assert.notNull(dto.getRatio(), "封面比率不可为空");
        Assert.notNull(dto.getCoverSize(), "封面大小不可为空");
        Assert.notNull(dto.getCoverMd5(), "封面Md5不可为空");
        Assert.notNull(dto.getCoverUrl(), "封面地址不可为空");
    }

    public void validEditWorks(Operator operator, CollageEditWorksNoLayersDto dto) {
        Assert.notNull(operator, "用户不存在");
        Assert.notNull(dto.getRenderImage(), "渲染生成的图片不可为空");
        Assert.notNull(dto.getWorksMd5(), "作品md5不可为空");
        Assert.notNull(dto.getWorksSize(), "作品尺寸不可为空");
        Assert.notNull(dto.getCover(), "封面不可为空");
        Assert.notNull(dto.getCover().getHeight(), "封面高度不可为空");
        Assert.notNull(dto.getCover().getWidth(), "封面宽度不可为空");
        Assert.notNull(dto.getCover().getRatio(), "封面比率不可为空");
        Assert.notNull(dto.getCover().getCoverSize(), "封面大小不可为空");
        Assert.notNull(dto.getCover().getCoverMd5(), "封面md5不可为空");
        Assert.notNull(dto.getCover().getCoverUrl(), "封面地址不可为空");

        List<MgkCollageWorksPo> worksPos = collageWorksServiceDelegate.getWorksPoById(Lists.newArrayList(dto.getId()));
        Assert.isTrue(!CollectionUtils.isEmpty(worksPos), "作品不存在");
        Assert.isTrue(operator.getOperatorId().equals(worksPos.get(0).getAccountId()), "您不能编辑不属于您的的作品");
    }

    public void validQueryEnterpriseDto(QueryCollageEnterpriseDto queryDto) {
        Assert.notNull(queryDto.getMid(), "企业号mid不可为空");
        Assert.notNull(queryDto.getPageInfo(), "分页参数不可为空");
        Assert.isTrue(queryDto.getMinRatio() <= queryDto.getMaxRatio(), "查询比例范围不正确");
    }
}
