package com.bilibili.collage.biz.service;

import com.bapis.archive.service.Arc;
import com.bapis.archive.service.ArcsReply;
import com.bapis.archive.service.ArcsRequest;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.AdvertisingMode;
import com.bilibili.adp.passport.api.dto.ArchiveVideoInfoDetailDto;
import com.bilibili.adp.passport.api.service.IPassportService;
import com.bilibili.adp.passport.biz.common.ArchiveState;
import com.bilibili.adp.passport.biz.common.GrpcManager;
import com.bilibili.bvid.BVIDUtils;
import com.bilibili.collage.api.dto.CollageEnterpriseVideoDto;
import com.bilibili.collage.api.dto.CollageEnterpriseVideoQueryDto;
import com.bilibili.collage.api.service.ICommercialOrderService;
import com.bilibili.collage.biz.service.model.CmcGoodsBo;
import com.bilibili.commercialorder.soa.adauth.pojo.SoaAdAuthInfoDto;
import com.bilibili.commercialorder.soa.adauth.pojo.SoaGetPageAdAuthReqDto;
import com.bilibili.commercialorder.soa.adauth.service.ISoaAdAuth4AdpService;
import com.bilibili.sycpb.acc.api.dict.common.YesNoEnum;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
@Slf4j
public class CommercialOrderServiceImpl implements ICommercialOrderService {

    @Value("${commercial.videl.size.limit:1000}")
    private Integer commercialVideoSizeLimit;

    @Autowired
    private ISoaAdAuth4AdpService soaAdAuth4AdpService;
    @Autowired
    private IPassportService passportService;
    @Autowired
    private GrpcManager grpcManager;
    @Autowired
    private GoodsArchiveQuerier goodsArchiveQuerier;

    private static final List<Integer> VALID_VIDEO_STATES = Arrays.asList(
            ArchiveState.OPEN_BROWSE.getCode(),
            ArchiveState.ORANGE_THROUGH.getCode(),
            ArchiveState.REPAIR_PENDING.getCode(),
            ArchiveState.VIP_ACCESS.getCode());


    @Override
    public PageResult<CollageEnterpriseVideoDto> getCommercialOrderVideos(CollageEnterpriseVideoQueryDto queryDto) {

        if (queryDto.fetchAllAndFilterByBiz()) {
            return doPageAfterFetchAll(queryDto, queryDto.getPage(), queryDto.getPageSize());
        } else {
            return doPage(queryDto, queryDto.getPage(), queryDto.getPageSize());
        }
    }


    private PageResult<CollageEnterpriseVideoDto> doPage(CollageEnterpriseVideoQueryDto queryDto, Integer page,
            Integer pageSize) {

        Integer accountId = queryDto.getAccountId();

        SoaGetPageAdAuthReqDto reqDto = new SoaGetPageAdAuthReqDto();
        reqDto.setAdvertisersAccountId(accountId);
        reqDto.setState(1);
        reqDto.setStateExt(4);
        if (AdvertisingMode.nativeContentMode(queryDto.getAdvertisingMode())) {
            //内容投放只支持 mode = 1
            //授权模式 1-原视频+UP主空间头像 2-原视频
            reqDto.setMode(1);
        }

        reqDto.setPage(page);
        reqDto.setSize(pageSize);

        PageResult<SoaAdAuthInfoDto> pageResult = soaAdAuth4AdpService.getPageAdAuth(reqDto);
        if (pageResult == null || pageResult.getTotal() == 0) {
            return PageResult.EMPTY_PAGE_RESULT;
        }

        List<Long> avidList = pageResult.getRecords()
                .stream()
                .map(SoaAdAuthInfoDto::getAvid)
                .filter(x -> x.compareTo(0L) > 0)
                .collect(Collectors.toList());
        ArcsReply arcsReply = grpcManager.getArcsReply(
                ArcsRequest.newBuilder().addAllAids(avidList).build());
        Map<Long, Arc> arcMap = ((arcsReply == null) || (arcsReply.getArcsCount() == 0))
                ? Collections.emptyMap()
                : arcsReply.getArcsMap();
        List<ArchiveVideoInfoDetailDto> archiveVideoInfoDetails = passportService.getAllArchiveInfoByAids(avidList);
        Map<Long, ArchiveVideoInfoDetailDto> videoDetailMap =
                CollectionUtils.isEmpty(archiveVideoInfoDetails) ? Collections.emptyMap()
                        : archiveVideoInfoDetails.stream()
                                .collect(Collectors.toMap(ArchiveVideoInfoDetailDto::getAid, Function.identity()));

        List<CollageEnterpriseVideoDto> records = pageResult.getRecords()
                .stream()
                .filter(soaAdAuthInfoDto -> soaAdAuthInfoDto.getAvid().compareTo(0L) > 0)
                .filter(soaAdAuthInfoDto -> Objects.nonNull(arcMap.get(soaAdAuthInfoDto.getAvid())))
                .map(adAuthInfoDto -> this.getCollageEnterpriseVideoDtoByArc(adAuthInfoDto,
                        arcMap.get(adAuthInfoDto.getAvid()), videoDetailMap.get(adAuthInfoDto.getAvid())))
                .collect(Collectors.toList());

        if ("pubTime desc".equals(queryDto.getOrderBy()) || "ctime desc".equals(queryDto.getOrderBy())) {
            records = records.stream()
                    .sorted(Comparator.comparingLong(CollageEnterpriseVideoDto::getPubTime).reversed())
                    .collect(Collectors.toList());
        } else if ("pubTime asc".equals(queryDto.getOrderBy()) || "ctime asc".equals(queryDto.getOrderBy())) {
            records = records.stream().sorted(Comparator.comparingLong(CollageEnterpriseVideoDto::getPubTime))
                    .collect(Collectors.toList());
        }

        // 带货稿件标记
        List<Long> finalAvids = records.stream().map(t -> t.getAid()).collect(Collectors.toList());
        Map<Long, CmcGoodsBo> arcGoodsMap = goodsArchiveQuerier.queryArchiveGoods(finalAvids);
        records.forEach(archiveBo -> {
            CmcGoodsBo goodsBo = arcGoodsMap.getOrDefault(archiveBo.getAid(), CmcGoodsBo.builder().build());
            archiveBo.setIsGoodsArchive(goodsBo.isGoodsArchive() ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
            archiveBo.setIsSupportCommentClick(goodsBo.isSupportCommentClick() ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
            archiveBo.setIsSupportYellowCarOrCallUp(goodsBo.isGoodsArchive() ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
        });

        int total = pageResult.getTotal();
        // 带货稿件过滤

        records.forEach(record -> record.setBvid(BVIDUtils.avToBv(record.getAid())));

        return PageResult.<CollageEnterpriseVideoDto>builder()
                .total(total)
                .records(records)
                .build();

    }


    private PageResult<CollageEnterpriseVideoDto> doPageAfterFetchAll(CollageEnterpriseVideoQueryDto reqDto,
            Integer page, Integer pageSize) {

        PageResult<CollageEnterpriseVideoDto> records = this.doPage(reqDto, 1, commercialVideoSizeLimit);

        List<CollageEnterpriseVideoDto> data = records.getRecords().stream()
                .filter(reqDto::filterByBiz)
                .collect(Collectors.toList());

        int total = data.size();
        // 内存分页
        data = data.stream()
                // 内存分页
                .skip((long) pageSize * (page - 1))
                .limit(pageSize)
                .collect(Collectors.toList());

        return PageResult.<CollageEnterpriseVideoDto>builder()
                .total(total)
                .records(data)
                .build();


    }


    private CollageEnterpriseVideoDto getCollageEnterpriseVideoDtoByArc(SoaAdAuthInfoDto adAuthInfoDto, Arc arc,
            ArchiveVideoInfoDetailDto detailDto) {
        //稿件状态正常 && 非限流状态 = 支持投放
        boolean isSupportAdvertising = VALID_VIDEO_STATES.contains(arc.getState());
        CollageEnterpriseVideoDto videoDto = CollageEnterpriseVideoDto.builder()
                .aid(adAuthInfoDto.getAvid())
                .isSupportAdvertising(isSupportAdvertising)
                .build();

        videoDto.setCover(StringUtils.isEmpty(arc.getPic()) ? "" : arc.getPic().replace("http", "https"));
        videoDto.setCtime(arc.getCtime());
        videoDto.setPubTime(arc.getPubDate());
        videoDto.setTitle(arc.getTitle());
        videoDto.setDesc(arc.getDesc());
        videoDto.setTags(String.join(",", arc.getTagsList()));
        if (detailDto != null) {
            videoDto.setCid(detailDto.getCid());
            videoDto.setHeight(detailDto.getHeight());
            videoDto.setWidth(detailDto.getWidth());
            videoDto.setDuration(detailDto.getDuration());
        }
        return videoDto;
    }
}
