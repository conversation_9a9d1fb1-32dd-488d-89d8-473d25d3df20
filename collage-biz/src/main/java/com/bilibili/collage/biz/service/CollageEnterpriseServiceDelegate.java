package com.bilibili.collage.biz.service;

import com.bapis.archive.service.Arc;
import com.bapis.archive.service.ArcRequest;
import com.bapis.archive.service.ArchiveGrpc;
import com.bapis.archive.service.ArcsRequest;
import com.bapis.videoup.open.service.UpArcsSearchReply;
import com.bapis.videoup.open.service.UpArcsSearchReq;
import com.bapis.videoup.open.service.VideoUpOpenGrpc;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.CollectionHelper;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.launch.api.account.dto.AccountCorpInfoVo;
import com.bilibili.adp.launch.api.soa.ISoaAdpCpcAccountService;
import com.bilibili.adp.passport.api.dto.ArchiveVideoDescDto;
import com.bilibili.adp.passport.api.dto.ArchiveVideoInfoDetailDto;
import com.bilibili.adp.passport.api.dto.ArchiveVideoInfoDto;
import com.bilibili.adp.passport.biz.common.ArchiveState;
import com.bilibili.collage.api.dto.CollageEnterpriseDto;
import com.bilibili.collage.api.dto.CollageEnterpriseVideoDto;
import com.bilibili.collage.api.dto.QueryCollageEnterpriseDto;
import com.bilibili.collage.biz.service.model.CmcGoodsBo;
import com.bilibili.mgk.platform.common.utils.NumberUtils;
import com.bilibili.mgk.platform.common.video_library.SizeTypeEnum;
import com.bilibili.sycpb.acc.api.dict.common.YesNoEnum;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import io.grpc.Channel;
import io.grpc.StatusRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.component.ecode.ServerCode;
import pleiades.component.rpc.core.StatusCode;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * @file: CollageEnterpriseServiceDelegate
 * @author: gaoming
 * @date: 2020/12/09
 * @version: 1.0
 * @description:
 **/

@Service
@Slf4j
public class CollageEnterpriseServiceDelegate {

    @Value("${mgk.bilibili.archive.enterprise.page.max_size:10}")
    private Integer videoUpPageLimit;

    public static final String ARCHIVE_CHANNEL = "archiveChannel";

    public static final String VIDEO_UP_OPEN_CHANNEL = "videoUpChannel";
    public static final Integer VIDEO_UP_GRPC_PAGE_SIZE = 20;

    /**
     * 稿件状态: 开放浏览，橙色通过
     */
    public static final List<Integer> VIDEO_UP_SEARCH_STATE_LIST = Lists.newArrayList(
            ArchiveState.OPEN_BROWSE.getCode(),
            ArchiveState.ORANGE_THROUGH.getCode());

    @Autowired
    private ISoaAdpCpcAccountService soaAdpCpcAccountService;

    @Resource(name = ARCHIVE_CHANNEL)
    private Channel archiveChannel;

    @Resource(name = VIDEO_UP_OPEN_CHANNEL)
    private Channel videoUpChannel;

    @Autowired
    private GoodsArchiveQuerier goodsArchiveQuerier;

    /**
     * 获取稿件列表
     *
     * @param queryDto
     * @return
     */
    public PageResult<CollageEnterpriseVideoDto> getArchiveVideos(QueryCollageEnterpriseDto queryDto) {

        List<CollageEnterpriseVideoDto> records;

        // 根据 avid 获取
        if (Utils.isPositive(queryDto.getAvid())) {
            records = getRecordsByAvid(queryDto.getAvid(), queryDto.getMid());
        }
        // 根据 keyword 获取
        else if (!Strings.isNullOrEmpty(queryDto.getKeyword())) {
            records = getRecordsByKeyword(queryDto.getKeyword(), queryDto.getMid());
        }
        // 否则，获取 mid 下的稿件列表
        else {
            records = getRecordsByMid(queryDto.getMid());
        }

        // 过滤
        List<CollageEnterpriseVideoDto> collageEnterpriseVideoDtos = filterConditions(records, queryDto);

        // 从主站videoUpChannel全量获取符合条件的所有视频，然后按前端要求进行排序
        Stream<CollageEnterpriseVideoDto> stream = collageEnterpriseVideoDtos.stream();
        if("pubTime desc".equals(queryDto.getOrderBy()) || "ctime desc".equals(queryDto.getOrderBy())){
            stream =  stream.sorted(Comparator.comparingLong(CollageEnterpriseVideoDto::getPubTime).reversed());
        }else if("pubTime asc".equals(queryDto.getOrderBy()) || "ctime asc".equals(queryDto.getOrderBy())){
            stream =  stream.sorted(Comparator.comparingLong(CollageEnterpriseVideoDto::getPubTime));
        }

        List<CollageEnterpriseVideoDto> res = stream
                // 内存分页
                .skip((long) queryDto.getPageInfo().getPageSize() * (queryDto.getPageInfo().getPage() - 1))
                .limit(queryDto.getPageInfo().getPageSize())
                .collect(Collectors.toList());

        return PageResult.<CollageEnterpriseVideoDto>builder()
                .total(collageEnterpriseVideoDtos.size())
                .records(res)
                .build();
    }

    private List<CollageEnterpriseVideoDto> getRecordsByAvid(Long avid, Long mid) {
        return getRecordsByAvidFromGrpc(avid, mid);
    }

    /**
     * 根据 mid 和关键词查询稿件列表
     *
     * @param keyword
     * @param mid
     * @return
     */
    private List<CollageEnterpriseVideoDto> getRecordsByKeyword(String keyword, Long mid) {

        List<com.bapis.videoup.open.service.Arc> arcList = getArchiveByMidFromVideoUpOpenGrpc(keyword, mid);
        if (CollectionUtils.isEmpty(arcList)) {
            return Collections.emptyList();
        }
        return convertArcs2VideoDtos(arcList);
    }

    /**
     * 获取 mid 下的稿件列表
     *
     * @param mid
     * @return
     */
    private List<CollageEnterpriseVideoDto> getRecordsByMid(Long mid) {
        List<com.bapis.videoup.open.service.Arc> arcList = getArchiveByMidFromVideoUpOpenGrpc("", mid);
        if (CollectionUtils.isEmpty(arcList)) {
            return Collections.emptyList();
        }
        return convertArcs2VideoDtos(arcList);
    }

    /**
     * 根据 mid, avid 查询稿件列表
     *
     * @param avid
     * @param mid
     * @return
     */
    private List<CollageEnterpriseVideoDto> getRecordsByAvidFromGrpc(Long avid, Long mid) {
        Arc arcByAvid = getArcByAvidFromArchiveGrpc(avid, mid);
        if (Objects.isNull(arcByAvid)) {
            return Collections.emptyList();
        }
        List<com.bapis.videoup.open.service.Arc> archiveInfoList = getArchiveByMidFromVideoUpOpenGrpc(arcByAvid.getTitle(), mid);
        List<com.bapis.videoup.open.service.Arc> arcByTitleList = archiveInfoList.stream()
                .filter(archiveInfo -> archiveInfo.getAid() == avid)
                .collect(Collectors.toList());
        com.bapis.videoup.open.service.Arc arcByTitle = null;
        if (!CollectionUtils.isEmpty(arcByTitleList)) {
            arcByTitle = arcByTitleList.get(0);
        }
        CollageEnterpriseVideoDto result = CollageEnterpriseVideoDto.builder()
                .aid(arcByAvid.getAid())
                .cid(arcByAvid.getFirstCid())
                .copyright(arcByAvid.getCopyright())
                .cover(arcByAvid.getPic())
                .ctime(arcByAvid.getCtime())
                .height(arcByAvid.getDimension().getHeight())
                .width(arcByAvid.getDimension().getWidth())
                .pubTime(arcByAvid.getPubDate())
                .rotate(arcByAvid.getDimension().getRotate())
                .state(arcByAvid.getState())
//                .status(arcByAvid.getState())
                .tags(arcByTitle == null ? null : arcByTitle.getTags())
                .tid(arcByAvid.getTypeID())
                .title(arcByAvid.getTitle())
                .duration(arcByAvid.getDuration())
                .desc(arcByAvid.getDesc())
                .nickname(Optional.of(arcByAvid.getAuthor()).map(o->o.getName()).orElse(""))
                .face(Optional.of(arcByAvid.getAuthor()).map(o->o.getFace()).orElse(""))
                .build();
        return Lists.newArrayList(result);
    }

    /**
     * 根据 mid, avid 查询稿件列表
     *
     * @param avid
     * @param mid
     * @return
     */
    private Arc getArcByAvidFromArchiveGrpc(Long avid, Long mid) {
        if (!Utils.isPositive(avid)) {
            return null;
        }
        try {
            ArchiveGrpc.ArchiveBlockingStub archiveBlockingStub = ArchiveGrpc.newBlockingStub(archiveChannel);
            Arc arc = archiveBlockingStub
                    .withDeadlineAfter(2000L, TimeUnit.MILLISECONDS)
                    .withWaitForReady()
                    .arc(ArcRequest.newBuilder()
                            .setAid(avid)
                            .build()).getArc();
            if (mid == arc.getAuthor().getMid()) {
                return arc;
            }
            return null;
        } catch (Exception e) {
            log.warn("grpc failed :{}", e.getMessage());
            if(e instanceof StatusRuntimeException) {
                StatusRuntimeException exception = (StatusRuntimeException)e;
                ServerCode code = StatusCode.toServerCode(exception.getStatus(), exception.getTrailers());
                log.error("grpc archiveService error! code={}, msg={}", code.getCode(), code.getMessage());
            }
            return null;
        }
    }

    private Map<Long, com.bapis.archive.service.Arc> getArcMapByAvidFromArchiveGrpc(List<Long> avidList) {
        if (CollectionUtils.isEmpty(avidList)) {
            return Collections.emptyMap();
        }
        try {
            ArchiveGrpc.ArchiveBlockingStub archiveBlockingStub = ArchiveGrpc.newBlockingStub(archiveChannel);
            return archiveBlockingStub
                    .withDeadlineAfter(2000L, TimeUnit.MILLISECONDS)
                    .withWaitForReady()
                    .arcs(ArcsRequest.newBuilder()
                            .addAllAids(avidList)
                            .build()).getArcsMap();
        } catch (Exception e) {
            log.warn("grpc failed :{}", e.getMessage());
            if(e instanceof StatusRuntimeException) {
                StatusRuntimeException exception = (StatusRuntimeException)e;
                ServerCode code = StatusCode.toServerCode(exception.getStatus(), exception.getTrailers());
                log.error("grpc archiveService error! code={}, msg={}", code.getCode(), code.getMessage());
            }
            return Collections.emptyMap();
        }
    }

    /**
     * 根据 mid, 关键词搜索稿件
     *
     * @param keyword
     * @param mid
     * @return
     */
    private List<com.bapis.videoup.open.service.Arc> getArchiveByMidFromVideoUpOpenGrpc(String keyword, Long mid) {
        // 获取主站 mid 下的稿件列表
        UpArcsSearchReply countReply = getUpArcsSearchReplyFromGrpc(keyword, mid, 1);

        if (Objects.isNull(countReply)) {
            return Collections.emptyList();
        }
        if (countReply.getTotal() <= VIDEO_UP_GRPC_PAGE_SIZE) {
            return countReply.getArcsList();
        }

        List<com.bapis.videoup.open.service.Arc> result = new ArrayList<>();
        Long basePage = countReply.getTotal() / VIDEO_UP_GRPC_PAGE_SIZE.longValue();
        Integer totalPage = countReply.getTotal() % VIDEO_UP_GRPC_PAGE_SIZE.longValue() == 0 ? basePage.intValue() : basePage.intValue() + 1;

        // 最多翻5页 100个
        IntStream.range(1, Math.min(totalPage, videoUpPageLimit) + 1).forEach(pageNum -> {
            //
            UpArcsSearchReply replyByKeyword = getUpArcsSearchReplyFromGrpc(keyword, mid, pageNum);
            if (Objects.nonNull(replyByKeyword) && !CollectionUtils.isEmpty(replyByKeyword.getArcsList())) {
                result.addAll(replyByKeyword.getArcsList());
            }
        });
        return result;
    }

    /**
     * 查询 mid 下的开放浏览，橙色通过状态的稿件列表
     *
     * @param keyword
     * @param mid
     * @param page
     * @return
     */
    private UpArcsSearchReply getUpArcsSearchReplyFromGrpc(String keyword, Long mid, Integer page) {
        try {
            // 查询 mid 下的开放浏览，橙色通过状态的稿件列表
            VideoUpOpenGrpc.VideoUpOpenBlockingStub videoUpOpenBlockingStub = VideoUpOpenGrpc.newBlockingStub(videoUpChannel);
            UpArcsSearchReq.Builder searchReqBuilder = UpArcsSearchReq.newBuilder()
                    .setMid(mid)
                    // 稿件状态: 开放浏览，橙色通过
                    .addAllStates(VIDEO_UP_SEARCH_STATE_LIST)
                    .setPn(page)
                    .setPs(VIDEO_UP_GRPC_PAGE_SIZE);
            if (!StringUtils.isEmpty(keyword)) {
                searchReqBuilder.setKeyword(keyword);
            }
            UpArcsSearchReply reply = videoUpOpenBlockingStub
                    .withDeadlineAfter(2000L, TimeUnit.MILLISECONDS)
                    .withWaitForReady()
                    .upArcsSearch(searchReqBuilder.build());
            return reply;

        } catch (Exception e) {
            log.warn("grpc failed :{}", e.getMessage());
            if(e instanceof StatusRuntimeException) {
                StatusRuntimeException exception = (StatusRuntimeException)e;
                ServerCode code = StatusCode.toServerCode(exception.getStatus(), exception.getTrailers());
                log.error("grpc upArchiveService error! code={}, msg={}", code.getCode(), code.getMessage());
            }
            return UpArcsSearchReply.getDefaultInstance();
        }
    }

    private List<CollageEnterpriseVideoDto> convertArcs2VideoDtos(List<com.bapis.videoup.open.service.Arc> arcs) {
        List<CollageEnterpriseVideoDto> result = new ArrayList<>();

        // 分批处理
        CollectionHelper.processInBatches(arcs, 100, subArcList -> {
            // 获取一批的 avids
            List<Long> avids = subArcList.stream().map(com.bapis.videoup.open.service.Arc::getAid).collect(Collectors.toList());
            // 根据 avids 获取稿件 map
            Map<Long, Arc> arcMap = getArcMapByAvidFromArchiveGrpc(avids);

            // 再次过滤稿件状态条件&高度不为0
            List<CollageEnterpriseVideoDto> batchResult = subArcList.stream()
                    .filter(arc -> VIDEO_UP_SEARCH_STATE_LIST.contains(arc.getState()))
                    .map(arc -> convertArc2VideoDto(arc, arcMap.getOrDefault(arc.getAid(), Arc.newBuilder().build())))
                    .filter(resultArc -> Utils.isPositive(resultArc.getHeight()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(batchResult)) {
                result.addAll(batchResult);
            }
        });
        return result;
    }

    private CollageEnterpriseVideoDto convertArc2VideoDto(com.bapis.videoup.open.service.Arc arcByTitle, Arc arcByAvid) {
        return CollageEnterpriseVideoDto.builder()
                .aid(arcByTitle.getAid())
                .cid(arcByAvid.getFirstCid())
                .copyright(arcByTitle.getCopyRight())
                .cover(arcByTitle.getCover())
                .ctime(arcByTitle.getCtime())
                .height(arcByAvid.getDimension().getHeight())
//                    .fileName(detailDto.getFileName())
//                    .idx(detailDto.getIdx())
                .pubTime(arcByTitle.getPubtime())
                .rotate(arcByAvid.getDimension().getRotate())
                .state(arcByTitle.getState())
                .tags(arcByTitle.getTags())
                .tid(arcByTitle.getTypeID())
                .title(arcByTitle.getTitle())
                .width(arcByAvid.getDimension().getWidth())
                .duration(arcByAvid.getDuration())
                .desc(arcByAvid.getDesc())
                .nickname(Optional.of(arcByAvid.getAuthor()).map(o->o.getName()).orElse(""))
                .face(Optional.of(arcByAvid.getAuthor()).map(o->o.getFace()).orElse(""))
                .build();
    }

    private List<CollageEnterpriseVideoDto> filterConditions(List<CollageEnterpriseVideoDto> records, QueryCollageEnterpriseDto queryDto) {

        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }

        // 名称过滤
        if (!Strings.isNullOrEmpty(queryDto.getName())) {
            records = records.stream().filter(collageEnterpriseVideoDto -> collageEnterpriseVideoDto.getTitle().contains(queryDto.getName())).collect(Collectors.toList());
        }

        // 稿件大小类型过滤
        if (queryDto.getSizeType() != null) {
            // 其他
            if (queryDto.getSizeType() == 0) {
                records = records.stream()
                        .filter(collageEnterpriseVideoDto -> !SizeTypeEnum.S_16_9.isMatch(collageEnterpriseVideoDto.getWidth().intValue(), collageEnterpriseVideoDto.getHeight().intValue()))
                        .filter(collageEnterpriseVideoDto -> !SizeTypeEnum.S_9_16.isMatch(collageEnterpriseVideoDto.getWidth().intValue(), collageEnterpriseVideoDto.getHeight().intValue()))
                        .filter(collageEnterpriseVideoDto -> !SizeTypeEnum.S_3_4.isMatch(collageEnterpriseVideoDto.getWidth().intValue(), collageEnterpriseVideoDto.getHeight().intValue()))
                        .filter(collageEnterpriseVideoDto -> !SizeTypeEnum.S_4_3.isMatch(collageEnterpriseVideoDto.getWidth().intValue(), collageEnterpriseVideoDto.getHeight().intValue()))
                        .collect(Collectors.toList());
            }
            // 非其他
            else {
                SizeTypeEnum sizeType = SizeTypeEnum.getByCode(queryDto.getSizeType());
                records = records.stream().filter(collageEnterpriseVideoDto -> sizeType.isMatch(collageEnterpriseVideoDto.getWidth().intValue(), collageEnterpriseVideoDto.getHeight().intValue())).collect(Collectors.toList());
            }
        }

        // 稿件 ratioWidth 过滤
        if (NumberUtils.isPositive(queryDto.getRatioHeight()) && NumberUtils.isPositive(queryDto.getRatioWidth())) {
            records = records.stream()
                    .filter(collageEnterpriseVideoDto -> collageEnterpriseVideoDto.getWidth().intValue() * queryDto.getRatioHeight() == collageEnterpriseVideoDto.getHeight().intValue() * queryDto.getRatioWidth())
                    .collect(Collectors.toList());
        }
        // 只能指定的几个尺寸 and ((width=16 and height=9) or (width=9 and height=16) or (width=3 and height=4) or (width=4 and height=3))
        if (NumberUtils.isPositive(queryDto.getPointedSize())) {
            records = records.stream()
                    .filter(collageEnterpriseVideoDto -> SizeTypeEnum.S_16_9.isMatch(collageEnterpriseVideoDto.getWidth().intValue(), collageEnterpriseVideoDto.getHeight().intValue())
                            || SizeTypeEnum.S_9_16.isMatch(collageEnterpriseVideoDto.getWidth().intValue(), collageEnterpriseVideoDto.getHeight().intValue())
                            || SizeTypeEnum.S_3_4.isMatch(collageEnterpriseVideoDto.getWidth().intValue(), collageEnterpriseVideoDto.getHeight().intValue())
                            || SizeTypeEnum.S_4_3.isMatch(collageEnterpriseVideoDto.getWidth().intValue(), collageEnterpriseVideoDto.getHeight().intValue()))
                    .collect(Collectors.toList());
        }

        // 视频时长时间过滤
        if (Utils.isPositive(queryDto.getDurationBegin())) {
            records = records.stream().filter(collageEnterpriseVideoDto -> collageEnterpriseVideoDto.getDuration() >= queryDto.getDurationBegin()).collect(Collectors.toList());
        }
        if (Utils.isPositive(queryDto.getDurationEnd())) {
            records = records.stream().filter(collageEnterpriseVideoDto -> collageEnterpriseVideoDto.getDuration() <= queryDto.getDurationEnd()).collect(Collectors.toList());
        }

        // 宽高比范围筛选
        if (Utils.isPositive(queryDto.getMaxRatio()) && Utils.isPositive(queryDto.getMinRatio())) {
            records = records.stream().filter(dto -> {
                long ratio = dto.getWidth() * 10000 / dto.getHeight();
                return ratio <= queryDto.getMaxRatio() && ratio >= queryDto.getMinRatio();
            }).collect(Collectors.toList());
        }

        // 带货稿件标记
        List<Long> finalAvids = records.stream().map(t -> t.getAid()).collect(Collectors.toList());
        Map<Long, CmcGoodsBo> arcGoodsMap = goodsArchiveQuerier.queryArchiveGoods(finalAvids);
        records.forEach(archiveBo -> {
            CmcGoodsBo goodsBo = arcGoodsMap.getOrDefault(archiveBo.getAid(), CmcGoodsBo.builder().build());
            archiveBo.setIsGoodsArchive(goodsBo.isGoodsArchive() ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
            archiveBo.setIsSupportCommentClick(goodsBo.isSupportCommentClick() ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
            archiveBo.setIsSupportYellowCarOrCallUp(goodsBo.isGoodsArchive() ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
        });

        // 带货稿件过滤
        if (Utils.isPositive(queryDto.getOnlyGoods())) {
            return records
                    .stream()
                    .filter(arc -> Utils.isPositive(arc.getIsGoodsArchive()))
                    .collect(Collectors.toList());
        }
        return records;
    }


    private List<CollageEnterpriseVideoDto> mergeArchiveVideo(List<ArchiveVideoInfoDto> archiveList, Map<Long, ArchiveVideoInfoDetailDto> videoDetailMap, Map<Long, ArchiveVideoDescDto> videoDescMap) {

        if (CollectionUtils.isEmpty(archiveList)) {
            return Collections.emptyList();
        }
        List<CollageEnterpriseVideoDto> res = archiveList.stream().map(dto -> {
            ArchiveVideoInfoDetailDto detailDto = videoDetailMap.getOrDefault(dto.getAid(), ArchiveVideoInfoDetailDto.builder()
                    .duration(0L)
                    .title("")
                    .status(0)
                    .state(ArchiveState.UP_DELETED.getCode())
                    .idx(0)
                    .fileName("")
                    .cid(0L)
                    .aid(0L)
                    .height(0L)
                    .rotate(0L)
                    .width(0L)
                    .build());

            ArchiveVideoDescDto descriptionDto = videoDescMap.getOrDefault(dto.getAid(), ArchiveVideoDescDto.builder()
                    .desc(StringUtils.EMPTY)
                    .build());
            return CollageEnterpriseVideoDto.builder()
                    .aid(dto.getAid())
                    .cid(detailDto.getCid())
                    .copyright(dto.getCopyright())
                    .cover(dto.getCover())
                    .ctime(dto.getCtime())
                    .height(detailDto.getHeight())
                    .pubTime(dto.getPubTime())
                    .rotate(detailDto.getRotate())
                    .state(detailDto.getState())
//                    .status(detailDto.getStatus())
                    .tags(dto.getTags())
                    .tid(dto.getTid())
                    .title(dto.getTitle())
                    .width(detailDto.getWidth())
                    .duration(detailDto.getDuration())
                    .desc(descriptionDto.getDesc())
                    .build();
        }).collect(Collectors.toList());

        // 根据默认状态再过滤一遍 高度不为0
        return res.stream().filter(dto -> !dto.getState().equals(ArchiveState.UP_DELETED.getCode())).filter(dto -> Utils.isPositive(dto.getHeight())).collect(Collectors.toList());
    }

    public List<CollageEnterpriseDto> getEnterpriseByAccount(Operator operator) {
        List<AccountCorpInfoVo> infos = soaAdpCpcAccountService.getBoundCorpInfo(operator.getOperatorId());
        if (CollectionUtils.isEmpty(infos)) {
            return Collections.emptyList();
        }

        return infos.stream().map(this::convertEnterprise2Vo).collect(Collectors.toList());
    }

    private CollageEnterpriseDto convertEnterprise2Vo(AccountCorpInfoVo vo) {
        return CollageEnterpriseDto.builder()
                .mid(vo.getMid())
                .name(vo.getCorpName())
                .build();
    }
}
