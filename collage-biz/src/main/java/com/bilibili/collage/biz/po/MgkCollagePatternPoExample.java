package com.bilibili.collage.biz.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class MgkCollagePatternPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public MgkCollagePatternPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andCollageSizeIdIsNull() {
            addCriterion("collage_size_id is null");
            return (Criteria) this;
        }

        public Criteria andCollageSizeIdIsNotNull() {
            addCriterion("collage_size_id is not null");
            return (Criteria) this;
        }

        public Criteria andCollageSizeIdEqualTo(Integer value) {
            addCriterion("collage_size_id =", value, "collageSizeId");
            return (Criteria) this;
        }

        public Criteria andCollageSizeIdNotEqualTo(Integer value) {
            addCriterion("collage_size_id <>", value, "collageSizeId");
            return (Criteria) this;
        }

        public Criteria andCollageSizeIdGreaterThan(Integer value) {
            addCriterion("collage_size_id >", value, "collageSizeId");
            return (Criteria) this;
        }

        public Criteria andCollageSizeIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("collage_size_id >=", value, "collageSizeId");
            return (Criteria) this;
        }

        public Criteria andCollageSizeIdLessThan(Integer value) {
            addCriterion("collage_size_id <", value, "collageSizeId");
            return (Criteria) this;
        }

        public Criteria andCollageSizeIdLessThanOrEqualTo(Integer value) {
            addCriterion("collage_size_id <=", value, "collageSizeId");
            return (Criteria) this;
        }

        public Criteria andCollageSizeIdIn(List<Integer> values) {
            addCriterion("collage_size_id in", values, "collageSizeId");
            return (Criteria) this;
        }

        public Criteria andCollageSizeIdNotIn(List<Integer> values) {
            addCriterion("collage_size_id not in", values, "collageSizeId");
            return (Criteria) this;
        }

        public Criteria andCollageSizeIdBetween(Integer value1, Integer value2) {
            addCriterion("collage_size_id between", value1, value2, "collageSizeId");
            return (Criteria) this;
        }

        public Criteria andCollageSizeIdNotBetween(Integer value1, Integer value2) {
            addCriterion("collage_size_id not between", value1, value2, "collageSizeId");
            return (Criteria) this;
        }

        public Criteria andIndustryIdsIsNull() {
            addCriterion("industry_ids is null");
            return (Criteria) this;
        }

        public Criteria andIndustryIdsIsNotNull() {
            addCriterion("industry_ids is not null");
            return (Criteria) this;
        }

        public Criteria andIndustryIdsEqualTo(String value) {
            addCriterion("industry_ids =", value, "industryIds");
            return (Criteria) this;
        }

        public Criteria andIndustryIdsNotEqualTo(String value) {
            addCriterion("industry_ids <>", value, "industryIds");
            return (Criteria) this;
        }

        public Criteria andIndustryIdsGreaterThan(String value) {
            addCriterion("industry_ids >", value, "industryIds");
            return (Criteria) this;
        }

        public Criteria andIndustryIdsGreaterThanOrEqualTo(String value) {
            addCriterion("industry_ids >=", value, "industryIds");
            return (Criteria) this;
        }

        public Criteria andIndustryIdsLessThan(String value) {
            addCriterion("industry_ids <", value, "industryIds");
            return (Criteria) this;
        }

        public Criteria andIndustryIdsLessThanOrEqualTo(String value) {
            addCriterion("industry_ids <=", value, "industryIds");
            return (Criteria) this;
        }

        public Criteria andIndustryIdsLike(String value) {
            addCriterion("industry_ids like", value, "industryIds");
            return (Criteria) this;
        }

        public Criteria andIndustryIdsNotLike(String value) {
            addCriterion("industry_ids not like", value, "industryIds");
            return (Criteria) this;
        }

        public Criteria andIndustryIdsIn(List<String> values) {
            addCriterion("industry_ids in", values, "industryIds");
            return (Criteria) this;
        }

        public Criteria andIndustryIdsNotIn(List<String> values) {
            addCriterion("industry_ids not in", values, "industryIds");
            return (Criteria) this;
        }

        public Criteria andIndustryIdsBetween(String value1, String value2) {
            addCriterion("industry_ids between", value1, value2, "industryIds");
            return (Criteria) this;
        }

        public Criteria andIndustryIdsNotBetween(String value1, String value2) {
            addCriterion("industry_ids not between", value1, value2, "industryIds");
            return (Criteria) this;
        }

        public Criteria andTagIdIsNull() {
            addCriterion("tag_id is null");
            return (Criteria) this;
        }

        public Criteria andTagIdIsNotNull() {
            addCriterion("tag_id is not null");
            return (Criteria) this;
        }

        public Criteria andTagIdEqualTo(Integer value) {
            addCriterion("tag_id =", value, "tagId");
            return (Criteria) this;
        }

        public Criteria andTagIdNotEqualTo(Integer value) {
            addCriterion("tag_id <>", value, "tagId");
            return (Criteria) this;
        }

        public Criteria andTagIdGreaterThan(Integer value) {
            addCriterion("tag_id >", value, "tagId");
            return (Criteria) this;
        }

        public Criteria andTagIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("tag_id >=", value, "tagId");
            return (Criteria) this;
        }

        public Criteria andTagIdLessThan(Integer value) {
            addCriterion("tag_id <", value, "tagId");
            return (Criteria) this;
        }

        public Criteria andTagIdLessThanOrEqualTo(Integer value) {
            addCriterion("tag_id <=", value, "tagId");
            return (Criteria) this;
        }

        public Criteria andTagIdIn(List<Integer> values) {
            addCriterion("tag_id in", values, "tagId");
            return (Criteria) this;
        }

        public Criteria andTagIdNotIn(List<Integer> values) {
            addCriterion("tag_id not in", values, "tagId");
            return (Criteria) this;
        }

        public Criteria andTagIdBetween(Integer value1, Integer value2) {
            addCriterion("tag_id between", value1, value2, "tagId");
            return (Criteria) this;
        }

        public Criteria andTagIdNotBetween(Integer value1, Integer value2) {
            addCriterion("tag_id not between", value1, value2, "tagId");
            return (Criteria) this;
        }

        public Criteria andRenderImageIsNull() {
            addCriterion("render_image is null");
            return (Criteria) this;
        }

        public Criteria andRenderImageIsNotNull() {
            addCriterion("render_image is not null");
            return (Criteria) this;
        }

        public Criteria andRenderImageEqualTo(String value) {
            addCriterion("render_image =", value, "renderImage");
            return (Criteria) this;
        }

        public Criteria andRenderImageNotEqualTo(String value) {
            addCriterion("render_image <>", value, "renderImage");
            return (Criteria) this;
        }

        public Criteria andRenderImageGreaterThan(String value) {
            addCriterion("render_image >", value, "renderImage");
            return (Criteria) this;
        }

        public Criteria andRenderImageGreaterThanOrEqualTo(String value) {
            addCriterion("render_image >=", value, "renderImage");
            return (Criteria) this;
        }

        public Criteria andRenderImageLessThan(String value) {
            addCriterion("render_image <", value, "renderImage");
            return (Criteria) this;
        }

        public Criteria andRenderImageLessThanOrEqualTo(String value) {
            addCriterion("render_image <=", value, "renderImage");
            return (Criteria) this;
        }

        public Criteria andRenderImageLike(String value) {
            addCriterion("render_image like", value, "renderImage");
            return (Criteria) this;
        }

        public Criteria andRenderImageNotLike(String value) {
            addCriterion("render_image not like", value, "renderImage");
            return (Criteria) this;
        }

        public Criteria andRenderImageIn(List<String> values) {
            addCriterion("render_image in", values, "renderImage");
            return (Criteria) this;
        }

        public Criteria andRenderImageNotIn(List<String> values) {
            addCriterion("render_image not in", values, "renderImage");
            return (Criteria) this;
        }

        public Criteria andRenderImageBetween(String value1, String value2) {
            addCriterion("render_image between", value1, value2, "renderImage");
            return (Criteria) this;
        }

        public Criteria andRenderImageNotBetween(String value1, String value2) {
            addCriterion("render_image not between", value1, value2, "renderImage");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(String value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(String value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(String value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(String value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(String value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(String value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLike(String value) {
            addCriterion("creator like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotLike(String value) {
            addCriterion("creator not like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<String> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<String> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(String value1, String value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(String value1, String value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andEditionIsNull() {
            addCriterion("edition is null");
            return (Criteria) this;
        }

        public Criteria andEditionIsNotNull() {
            addCriterion("edition is not null");
            return (Criteria) this;
        }

        public Criteria andEditionEqualTo(Integer value) {
            addCriterion("edition =", value, "edition");
            return (Criteria) this;
        }

        public Criteria andEditionNotEqualTo(Integer value) {
            addCriterion("edition <>", value, "edition");
            return (Criteria) this;
        }

        public Criteria andEditionGreaterThan(Integer value) {
            addCriterion("edition >", value, "edition");
            return (Criteria) this;
        }

        public Criteria andEditionGreaterThanOrEqualTo(Integer value) {
            addCriterion("edition >=", value, "edition");
            return (Criteria) this;
        }

        public Criteria andEditionLessThan(Integer value) {
            addCriterion("edition <", value, "edition");
            return (Criteria) this;
        }

        public Criteria andEditionLessThanOrEqualTo(Integer value) {
            addCriterion("edition <=", value, "edition");
            return (Criteria) this;
        }

        public Criteria andEditionIn(List<Integer> values) {
            addCriterion("edition in", values, "edition");
            return (Criteria) this;
        }

        public Criteria andEditionNotIn(List<Integer> values) {
            addCriterion("edition not in", values, "edition");
            return (Criteria) this;
        }

        public Criteria andEditionBetween(Integer value1, Integer value2) {
            addCriterion("edition between", value1, value2, "edition");
            return (Criteria) this;
        }

        public Criteria andEditionNotBetween(Integer value1, Integer value2) {
            addCriterion("edition not between", value1, value2, "edition");
            return (Criteria) this;
        }

        public Criteria andPatternIdIsNull() {
            addCriterion("pattern_id is null");
            return (Criteria) this;
        }

        public Criteria andPatternIdIsNotNull() {
            addCriterion("pattern_id is not null");
            return (Criteria) this;
        }

        public Criteria andPatternIdEqualTo(Long value) {
            addCriterion("pattern_id =", value, "patternId");
            return (Criteria) this;
        }

        public Criteria andPatternIdNotEqualTo(Long value) {
            addCriterion("pattern_id <>", value, "patternId");
            return (Criteria) this;
        }

        public Criteria andPatternIdGreaterThan(Long value) {
            addCriterion("pattern_id >", value, "patternId");
            return (Criteria) this;
        }

        public Criteria andPatternIdGreaterThanOrEqualTo(Long value) {
            addCriterion("pattern_id >=", value, "patternId");
            return (Criteria) this;
        }

        public Criteria andPatternIdLessThan(Long value) {
            addCriterion("pattern_id <", value, "patternId");
            return (Criteria) this;
        }

        public Criteria andPatternIdLessThanOrEqualTo(Long value) {
            addCriterion("pattern_id <=", value, "patternId");
            return (Criteria) this;
        }

        public Criteria andPatternIdIn(List<Long> values) {
            addCriterion("pattern_id in", values, "patternId");
            return (Criteria) this;
        }

        public Criteria andPatternIdNotIn(List<Long> values) {
            addCriterion("pattern_id not in", values, "patternId");
            return (Criteria) this;
        }

        public Criteria andPatternIdBetween(Long value1, Long value2) {
            addCriterion("pattern_id between", value1, value2, "patternId");
            return (Criteria) this;
        }

        public Criteria andPatternIdNotBetween(Long value1, Long value2) {
            addCriterion("pattern_id not between", value1, value2, "patternId");
            return (Criteria) this;
        }

        public Criteria andPatternRadioIsNull() {
            addCriterion("pattern_radio is null");
            return (Criteria) this;
        }

        public Criteria andPatternRadioIsNotNull() {
            addCriterion("pattern_radio is not null");
            return (Criteria) this;
        }

        public Criteria andPatternRadioEqualTo(Integer value) {
            addCriterion("pattern_radio =", value, "patternRadio");
            return (Criteria) this;
        }

        public Criteria andPatternRadioNotEqualTo(Integer value) {
            addCriterion("pattern_radio <>", value, "patternRadio");
            return (Criteria) this;
        }

        public Criteria andPatternRadioGreaterThan(Integer value) {
            addCriterion("pattern_radio >", value, "patternRadio");
            return (Criteria) this;
        }

        public Criteria andPatternRadioGreaterThanOrEqualTo(Integer value) {
            addCriterion("pattern_radio >=", value, "patternRadio");
            return (Criteria) this;
        }

        public Criteria andPatternRadioLessThan(Integer value) {
            addCriterion("pattern_radio <", value, "patternRadio");
            return (Criteria) this;
        }

        public Criteria andPatternRadioLessThanOrEqualTo(Integer value) {
            addCriterion("pattern_radio <=", value, "patternRadio");
            return (Criteria) this;
        }

        public Criteria andPatternRadioIn(List<Integer> values) {
            addCriterion("pattern_radio in", values, "patternRadio");
            return (Criteria) this;
        }

        public Criteria andPatternRadioNotIn(List<Integer> values) {
            addCriterion("pattern_radio not in", values, "patternRadio");
            return (Criteria) this;
        }

        public Criteria andPatternRadioBetween(Integer value1, Integer value2) {
            addCriterion("pattern_radio between", value1, value2, "patternRadio");
            return (Criteria) this;
        }

        public Criteria andPatternRadioNotBetween(Integer value1, Integer value2) {
            addCriterion("pattern_radio not between", value1, value2, "patternRadio");
            return (Criteria) this;
        }

        public Criteria andPatternCoverIsNull() {
            addCriterion("pattern_cover is null");
            return (Criteria) this;
        }

        public Criteria andPatternCoverIsNotNull() {
            addCriterion("pattern_cover is not null");
            return (Criteria) this;
        }

        public Criteria andPatternCoverEqualTo(String value) {
            addCriterion("pattern_cover =", value, "patternCover");
            return (Criteria) this;
        }

        public Criteria andPatternCoverNotEqualTo(String value) {
            addCriterion("pattern_cover <>", value, "patternCover");
            return (Criteria) this;
        }

        public Criteria andPatternCoverGreaterThan(String value) {
            addCriterion("pattern_cover >", value, "patternCover");
            return (Criteria) this;
        }

        public Criteria andPatternCoverGreaterThanOrEqualTo(String value) {
            addCriterion("pattern_cover >=", value, "patternCover");
            return (Criteria) this;
        }

        public Criteria andPatternCoverLessThan(String value) {
            addCriterion("pattern_cover <", value, "patternCover");
            return (Criteria) this;
        }

        public Criteria andPatternCoverLessThanOrEqualTo(String value) {
            addCriterion("pattern_cover <=", value, "patternCover");
            return (Criteria) this;
        }

        public Criteria andPatternCoverLike(String value) {
            addCriterion("pattern_cover like", value, "patternCover");
            return (Criteria) this;
        }

        public Criteria andPatternCoverNotLike(String value) {
            addCriterion("pattern_cover not like", value, "patternCover");
            return (Criteria) this;
        }

        public Criteria andPatternCoverIn(List<String> values) {
            addCriterion("pattern_cover in", values, "patternCover");
            return (Criteria) this;
        }

        public Criteria andPatternCoverNotIn(List<String> values) {
            addCriterion("pattern_cover not in", values, "patternCover");
            return (Criteria) this;
        }

        public Criteria andPatternCoverBetween(String value1, String value2) {
            addCriterion("pattern_cover between", value1, value2, "patternCover");
            return (Criteria) this;
        }

        public Criteria andPatternCoverNotBetween(String value1, String value2) {
            addCriterion("pattern_cover not between", value1, value2, "patternCover");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}