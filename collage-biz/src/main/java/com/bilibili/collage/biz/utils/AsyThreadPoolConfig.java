package com.bilibili.collage.biz.utils;

import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Configuration
public class AsyThreadPoolConfig {

    public static ConcurrentHashMap<String, ThreadPoolExecutor> threadPoolMap = new ConcurrentHashMap<>();


    public static final String ARC_SUPPORT_ADVERTISING = "ArcSupportAdvertising";

    static {
        init();
    }

    static void init() {
        threadPoolMap.put(ARC_SUPPORT_ADVERTISING, threadPoolAbort(8, ARC_SUPPORT_ADVERTISING));
    }

    static ThreadPoolExecutor threadPoolAbort(int poolSize, String threadNamePrefix) {
        return new ThreadPoolExecutor(poolSize, poolSize, 0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingDeque<>(1024), new NamedThreadFactor(threadNamePrefix));
    }


    static class NamedThreadFactor implements ThreadFactory {


        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;

        NamedThreadFactor(String namePrefix) {
            this.namePrefix = namePrefix + "-";
        }


        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r, namePrefix + threadNumber.getAndIncrement());
            if (thread.isDaemon()) {
                thread.setDaemon(true);
            }
            if (thread.getPriority() != Thread.NORM_PRIORITY) {
                thread.setPriority(Thread.NORM_PRIORITY);
            }
            return thread;
        }
    }
}
