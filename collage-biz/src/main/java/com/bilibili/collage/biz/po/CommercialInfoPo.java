package com.bilibili.collage.biz.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
@Data
public class CommercialInfoPo {
    /**
     * 自增ID（主键）
     */
    private Long id;

    /**
     * 链接地址
     */
    private String jumpUrl;

    /**
     * 分类名称
     */
    private String title;

    /**
     * 软删除: 0-有效 1-删除
     */
    private Byte isDeleted;

    /**
     * 添加时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;

    /**
     * 三连首页是否展示
     */
    private Integer sanlianIsShow;

    /**
     * 三连首页展示顺序
     */
    private Integer sanlianShowOrderNum;

}