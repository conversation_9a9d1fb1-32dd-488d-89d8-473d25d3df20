package com.bilibili.collage.biz.service;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.collage.api.dto.CollageOperationLogDto;
import com.bilibili.collage.api.dto.NewCollageOperationLogDto;
import com.bilibili.collage.api.dto.QueryCollageOperationLogDto;
import com.bilibili.collage.api.service.ICollageLogService;
import com.bilibili.collage.biz.dao.CollageOperationLogDao;
import com.bilibili.collage.biz.po.CollageOperationLogPo;
import com.bilibili.collage.biz.po.CollageOperationLogPoExample;
import com.bilibili.mgk.platform.common.CollageLogObjFlagEnum;
import com.bilibili.mgk.platform.common.CollageOperateTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.support.Assert;

import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/09/10
 **/
@Service
public class CollageLogServiceImpl implements ICollageLogService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CollageLogServiceImpl.class);

    @Autowired
    private CollageOperationLogDao collageOperationLogDao;

    @Override
    public void insertLog(NewCollageOperationLogDto newCollageOperationLogDto) {
        validNewLogOperation(newCollageOperationLogDto);

        CollageOperationLogPo po = dto2po(newCollageOperationLogDto);

        try {
            collageOperationLogDao.insertSelective(po);
        } catch (Exception e) {
            LOGGER.error("insertLog.error", e);
        }

    }

    private CollageOperationLogPo dto2po(NewCollageOperationLogDto newCollageOperationLogDto) {
        CollageOperationLogPo po = CollageOperationLogPo.builder()
                .build();
        BeanUtils.copyProperties(newCollageOperationLogDto, po);
        po.setOldValue(JSON.toJSONString(newCollageOperationLogDto.getOldValue()));
        po.setNewValue(JSON.toJSONString(newCollageOperationLogDto.getNewValue()));
        po.setIsDeleted(IsDeleted.VALID.getCode());
        return po;
    }

    private CollageOperationLogPo dto2po(Timestamp timestamp, NewCollageOperationLogDto newCollageOperationLogDto) {
        CollageOperationLogPo po = dto2po(newCollageOperationLogDto);
        po.setMtime(timestamp);
        po.setCtime(timestamp);
        return po;
    }


    @Override
    public PageResult<CollageOperationLogDto> queryOperationLogs(QueryCollageOperationLogDto param) {
        Assert.notNull(param, "查询日志参数不可为空");
        CollageOperationLogPoExample example = getLogExample(param);

        Long total = collageOperationLogDao.countByExample(example);
        if (total == 0) {
            return PageResult.emptyPageResult();
        }

        List<CollageOperationLogPo> pos = collageOperationLogDao.selectByExample(example);

        List<CollageOperationLogDto> dtos = pos.stream().map(this::convertLogPo2Dto).collect(Collectors.toList());

        return PageResult.<CollageOperationLogDto>builder()
                .total(total.intValue())
                .records(dtos)
                .build();
    }

    @Override
    public void batchInsert(List<NewCollageOperationLogDto> logDtos) {
        logDtos.forEach(this::validNewLogOperation);
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        List<CollageOperationLogPo> pos = logDtos.stream().map(dto -> this.dto2po(timestamp, dto)).collect(Collectors.toList());
        collageOperationLogDao.insertBatch(pos);
    }

    private CollageOperationLogDto convertLogPo2Dto(CollageOperationLogPo po) {
        CollageOperationLogDto dto = CollageOperationLogDto.builder().build();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    private CollageOperationLogPoExample getLogExample(QueryCollageOperationLogDto param) {

        CollageOperationLogPoExample example = new CollageOperationLogPoExample();
        CollageOperationLogPoExample.Criteria c = example.or();
        ObjectUtils.setObject(param::getAccountId, c::andAccountIdEqualTo);
        ObjectUtils.setObject(param::getObjId, c::andObjIdEqualTo);
        ObjectUtils.setObject(param::getObjFlag, c::andObjFlagEqualTo);
        ObjectUtils.setObject(param::getOperateType, c::andOperateTypeEqualTo);
        ObjectUtils.setObject(param::getOperatorType, c::andOperatorTypeEqualTo);
        ObjectUtils.setObject(param::getStartTime, c::andCtimeGreaterThanOrEqualTo);
        ObjectUtils.setObject(param::getEndTime, c::andCtimeLessThanOrEqualTo);

        ObjectUtils.setObject(param::getOrderBy, example::setOrderByClause);

        ObjectUtils.setPage(param::getPage, example::setLimit, example::setOffset);

        return example;
    }

    private void validNewLogOperation(NewCollageOperationLogDto dto) {
        Assert.notNull(dto, "操作信息不可为空");
        Assert.notNull(dto.getAccountId(), "账号Id不可为空");
        Assert.notNull(dto.getObjId(), "对象Id不可为空");
        Assert.notNull(dto.getObjFlag(), "对象类型不可为空");
        CollageLogObjFlagEnum.getByCode(dto.getObjFlag());
        Assert.notNull(dto.getOperateType(), "操作类型不可为空");
        CollageOperateTypeEnum.getByCode(dto.getOperateType());
        Assert.notNull(dto.getOperatorUsername(), "操作人不可为空");
        Assert.notNull(dto.getOperatorType(), "操作人类型不可为空");
        OperatorType.getByCode(dto.getOperatorType());
    }
}
