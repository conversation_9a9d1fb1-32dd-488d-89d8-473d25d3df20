package com.bilibili.collage.biz.service.school.migrate;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.collage.api.dto.CategoryDto;
import com.bilibili.collage.api.dto.CommercialInfoDto;
import com.bilibili.collage.api.dto.CommonQuestionDto;
import com.bilibili.collage.api.dto.CreateAdvertiseGuidanceDto;
import com.bilibili.collage.api.dto.CreateArticleDto;
import com.bilibili.collage.api.dto.CreateCommercialInfoDto;
import com.bilibili.collage.api.dto.CreateCommonQuestionDto;
import com.bilibili.collage.api.dto.CreateProductManualDto;
import com.bilibili.collage.api.dto.EditAdvertiseGuidanceDto;
import com.bilibili.collage.api.dto.EditProductManualDto;
import com.bilibili.collage.api.dto.SanlianLatestUpdateDto;
import com.bilibili.collage.api.dto.SchoolArticleDto;
import com.bilibili.collage.api.dto.TitleDto;
import com.bilibili.collage.api.service.ISchoolConfigService;
import com.bilibili.collage.biz.config.SchoolDocTreeConfig;
import com.bilibili.collage.biz.service.school.SchoolConfigService;
import com.bilibili.collage.biz.service.school.SchoolPojoConvertor;
import com.bilibili.collage.biz.service.school.doctree.SanlianSchoolDocTreeMngServiceImpl;
import com.bilibili.collage.biz.service.school.doctree.SanlianSchoolDocTreeQueryServiceImpl;
import com.bilibili.collage.biz.service.school.doctree.model.SanlianDocNode;
import com.bilibili.collage.biz.service.school.doctree.model.SanlianSchoolNodeType;
import com.bilibili.collage.biz.service.school.doctree.model.SanlianSchoolTreeType;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodeBatchGetReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodeCreateReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodeGetReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodeListReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodePageReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodeUpdateReq;
import com.bilibili.mgk.platform.common.enums.school.CategoryTypeEnum;
import com.biz.common.doc.tree.common.Pagination;
import com.biz.common.doc.tree.model.DocFlattenNode;
import com.biz.common.doc.tree.service.DocNodeCommandService;
import com.biz.common.doc.tree.service.DocNodeQueryService;
import com.biz.common.doc.tree.service.vo.NodeDeleteReq;
import com.biz.common.doc.tree.service.vo.NodeGetReq;
import com.biz.common.doc.tree.service.vo.NodeListReq;
import com.biz.common.doc.tree.service.vo.NodePageReq;
import com.biz.common.doc.tree.service.vo.NodeSortType;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/12/13
 */
@Slf4j
@Primary
@Service
public class SanlianSchoolMngServiceDocTreeAdaptImpl implements ISchoolConfigService {

    @Resource
    private SchoolDocTreeConfig schoolDocTreeConfig;

    @Resource
    private DocNodeCommandService docNodeCommandService;

    @Resource
    private DocNodeQueryService docNodeQueryService;


    @Resource
    private SchoolConfigService v1SchoolConfigService;


    @Resource
    private SanlianSchoolDocTreeMngServiceImpl v2SchoolConfigServicePowerByDocTree;


    @Resource
    private SanlianSchoolDocTreeQueryServiceImpl v2SchoolQueryServicePowerByDocTree;



    /**
     * 废弃！！ 虽然保留了兼容，但是不应该用到，因为目前的创建结构不能满足多层需求
     *
     * @param createProductManualDto
     * @return
     */
    @Override
    @Deprecated
    public Integer createProductManual(CreateProductManualDto createProductManualDto) {

        if (!schoolDocTreeConfig.getMngMigrateEnabled()) {

            return v1SchoolConfigService.createProductManual(createProductManualDto);
        }

//        SanlianDocNode r = v2SchoolConfigServicePowerByDocTree.newNode(
//                SchoolPojoConvertor.convertor.productManual2SanlianCreateReq(createProductManualDto)
//                        .setTreeType(SanlianSchoolTreeType.product_manual)
//
//        );

        log.error("废弃接口, 降级为操作v1接口");

        return v1SchoolConfigService.createProductManual(createProductManualDto);

    }


    /**
     * 废弃
     *
     * @param createAdvertiseGuidanceDto
     * @return
     */
    @Deprecated
    // 只有一层
    @Override
    public Integer createAdvertiseGuidance(CreateAdvertiseGuidanceDto createAdvertiseGuidanceDto) {

        if (!schoolDocTreeConfig.getMngMigrateEnabled()) {
            return v1SchoolConfigService.createAdvertiseGuidance(createAdvertiseGuidanceDto);
        }

//
//        DocFlattenNode r = docNodeCommandService.newNode(
//                SchoolPojoConvertor.convertor.advertiseGuidance2CreateReq(createAdvertiseGuidanceDto)
//                        .setParentId(schoolDocTreeConfig.getAdvertiseGuidanceRootId())
//        );
//
//        return 1;

        log.error("废弃接口, 降级为操作v1接口");
        return v1SchoolConfigService.createAdvertiseGuidance(createAdvertiseGuidanceDto);
    }


    /**
     * 废弃！！
     */
    @Override
    @Deprecated
    public PageResult<CategoryDto> getProductManualList(String nameLike, Long timeFrom, Long timeTo, Integer page,
            Integer size) {

        if (!schoolDocTreeConfig.getMngMigrateEnabled()) {
            return v1SchoolConfigService.getProductManualList(nameLike, timeFrom, timeTo, page, size);
        }

        // TODO nameLike没必要支持 timebetween 实际也没必要支持
        Pagination<List<DocFlattenNode>> nodePage = docNodeQueryService.pageChildren(
                (NodePageReq) new NodePageReq()
                        .setPn(page)
                        .setPs(size)
                        .setRootId(Lists.newArrayList(schoolDocTreeConfig.getProductManualRootId()))
                        .setNodeType(Lists.newArrayList(SanlianSchoolNodeType.manual_dir.name()))
//                        .setCtimeFrom(
//                                Optional.ofNullable(timeFrom)
//                                        .map(t -> LocalDateTime.ofInstant(Instant.ofEpochMilli(t), ZoneOffset.of("+8")))
//                                        .orElse(null)
//                        )
//                        .setCtimeTo(
//                                Optional.ofNullable(timeTo)
//                                        .map(t -> LocalDateTime.ofInstant(Instant.ofEpochMilli(t), ZoneOffset.of("+8")))
//                                        .orElse(null)
//                        )
        );

        return new PageResult<>(nodePage.getTotalCount(),

                Optional.ofNullable(nodePage.getData()).orElse(new ArrayList<>())
                        .stream().map(item -> SchoolPojoConvertor.convertor.node2ProductMunualCategory(item))
                        .collect(Collectors.toList())
        );


    }


    /**
     * 废弃！！
     */
    @Deprecated
    @Override
    public PageResult<CategoryDto> getAdvertiseGuidanceList(String nameLike, Long timeFrom, Long timeTo, Integer page,
            Integer size) {

        if (!schoolDocTreeConfig.getMngMigrateEnabled()) {
            return v1SchoolConfigService.getAdvertiseGuidanceList(nameLike, timeFrom, timeTo, page, size);
        }

        // TODO nameLike没必要支持 timebetween 可以支持
        Pagination<List<DocFlattenNode>> nodePage = docNodeQueryService.pageChildren(
                (NodePageReq) new NodePageReq()
                        .setPn(page)
                        .setPs(size)
                        .setRootId(Lists.newArrayList(schoolDocTreeConfig.getAdvertiseGuidanceRootId()))
                        .setNodeType(Lists.newArrayList(SanlianSchoolNodeType.ad_guide_dir.name()))
//                        .setCtimeFrom(
//                                Optional.ofNullable(timeFrom)
//                                        .map(t -> LocalDateTime.ofInstant(Instant.ofEpochMilli(t), ZoneOffset.of("+8")))
//                                        .orElse(null)
//                        )
//                        .setCtimeTo(
//                                Optional.ofNullable(timeTo)
//                                        .map(t -> LocalDateTime.ofInstant(Instant.ofEpochMilli(t), ZoneOffset.of("+8")))
//                                        .orElse(null)
//                        )
        );

        return new PageResult<>(nodePage.getTotalCount(),
                Optional.ofNullable(nodePage.getData()).orElse(new ArrayList<>())
                        .stream().map(item -> SchoolPojoConvertor.convertor.node2AdvertiseGuidanceCategory(item))
                        .collect(Collectors.toList())
        );
    }

    /**
     * 可保留可废弃, 总之是不建议使用了
     *
     * @param editProductManualDto
     * @return
     */
    @Override
    @Deprecated
    public Integer editProductManual(EditProductManualDto editProductManualDto) {

        if (!schoolDocTreeConfig.getMngMigrateEnabled()) {
            return v1SchoolConfigService.editProductManual(editProductManualDto);
        }

//        SanlianDocNode r = v2SchoolConfigServicePowerByDocTree.updateNode(
//
//                SchoolPojoConvertor.convertor.productManual2SanlianUpdateReq(editProductManualDto)
//        );

        log.error("废弃接口, 降级为操作v1接口");
        return v1SchoolConfigService.editProductManual(editProductManualDto);
    }


    /**
     * 废弃！！
     *
     * @param editAdvertiseGuidanceDto
     * @return
     */
    @Override
    @Deprecated
    public Integer editAdvertiseGuidance(EditAdvertiseGuidanceDto editAdvertiseGuidanceDto) {

        if (!schoolDocTreeConfig.getMngMigrateEnabled()) {
            return v1SchoolConfigService.editAdvertiseGuidance(editAdvertiseGuidanceDto);
        }

        log.error("废弃接口, 降级为操作v1接口");

        return v1SchoolConfigService.editAdvertiseGuidance(editAdvertiseGuidanceDto);

    }

    @Override
    public Integer deleteProductManual(Long id) {

        if (!schoolDocTreeConfig.getMngMigrateEnabled()) {
            return v1SchoolConfigService.deleteProductManual(id);
        }

        SanlianDocNode r = v2SchoolConfigServicePowerByDocTree.deleteNode(new NodeDeleteReq().setNodeId(id));

        return 1;
    }


    @Override
    public Integer deleteAdvertiseGuidance(Long id) {

        if (!schoolDocTreeConfig.getMngMigrateEnabled()) {
            return v1SchoolConfigService.deleteAdvertiseGuidance(id);
        }

        SanlianDocNode r = v2SchoolConfigServicePowerByDocTree.deleteNode(new NodeDeleteReq().setNodeId(id));
        return 1;
    }


    /**
     * 可以保留这个兼容实现
     *
     * @param type
     * @param level
     * @return
     */
    @Override
    public List<TitleDto> searchTitle(Byte type, Integer level) {

        if (!schoolDocTreeConfig.getMngMigrateEnabled()) {
            return v1SchoolConfigService.searchTitle(type, level);
        }

        List<DocFlattenNode> results = new ArrayList<>();

        if (CategoryTypeEnum.PRODUCT_MANUAL.getCode().equals(type) ||
                !CategoryTypeEnum.ADVERTISE_GUIDANCE.getCode().equals(type)) {

            results.addAll(docNodeQueryService.listChildren(
                    new NodeListReq()
                            .setRootId(Lists.newArrayList(schoolDocTreeConfig.getProductManualRootId()))
                            .setNodeId(schoolDocTreeConfig.getProductManualRootId())
                            .setDepth(level + 1)
                            .setSort(NodeSortType.mtime_desc)
            ));
        }

        if (CategoryTypeEnum.ADVERTISE_GUIDANCE.getCode().equals(type) ||
                !CategoryTypeEnum.PRODUCT_MANUAL.getCode().equals(type)) {

            results.addAll(docNodeQueryService.listChildren(
                    new NodeListReq()
                            .setRootId(Lists.newArrayList(schoolDocTreeConfig.getAdvertiseGuidanceRootId()))
                            .setNodeId(schoolDocTreeConfig.getAdvertiseGuidanceRootId())
                            .setSort(NodeSortType.mtime_desc)
            ));
        }

        return results.stream().map(item -> {
            TitleDto titleDto = new TitleDto();
            titleDto.setId(item.getNodeId());
            titleDto.setTitle(item.getNodeName());
            return titleDto;
        }).collect(Collectors.toList());
    }

    /**
     * 1。对应三种排序link 尽量配置页保留，这样减少前端的接入成本; 2。对于新需要接入的latest_info
     * 则不再提供新的适配接口，而是使用新的一套底层doc-tree接口进行接入，create就是对应的newNode接口
     *
     * @param dto
     * @return
     * @throws ServiceException
     */
    // 一层
    @Override
    public Integer createCommonQuestion(CreateCommonQuestionDto dto) throws ServiceException {

        if (!schoolDocTreeConfig.getMngMigrateEnabled()) {
            return v1SchoolConfigService.createCommonQuestion(dto);
        }

        this.validateTryAddNewSanlianShowQuestion(false, dto.getSanlianIsShow() == 1);

        // check relate article-id
        SanlianDocNode article = v2SchoolQueryServicePowerByDocTree.getNode(new SanlianNodeGetReq()
                .setNodeId(dto.getTitleId())
                .setIsShow(true)
                .setIsDeleted(false)
        );

        if (article == null) {
            throw new ServiceException("找不到关联文章");
        }

        SanlianDocNode r = v2SchoolConfigServicePowerByDocTree.newNode(
                new SanlianNodeCreateReq()
                        .setTreeType(SanlianSchoolTreeType.common_question)
                        .setParentId(schoolDocTreeConfig.getCommonQuestionRootId())
                        .setNodeName(article.getNodeName())
                        .setDocType(String.valueOf(dto.getType()))
                        .setArticleId(article.getNodeId())
                        .setArticleTitle(article.getNodeName())
                        .setArticleParentId(article.getParentId())
                        .setArticleParentName(article.getParentName())
                        .setArticleType((int) SanlianSchoolTreeType.toV1ByteType(SanlianSchoolTreeType.common_question))
                        .setSanlianOrderNum(dto.getSanlianShowOrderNum())
                        .setIsShow(dto.getSanlianIsShow() == 1)
                        .setOrderNum(dto.getOrderNum())
        );

        /**
         *  @link com.bilibili.collage.biz.service.school.SchoolConfigService#getLatestUpdateListForSanlian()} 从该代码看，
         *  常见问题不能用于“最新动态”的配置， 这里需要添加附属额的node属性
         */
        // this.newLatestInfoNode(r, dto.getSanlianShowOrderNum(), dto.getSanlianIsShow());

        log.info("Success to create common question in doc-tree, req={}, resp={}", dto, r);
        return 1;

    }


    @Override
    public Integer createCommercialInfo(CreateCommercialInfoDto createCommercialInfoDto) {

        if (!schoolDocTreeConfig.getMngMigrateEnabled()) {
            return v1SchoolConfigService.createCommercialInfo(createCommercialInfoDto);
        }

        // commercial 本体中的order和show纯纯无用，
        SanlianDocNode r = v2SchoolConfigServicePowerByDocTree.newNode(
                new SanlianNodeCreateReq()
                        .setTreeType(SanlianSchoolTreeType.commercial_info)
                        .setTitle(createCommercialInfoDto.getTitle())
                        .setIsShow(createCommercialInfoDto.getSanlianIsShow() == 1)
                        .setOrderNum(createCommercialInfoDto.getSanlianShowOrderNum())
                        .setJumpUrl(createCommercialInfoDto.getUrl())
        );

        this.newLatestInfoNode(r, createCommercialInfoDto.getSanlianShowOrderNum(),
                createCommercialInfoDto.getSanlianIsShow());

        return 1;
    }


    // 感觉这个接口没有意义
    // TODO 可以改成只有三连的版本
    @Override
    @Deprecated
    public PageResult<CommonQuestionDto> getCommonQuestionListForMng(Integer page, Integer size) {

        if (!schoolDocTreeConfig.getMngMigrateEnabled()) {
            return v1SchoolConfigService.getCommonQuestionListForMng(page, size);
        }

        Pagination<List<SanlianDocNode>> pageResp = v2SchoolConfigServicePowerByDocTree.pageChildrenForMng(

                new SanlianNodePageReq()
                        .setPn(page)
                        .setPs(size)
                        .setTreeType(SanlianSchoolTreeType.common_question)
                        .setNodeId(schoolDocTreeConfig.getCommonQuestionRootId())
                        .setIsDeleted(false)
                        .setIsShow(true)
                        .setSort(NodeSortType.priority_asc)
                // 在老逻辑里是mtime排序 但是我觉得没必要
//                        .setSort(NodeSortType.mtime_desc)
        );

        if (CollectionUtils.isEmpty(pageResp.getData())) {
            return new PageResult<>(0, new ArrayList<>());
        }

        return new PageResult<>(pageResp.getTotalCount(),
                Optional.ofNullable(pageResp.getData())
                        .orElse(new ArrayList<>())
                        .stream()
                        .map(item -> SchoolPojoConvertor.convertor.node2CommonQuestionForMng(item))
                        .collect(Collectors.toList())
        );


    }


    @Override
    public PageResult<CommercialInfoDto> getCommercialInfoList(Integer page, Integer size) {

        if (v1SchoolConfigService.getCommercialInfoList(page, size) != null) {
            return v1SchoolConfigService.getCommercialInfoList(page, size);
        }

        Pagination<List<SanlianDocNode>> pageResp = v2SchoolConfigServicePowerByDocTree.pageChildrenForMng(

                new SanlianNodePageReq()
                        .setPn(page)
                        .setPs(size)
                        .setTreeType(SanlianSchoolTreeType.commercial_info)
                        .setNodeId(schoolDocTreeConfig.getCommercialInfoRootId())
                        .setIsDeleted(false)
                        .setIsShow(null)
                // 在老逻辑里是mtime排序 但是我觉得没必要
//                        .setSort(NodeSortType.mtime_desc)
        );

        if (CollectionUtils.isEmpty(pageResp.getData())) {
            return new PageResult<>(0, new ArrayList<>());
        }

        return new PageResult<>(pageResp.getTotalCount(),
                Optional.ofNullable(pageResp.getData())
                        .orElse(new ArrayList<>())
                        .stream()
                        .map(item -> SchoolPojoConvertor.convertor.node2CommercialInfo(item))
                        .collect(Collectors.toList())
        );

    }


    // TODO 删除 普通节点逻辑
    @Override
    public Integer editCommonQuestion(CreateCommonQuestionDto dto) throws ServiceException {

        if (!schoolDocTreeConfig.getMngMigrateEnabled()) {
            return v1SchoolConfigService.editCommonQuestion(dto);
        }

        if (dto.getId() == null) {
            throw new IllegalArgumentException("id不能为空");
        }

        DocFlattenNode node = docNodeQueryService.getNode(new NodeGetReq()
                .setNodeId(dto.getId())
        );

        Optional<DocFlattenNode> article = Optional.empty();
        // 尝试更新关联信息
        if (dto.getTitleId() != null) {

            article = Optional.ofNullable(docNodeQueryService.getNode(new NodeGetReq()
                    .setNodeId(dto.getTitleId())
            ));

            if (!article.isPresent()) {
                throw new ServiceException("找不到关联文章");
            }
        }

        this.validateTryAddNewSanlianShowQuestion(node.getIsShow(),
                Optional.ofNullable(dto.getSanlianIsShow()).map(s -> s == 1).orElse(node.getIsShow()));

        try {
            SanlianDocNode sanlianUpdateResp = v2SchoolConfigServicePowerByDocTree.updateNode(
                    new SanlianNodeUpdateReq()
                            .setNodeId(node.getNodeId())
                            .setNodeName(article.map(r -> r.getNodeName()).orElse(null))
//                            .setDocType(Optional.ofNullable(dto.getType()).map(t -> String.valueOf(t)).orElse(null))
                            .setArticleId(article.map(a -> a.getNodeId()).orElse(null))
                            .setIsShow(Optional.ofNullable(dto.getSanlianIsShow()).map(s -> s == 1).orElse(null))
                            .setOrderNum(Optional.ofNullable(dto.getOrderNum()).orElse(null))
                            .setSanlianOrderNum(Optional.ofNullable(dto.getSanlianShowOrderNum()).orElse(null))
            );
        } catch (Exception e) {
            log.error("Fail to update sanlian common question show node info, req={}", dto, e);
        }

        return 1;

    }


    @Override
    public Integer editCommercialInfo(CreateCommercialInfoDto createCommercialInfoDto) {

        if (!schoolDocTreeConfig.getMngMigrateEnabled()) {
            return v1SchoolConfigService.editCommercialInfo(createCommercialInfoDto);
        }

        SanlianDocNode r = v2SchoolConfigServicePowerByDocTree.updateNode(
                new SanlianNodeUpdateReq()
                        .setNodeId(createCommercialInfoDto.getId())
                        .setTitle(createCommercialInfoDto.getTitle())
                        .setJumpUrl(createCommercialInfoDto.getUrl())
                        .setIsShow(Optional.ofNullable(createCommercialInfoDto.getSanlianIsShow())
                                .map(isShow -> isShow == 1).orElse(null))
                        .setOrderNum(createCommercialInfoDto.getSanlianShowOrderNum())
        );

        return 1;
    }


    /**
     * 该逻辑老代码可能是错误的 当前请求尝试添加新的三连展示问题
     */
    private void validateTryAddNewSanlianShowQuestion(boolean originShow, boolean targetShow) {

        if (!targetShow) {
            // 如果现在不需要展示，那么自然不会增多
            return;
        }

        if (originShow) {
            // 现在需要展示，原来也是展示，那么也不会增多
            return;
        }

        List<DocFlattenNode> sanlianShowingNodes = docNodeQueryService.listChildren(
                new NodeListReq()
                        .setNodeId(schoolDocTreeConfig.getCommonQuestionRootId())
                        .setDepth(1)
                        .setIsDeleted(false)
                        .setIsShow(true)
        );

        int sanlianShowingNodesCounts = sanlianShowingNodes.size();

        if (sanlianShowingNodesCounts >= schoolDocTreeConfig.getCommonQuestion4SanlianMaxShowCount()) {
            throw new IllegalArgumentException("三连展示问题已经达到最大展示数量");
        }

    }


    @Override
    public Integer deleteCommonQuestion(Long id) {

        if (!schoolDocTreeConfig.getMngMigrateEnabled()) {
            return v1SchoolConfigService.deleteCommonQuestion(id);
        }

//        DocFlattenNode node = docNodeQueryService.getNode(new NodeGetReq()
//                .setNodeId(id)
//        );

        v2SchoolConfigServicePowerByDocTree.deleteNode(
                new NodeDeleteReq().setNodeId(id)
        );

        return 1;
    }




    @Override
    public Integer deleteCommercialInfo(Long id) {

        if (!schoolDocTreeConfig.getMngMigrateEnabled()) {
            return v1SchoolConfigService.deleteCommercialInfo(id);
        }

        DocFlattenNode r = docNodeCommandService.deleteNode(new NodeDeleteReq().setNodeId(id));
        return 1;

    }

    /**
     * refactor {@link SchoolConfigService#createArticle(CreateArticleDto)}
     *
     * @param createArticleDto
     * @return
     * @throws ServiceException
     */
    @Override
    public Integer createArticle(CreateArticleDto createArticleDto) throws ServiceException {

        if (!schoolDocTreeConfig.getMngMigrateEnabled()) {
            return v1SchoolConfigService.createArticle(createArticleDto);
        }

        SanlianDocNode r = v2SchoolConfigServicePowerByDocTree.newNode(
                SchoolPojoConvertor.convertor.article2SanlianCreateReq(createArticleDto));

        this.newLatestInfoNode(r, createArticleDto.getSanlianShowOrderNum(), createArticleDto.getSanlianIsShow());

        return 1;
    }


    private void newLatestInfoNode(SanlianDocNode linkNode, Integer sanlianShowOrderNum, Integer sanlianIsShow) {

        v2SchoolConfigServicePowerByDocTree.newNode(
                new SanlianNodeCreateReq()
                        .setTreeType(SanlianSchoolTreeType.latest_info)
                        .setOrderNum(sanlianShowOrderNum)
                        .setIsShow(Optional.ofNullable(sanlianIsShow)
                                .map(isShow -> isShow == 1)
                                // 三连首页的最新最新动态默认不展示
                                .orElse(false))
                        .setLinkNodeId(linkNode.getNodeId())
                        .setLinkType(linkNode.getTreeType())
        );
    }


    @Override
    public PageResult<SchoolArticleDto> articleList(Long parentId, Byte type) {

        if (!schoolDocTreeConfig.getMngMigrateEnabled()) {
            return v1SchoolConfigService.articleList(parentId, type);
        }

        v2SchoolConfigServicePowerByDocTree.pageChildrenForMng(
                new SanlianNodePageReq()
                        .setNodeId(parentId)
                        .setTreeType(SanlianSchoolTreeType.fromV1ByteType(type))
        );


        return null;
    }


    @Override
    public Integer editArticle(CreateArticleDto createArticleDto) throws ServiceException {
        if (!schoolDocTreeConfig.getMngMigrateEnabled()) {
            return v1SchoolConfigService.editArticle(createArticleDto);
        }

        log.warn("废弃接口, 降级为操作v1接口");
        return v1SchoolConfigService.editArticle(createArticleDto);


    }

    @Override
    public Integer deleteArticle(Long articleId) {

        if (!schoolDocTreeConfig.getMngMigrateEnabled()) {
            return v1SchoolConfigService.deleteArticle(articleId);
        }

        SanlianDocNode r = v2SchoolConfigServicePowerByDocTree.deleteNode(new NodeDeleteReq()
                .setNodeId(articleId));

        return 1;
    }

    @Override
    public List<CommonQuestionDto> getCommonQuestionListForSanlian(Integer size) {
        // 注意这个其实是B端接口
        if (!schoolDocTreeConfig.getMigrateEnabled()) {
            return v1SchoolConfigService.getCommonQuestionListForSanlian(size);
        }

        List<SanlianDocNode> allCommonQuestion = v2SchoolQueryServicePowerByDocTree.listChildren(
                new SanlianNodeListReq()
                        .setTreeType(SanlianSchoolTreeType.common_question)
                        .setIsShow(true)
                        .setIsDeleted(false)
                        .setSort(NodeSortType.priority_asc)

        );

        if (CollectionUtils.isEmpty(allCommonQuestion)) {
            return new ArrayList<>();
        }

        Map<Long, SanlianDocNode> linkArticles = v2SchoolQueryServicePowerByDocTree.batchGetNode(
                new SanlianNodeBatchGetReq()
                        .setNodeIds(
                                allCommonQuestion.stream().map(link -> link.getArticleId())
                                        .collect(Collectors.toList()))
                        .setIsShow(true)
                        .setIsDeleted(false)
        ).stream().collect(Collectors.toMap(node -> node.getNodeId(), node -> node));


        return allCommonQuestion.stream()
//                .sorted(new Comparator<SanlianDocNode>() {
//                    @Override
//                    public int compare(SanlianDocNode o1, SanlianDocNode o2) {
//                        return Optional.ofNullable(o1.getSanlianOrderNum()).orElse(o1.getOrderNum()) -
//                                Optional.ofNullable(o2.getSanlianOrderNum()).orElse(o1.getOrderNum());
//                    }
//
//                })
                .map(question -> {
                    SanlianDocNode linkNode = linkArticles.get(question.getArticleId());
                    if (linkNode == null) {
                        return null;
                    }
                    return SchoolPojoConvertor.convertor.linkNode2CommonQuestion(question, linkNode);
                })
                .filter(Objects::nonNull)
                .limit(size)
                .limit(schoolDocTreeConfig.getSanlianCommonQuestionMax())
                .collect(Collectors.toList());

    }

    @Override
    public List<SanlianLatestUpdateDto> getLatestUpdateListForSanlian() {

        // 注意这个其实是B端接口
        if (!schoolDocTreeConfig.getMigrateEnabled()) {
            return v1SchoolConfigService.getLatestUpdateListForSanlian();
        }

        List<SanlianDocNode> allLatestInfo = v2SchoolQueryServicePowerByDocTree.listChildren(
                new SanlianNodeListReq()
                        .setTreeType(SanlianSchoolTreeType.latest_info)
                        .setIsShow(true)
                        .setIsDeleted(false)
                        .setSort(NodeSortType.priority_asc)
        );

        if (CollectionUtils.isEmpty(allLatestInfo)) {
            return new ArrayList<>();
        }

        // TODO 注意可能有名称无法联动更新的问题。需要注意下
        Map<Long, SanlianDocNode> links = v2SchoolQueryServicePowerByDocTree.batchGetNode(
                new SanlianNodeBatchGetReq()
                        .setNodeIds(
                                allLatestInfo.stream().map(link -> link.getLinkNodeId()).collect(Collectors.toList()))
                        .setIsShow(true)
                        .setIsDeleted(false)
        ).stream().collect(Collectors.toMap(node -> node.getNodeId(), node -> node));

        return allLatestInfo.stream()
                .map(latestUpdate -> {

                    SanlianDocNode link = links.get(latestUpdate.getLinkNodeId());

                    if (link == null) {
                        return null;
                    }
                    SanlianLatestUpdateDto r = new SanlianLatestUpdateDto()
                            .setType((int) SanlianSchoolTreeType.toV1ByteType(link.getTreeType()))
                            .setTitle(link.getNodeName());
                    if (!StringUtils.isEmpty(link.getJumpUrl())) {
                        r.setJumpUrl(link.getJumpUrl());
                    } else {
                        r.setArticleId(link.getNodeId().toString());
                    }
                    return r;
                })
                .filter(Objects::nonNull)
                .limit(schoolDocTreeConfig.getSanlianLatestUpdateMax())
                .collect(Collectors.toList());






    }
}
