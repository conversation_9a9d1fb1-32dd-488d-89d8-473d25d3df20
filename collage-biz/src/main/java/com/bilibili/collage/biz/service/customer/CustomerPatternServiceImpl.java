package com.bilibili.collage.biz.service.customer;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.collage.api.dto.*;
import com.bilibili.collage.api.service.ICollageSizeService;
import com.bilibili.collage.api.service.IPatternService;
import com.bilibili.collage.api.service.IPatternTagService;
import com.bilibili.collage.api.service.customer.ICustomerPatternService;
import com.bilibili.mgk.platform.common.CollageLayerEnum;
import com.bilibili.mgk.platform.common.CollagePatternStatusEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/12/11
 **/
@Slf4j
@Service
public class CustomerPatternServiceImpl implements ICustomerPatternService {

    @Autowired
    private IPatternService patternService;

    @Autowired
    private ICollageSizeService collageSizeService;

    /**
     * 返回素材个数与素材列表的map
     * 素材个数：模版下的图层中的主素材图层数
     * @return
     */
    @Override
    public Map<Integer, List<PatternDto>> getPatternListInMateCount() {

        // 获取所有模版列表
        List<PatternDto> patternList = patternService.queryPattern(QueryCollagePatternDto.builder()
                .status(CollagePatternStatusEnum.USING.getCode())
                .build());
        if (CollectionUtils.isEmpty(patternList)) {
            return Collections.emptyMap();
        }
        // 模版id与模版的map
        Map<Integer, PatternDto> patternMap = patternList.stream().collect(Collectors.toMap(PatternDto::getId, Function.identity()));

        // 模版id与素材数量的map
        Map<Integer, Long> patternId2MateCount = patternList.stream()
                .flatMap(item -> item.getLayerDtos().stream())
                .filter(layer -> layer.getCategory().getCode().equals(CollageLayerEnum.MATE_IMG.getCode()))
                .collect(Collectors.groupingBy(LayerDto::getPatternId, Collectors.counting()));

        // 结果: 素材数量与模版列表的map
        // 遍历patternId2MateCount，将相同数量的模版合并
        Map<Integer, List<PatternDto>> result = Maps.newHashMap();
        patternId2MateCount.keySet().forEach(patternId -> {
            // 数量
            Integer count = patternId2MateCount.get(patternId).intValue();
            // 此数量下的模版列表
            List<PatternDto> patterns = result.get(count);
            if (CollectionUtils.isEmpty(patterns)) {
                patterns = Lists.newArrayList();
                patterns.add(patternMap.get(patternId));
                result.put(count, patterns);
            } else {
                patterns.add(patternMap.get(patternId));
            }
        });
        return result;
    }

    /**
     * 返回素材个数列表
     * @return
     */
    @Override
    public List<Integer> getPatternCountList() {
        Map<Integer, List<PatternDto>> patternListInMateCount = this.getPatternListInMateCount();
        if (patternListInMateCount.isEmpty()) {
            return Collections.emptyList();
        }
        return patternListInMateCount.keySet().stream().collect(Collectors.toList());
    }

    /**
     * 根据素材个数返回尺寸
     * @param mateCount
     * @return
     */
    @Override
    public List<CollageSizeDto> getSizeListByMateCount(Integer mateCount) {

        Map<Integer, List<PatternDto>> patternListInMateCount = this.getPatternListInMateCount();
        if (patternListInMateCount.isEmpty()) {
            return Collections.emptyList();
        }
        List<PatternDto> patternList = patternListInMateCount.get(mateCount);
        if (CollectionUtils.isEmpty(patternList)) {
            return Collections.emptyList();
        }
        List<Integer> sizeIds = patternList.stream().map(PatternDto::getCollageSizeId).distinct().collect(Collectors.toList());
        return collageSizeService.getCollageSizeByIds(sizeIds);
    }

    /**
     *根据(素材数，主标，副标，尺寸，行业)匹配模版集合
     *  1.按模版id的纬度聚合出所有图层数据
     *  2.再根据素材数，主标，副标的值的情况筛选出符合的图层
     *  3.最后得到筛选后的模版id
     *  4.根据模版id，尺寸，行业 查询出匹配的模版
     *  5.返回匹配结果（分页）
     * @param query
     * @return
     */
    @Override
    public PageResult<PatternDto> matchingPattern(MatchingPatternDto query) {

        Assert.notNull(query.getPage(), "分页页号不能为空");
        Assert.notNull(query.getSize(), "分页页长不能为空");

        // 1.按模版id的纬度聚合出所有图层数据
        List<LayerDto> layerList = patternService.queryLayerList(QueryCollageLayerDto.builder()
                .isDeleted(IsDeleted.VALID.getCode())
                .build());
        Map<Integer, List<LayerDto>> patternId2layerMap = layerList.stream().collect(Collectors.groupingBy(LayerDto::getPatternId));

        // 2.再根据素材数，主标，副标的值的情况筛选出符合的图层
        // 3.最后得到筛选后的模版id
        List<Integer> filterPatternIds = Lists.newArrayList();
        patternId2layerMap.keySet().forEach(patternId -> {
            boolean matched = true;
            List<LayerDto> layers = patternId2layerMap.get(patternId);
            // 素材数
            Long mateCount = layers.stream().filter(layer -> layer.getCategory().getCode().equals(CollageLayerEnum.MATE_IMG.getCode()))
                    .collect(Collectors.counting());
            if (Utils.isPositive(query.getMateCount()) && !query.getMateCount().equals(mateCount.intValue())) {
                matched = false;
            }

            // 主标
            Long mainTitleCount = layers.stream().filter(layer -> layer.getCategory().getCode().equals(CollageLayerEnum.MAIN_TITLE.getCode()))
                    .collect(Collectors.counting());
            if (!StringUtils.isEmpty(query.getMainTitle()) && !Utils.isPositive(mainTitleCount)) {
                matched = false;
            }

            // 副标
            Long subTitleCount = layers.stream().filter(layer -> layer.getCategory().getCode().equals(CollageLayerEnum.SUB_TITLE.getCode()))
                    .collect(Collectors.counting());
            if (!StringUtils.isEmpty(query.getSubTitle()) && !Utils.isPositive(subTitleCount)) {
                matched = false;
            }
            if (matched) {
                filterPatternIds.add(patternId);
            }
        });

        if (CollectionUtils.isEmpty(filterPatternIds)) {
            return PageResult.emptyPageResult();
        }

        // 4.根据模版id，尺寸，行业 查询出匹配的模版
        PageResult<PatternDto> result = patternService.queryPatternByPage(QueryCollagePatternDto.builder()
                .ids(filterPatternIds)
                .collageSizeId(query.getAdSizeId())
                .industryId(query.getIndustryId())
                .tagId(query.getTagId())
                .status(CollagePatternStatusEnum.USING.getCode())
                .pageInfo(Page.valueOf(query.getPage(), query.getSize()))
                .build());

        if (CollectionUtils.isEmpty(result.getRecords())) {
            return result;
        }
        // 获取尺寸信息
        List<Integer> sizeIds = result.getRecords().stream().map(PatternDto::getCollageSizeId).distinct().collect(Collectors.toList());
        List<CollageSizeDto> sizeList = collageSizeService.getCollageSizeByIds(sizeIds);
        Map<Integer, CollageSizeDto> sizeMap = sizeList.stream().collect(Collectors.toMap(CollageSizeDto::getId, Function.identity()));

        result.getRecords().forEach(pattern -> {
            CollageSizeDto sizeDto = sizeMap.getOrDefault(pattern.getCollageSizeId(), CollageSizeDto.builder().build());
            pattern.setWidth(sizeDto.getWidth());
            pattern.setHeight(sizeDto.getHeight());
            pattern.setSizeDesc(sizeDto.getDesc());
        });

        /**
         * 根据tagIds获取模版list
         * 判断相同标记的模版的个数是否大于1
         * 同标记模版大于1的，才给tagId赋值
         */
        List<Integer> tagIds = result.getRecords().stream().map(PatternDto::getTagId).filter(Utils::isPositive).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tagIds)) {
            return result;
        }
        // 获取标签id->模版列表的map
        List<PatternDto> patternListInTag = patternService.queryPatternBaseInfo(QueryCollagePatternDto.builder().tagIds(tagIds).build());
        Map<Integer, List<PatternDto>> patternMapInTag = patternListInTag.stream().collect(Collectors.groupingBy(PatternDto::getTagId));

        result.getRecords().forEach(pattern -> {
            Integer tagId = pattern.getTagId();
            List<PatternDto> curPatterns = patternMapInTag.getOrDefault(tagId, Collections.emptyList());
            pattern.setTagId(curPatterns.size() > 1 ? tagId : null);
        });

        return result;
    }
}
