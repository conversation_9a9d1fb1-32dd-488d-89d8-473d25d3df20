package com.bilibili.collage.biz.service.school.doctree;


import com.bilibili.collage.biz.config.SchoolDocTreeConfig;
import com.bilibili.collage.biz.service.school.SchoolPojoConvertor;
import com.bilibili.collage.biz.service.school.doctree.model.SanlianDocNode;
import com.bilibili.collage.biz.service.school.doctree.model.SanlianDocTreeNode;
import com.bilibili.collage.biz.service.school.doctree.model.SanlianNodeSearchResult;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodeBatchGetReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodeGetReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodeListReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodePageReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianNodeSearchReq;
import com.bilibili.collage.biz.service.school.doctree.vo.SanlianSubTreeReq;
import com.biz.common.doc.tree.common.Pagination;
import com.biz.common.doc.tree.model.DocFlattenNode;
import com.biz.common.doc.tree.model.DocTreeNode;
import com.biz.common.doc.tree.service.DocNodeFavoriteService;
import com.biz.common.doc.tree.service.DocNodeQueryService;
import com.biz.common.doc.tree.service.vo.NodeBatchGetReq;
import com.biz.common.doc.tree.service.vo.NodeGetReq;
import com.biz.common.doc.tree.service.vo.NodeListRecursiveReq;
import com.biz.common.doc.tree.service.vo.NodeListReq;
import com.biz.common.doc.tree.service.vo.NodeListReq.TargetDepthStrategy;
import com.biz.common.doc.tree.service.vo.NodePageReq;
import com.biz.common.doc.tree.service.vo.NodeSearchReq;
import com.biz.common.doc.tree.service.vo.NodeSearchResult;
import com.biz.common.doc.tree.service.vo.NodeSortType;
import com.google.common.collect.Lists;
import io.vavr.control.Try;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/12/9
 */
@Slf4j
@Service
public class SanlianSchoolDocTreeQueryServiceImpl implements SanlianSchoolDocTreeQueryService {

    @Resource
    private DocNodeQueryService docNodeQueryService;


    @Resource
    private DocNodeFavoriteService docNodeFavoriteService;


    @Resource
    private SchoolDocTreeConfig docTreeConfig;


    @Override
    public SanlianDocTreeNode subTree(SanlianSubTreeReq req) {

        req.validate();

        DocTreeNode tree = docNodeQueryService.subTree(
                new NodeListRecursiveReq()
                        .setNodeId(docTreeConfig.findRootIdBySchoolTreeType(req.getTreeType()))
                        .setRetrieveDoc(req.getRetrieveDoc())
                        .setRetrieveAncestors(true)
                        .setSort(NodeSortType.priority_asc)
                        .setIsShow(req.getIsShow())
                        .setIsDeleted(req.getIsDeleted())
                        .setAccountId(req.getAccountId())
                        .setRetrieveFavorite(true)
                        .setDepth(req.getDepth())
        );

        SanlianDocTreeNode r = SchoolPojoConvertor.convertor.docTree2SchoolTree(tree);
        r.setTreeTypeRecursive(docTreeConfig.findTreeTypeByRootId(tree.findRootIdIfZeroUseNodeId()));
        return r;

    }


    @Override
    public List<SanlianDocNode> listChildren(SanlianNodeListReq req) {

        req.validate();

        List<DocFlattenNode> r = docNodeQueryService.listChildren(
                new NodeListReq()
                        .setNodeId(Optional.ofNullable(req.getNodeId()).orElse(
                                docTreeConfig.findRootIdBySchoolTreeType(req.getTreeType())))
                        .setIsDeleted(req.getIsDeleted())
                        .setIsShow(req.getIsShow())
                        .setAccountId(req.getAccountId())
                        .setRetrieveFavorite(true)
                        .setRetrieveDoc(false)
                        .setRetrieveAncestors(true)
                        // 深度不限，但是必须要是文档节点
                        .setDepthStrategy(Try.of(() -> {
                            return TargetDepthStrategy.valueOf(req.getDepthStrategy());
                        }).getOrElse(TargetDepthStrategy.depth_lte))
                        .setDepth(req.getDepth())
                        .setNodeType(req.fetchNodeTypesAsStringList())
                        .setSort(Optional.ofNullable(req.getSort()).orElse(NodeSortType.priority_asc))
        );

        return r.stream()
                .map(node -> {
                    return SchoolPojoConvertor.convertor.docNode2SchoolDocNode(node)
                            .setTreeType(docTreeConfig.findTreeTypeByRootId(node.findRootIdIfZeroUseNodeId()));
                })
                .collect(Collectors.toList());
    }


    @Override
    public Pagination<List<SanlianDocNode>> pageChildren(
            SanlianNodePageReq req

    ) {

        req.validate();
        Pagination<List<DocFlattenNode>> r = docNodeQueryService.pageChildren(
                (NodePageReq) new NodePageReq()
                        .setPn(req.getPn())
                        .setPs(req.getPs())
                        .setNodeId(Optional.ofNullable(req.getNodeId()).orElse(
                                docTreeConfig.findRootIdBySchoolTreeType(req.getTreeType())))
                        .setIsDeleted(req.getIsDeleted())
                        .setIsShow(req.getIsShow())
                        .setAccountId(req.getAccountId())
                        .setRetrieveFavorite(true)
                        .setDepthStrategy(Try.of(() -> {
                            return TargetDepthStrategy.valueOf(req.getDepthStrategy());
                        }).getOrElse(TargetDepthStrategy.depth_lte))
                        .setDepth(req.getDepth())
                        .setRetrieveDoc(false)
                        .setRetrieveAncestors(true)
                        .setNodeType(req.fetchNodeTypesAsStringList())
                        .setSort(Optional.ofNullable(req.getSort()).orElse(NodeSortType.priority_asc))


        );

        return r.map(list -> {

            return list.stream()
                    .map(node -> {
                        return SchoolPojoConvertor.convertor.docNode2SchoolDocNode(node)
                                .setTreeType(docTreeConfig.findTreeTypeByRootId(node.findRootIdIfZeroUseNodeId()));
                    })
                    .collect(Collectors.toList());
        });
    }


    /**
     * @return
     */
    @Override
    public SanlianDocNode getNode(SanlianNodeGetReq req) {
        DocFlattenNode node = docNodeQueryService.getNode(
                new NodeGetReq()
                        .setRootId(docTreeConfig.fetchAllRootIds())
                        .setNodeId(req.getNodeId())
                        .setRetrieveDoc(true)
                        .setRetrieveAncestors(false)
                        .setAccountId(req.getAccountId())
                        .setRetrieveFavorite(true)
                        .setIsDeleted(req.getIsDeleted())
                        .setIsShow(req.getIsShow())
        );

        return SchoolPojoConvertor.convertor.docNode2SchoolDocNode(node)
                .setTreeType(docTreeConfig.findTreeTypeByRootId(node.findRootIdIfZeroUseNodeId()));
    }


    @Override
    public List<SanlianDocNode> batchGetNode(SanlianNodeBatchGetReq req) {

        List<DocFlattenNode> nodes = docNodeQueryService.batchGetNode(
                new NodeBatchGetReq()
                        .setRootId(docTreeConfig.fetchAllRootIds())
                        .setNodeIds(req.getNodeIds())
                        .setRetrieveDoc(true)
                        .setRetrieveAncestors(true)
                        .setIsDeleted(req.getIsDeleted())
                        .setIsShow(req.getIsShow())
                        .setAccountId(req.getAccountId())
                        .setRetrieveFavorite(true)
        );

        return nodes.stream()
                .map(node -> {
                    return SchoolPojoConvertor.convertor.docNode2SchoolDocNode(node)
                            .setTreeType(docTreeConfig.findTreeTypeByRootId(node.findRootIdIfZeroUseNodeId()));
                })
                .collect(Collectors.toList());
    }


    @Override
    public Pagination<List<SanlianNodeSearchResult>> searchDoc(SanlianNodeSearchReq req) {

        return doSearchDoc(req, null);

    }


    @Override
    public Pagination<List<SanlianNodeSearchResult>> searchDocTitle(SanlianNodeSearchReq req) {

        return doSearchDoc(req, Lists.newArrayList("docTitle"));

    }


    private Pagination<List<SanlianNodeSearchResult>> doSearchDoc(SanlianNodeSearchReq req,
            List<String> keywordMatchProjections
    ) {

        // TODO 搜索node范围保护
        List<Long> rootIds = Optional.ofNullable(req.getTreeType())
                .orElse(new ArrayList<>())
                .stream()
                .map(docTreeConfig::findRootIdBySchoolTreeType)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(rootIds)) {
            // 至少限定以下范围
            rootIds = docTreeConfig.fetchDocRootIds();
        }

        Pagination<List<NodeSearchResult>> result = docNodeQueryService.searchDoc(
                new NodeSearchReq()
                        .setRootId(rootIds)
                        .setKeyword(req.getKeyword())
                        .setHighlight(true)
                        .setPn(req.getPn())
                        .setPs(req.getPs())
                        .setIsDeleted(false)
                        .setIsShow(true)
                        .setAccountId(req.getAccountId())
                        .setRetrieveFavorite(true)
                        .setKeywordMatchProjections(keywordMatchProjections)
                        .setSort(NodeSortType.priority_asc)
        );

        return result.map(list -> {
            return list.stream()
                    .map(node -> {
                        SanlianNodeSearchResult r = SchoolPojoConvertor.convertor.docNode2SchoolNodeSearchResult(node);
                        r.setTreeType(docTreeConfig.findTreeTypeByRootId(node.findRootIdIfZeroUseNodeId()));
                        return r;
                    })
                    .collect(Collectors.toList());
        });

    }


}
