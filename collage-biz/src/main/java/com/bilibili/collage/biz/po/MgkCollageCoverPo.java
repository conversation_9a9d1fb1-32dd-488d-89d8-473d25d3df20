package com.bilibili.collage.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MgkCollageCoverPo implements Serializable {
    /**
     * 自增Id
     */
    private Integer id;

    /**
     * 媒体雪花Id
     */
    private Long coverId;

    /**
     * 封面类型 1-作品封面 2-媒体封面
     */
    private Integer objType;

    /**
     * 作品（媒体）的Id
     */
    private Integer objId;

    /**
     * 封面宽度
     */
    private Integer width;

    /**
     * 封面高度
     */
    private Integer height;

    /**
     * 宽高比（扩大10000）
     */
    private Integer ratio;

    /**
     * 封面文件大小
     */
    private Integer coverSize;

    /**
     * 封面图片的md5
     */
    private String coverMd5;

    /**
     * 封面图片的url
     */
    private String coverUrl;

    /**
     * 是否删除 0-正常 1-删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}