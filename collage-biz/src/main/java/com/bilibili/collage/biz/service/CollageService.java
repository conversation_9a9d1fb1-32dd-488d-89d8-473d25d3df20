package com.bilibili.collage.biz.service;

import com.bilibili.adp.bfs.dto.BfsUploadResult;
import com.bilibili.adp.bfs.service.IBfsService;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.collage.api.dto.PicDownloadInfoBo;
import com.bilibili.collage.api.service.ICollageService;
import com.bilibili.mgk.platform.common.MgkConstants;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static java.lang.Thread.currentThread;
import static java.lang.Thread.sleep;

/**
 * <AUTHOR>
 * @date 2018/11/15
 * 拼贴艺术
 **/
@Service
@Slf4j
public class CollageService implements ICollageService {

    @Autowired
    private IBfsService bfsService;

    @Autowired
    private KsService ksService;

    @Autowired
    private OkHttpClient okHttpClient;

    @Override
    public String upload(File file) throws ServiceException {
        Assert.notNull(file, "文件不允许为空");
        String catePath = getCategoryPathByFileName(file.getName());
        BfsUploadResult bfsUploadResult = bfsService.upload(catePath, file);
        return bfsUploadResult.getUrl();
    }

    @Override
    public List<String> upload(List<File> files) {

        if (CollectionUtils.isEmpty(files)) {
            return Collections.emptyList();
        }
        return files.stream().map(file -> {
            try {
                sleep(50);
                return upload(file);
            } catch (ServiceException | InterruptedException e) {
                log.error("upload fail: ", e);
                Thread.currentThread().interrupt();
                throw new RuntimeException(e);
            }
        }).collect(Collectors.toList());
    }

    @Override
    public String upload(String fileName, byte[] data) throws ServiceException {
        String catePath = getCategoryPathByFileName(fileName);
        BfsUploadResult bfsUploadResult = bfsService.upload(catePath, fileName, data);
        return bfsUploadResult.getUrl();
    }

    @Override
    @SneakyThrows
    public String uploadWebp(File file) {
        String originUrl = upload(file);
        return downloadWebpAndUploadFile2Bfs(originUrl).getUrl();
    }

    @SneakyThrows
    private BfsUploadResult downloadWebpAndUploadFile2Bfs(String originUrl) {
        String catePath = getCategoryPathByFileName(originUrl);
        String webpUrl = originUrl.replace("https", "http") + "@.webp";
        log.info("downloadWebpAndUploadFile2Bfs, origin file url:{}, webp file url:{}", originUrl, webpUrl);

        PicDownloadInfoBo picDownloadInfoBo = downloadPicInfo(webpUrl);
        Assert.notNull(picDownloadInfoBo, "同步webp图片失败");
        checkDownLoad(picDownloadInfoBo);
        String uploadUrl = org.apache.commons.lang.StringUtils.trim(originUrl.substring(originUrl.lastIndexOf(".") + 1)).toLowerCase() + ".webp";

        BfsUploadResult uploadResult = bfsService.upload(originUrl, uploadUrl, picDownloadInfoBo.getBytes());
        PicDownloadInfoBo downloadUploadPicInfo = downloadPicInfo(uploadUrl);
        Assert.isTrue(Objects.nonNull(downloadUploadPicInfo)
                        && Objects.equals(downloadUploadPicInfo.getMd5(), uploadResult.getMd5()),
                "webp上传bfs后文件内容不一致,请重试");
        return uploadResult;
    }

    private void checkDownLoad(PicDownloadInfoBo picDownloadInfo) {
        String fileContentMd5 = Base64.encodeBase64String(DigestUtils.md5(picDownloadInfo.getBytes()));
        log.info("checkDownLoad fileContentMd5:{}", fileContentMd5);
        Assert.isTrue(Objects.equals(fileContentMd5, picDownloadInfo.getContentMd5()), "webp转换流程，webp下载不完整，请重试");
    }

    @SneakyThrows
    private PicDownloadInfoBo downloadPicInfo(String webpUrl) {
        Request request = new Request.Builder()
                .header(HttpHeaders.CONNECTION, "close")
                .url(webpUrl)
                .get()
                .build();
        try (Response response = okHttpClient.newCall(request).execute();) {
            byte[] bytes = response.body().bytes();
            String contentMd5 = response.header("content-md5");
            PicDownloadInfoBo picDownloadInfo = new PicDownloadInfoBo();
            picDownloadInfo.setBytes(bytes);
            picDownloadInfo.setContentMd5(contentMd5);
            picDownloadInfo.setMd5(DigestUtils.md5Hex(bytes));
            return picDownloadInfo;
        } catch (Exception e) {
            log.error("webp转换流程，下载图片{}发生异常:{}", webpUrl, ExceptionUtils.getStackTrace(e));
            return null;
        }
    }

    @Override
    public String uploadToK3s(File file) throws ServiceException {

        Assert.notNull(file, "文件不允许为空");
        return ksService.upload(file);
    }

    @Override
    public List<String> uploadToK3s(List<String> url) throws ServiceException {


        return null;
    }

    @Override
    public String replaceHttpsProtocol(String url) {
        if (StringUtils.isEmpty(url)) {
            return "";
        }
        return url.replaceAll("^https://", "http://");
    }

    private String getCategoryPathByFileName(String fileName) {
        Assert.hasText(fileName, "文件名称不可为空");
        String cate = StringUtils.trimWhitespace(fileName.substring(fileName.lastIndexOf(".") + 1)).toLowerCase();
        return String.format(MgkConstants.BFS_COLLAGE_DIR_SUFFIX, cate);
    }

}
